<?php

namespace Tests\Feature;

use App\Enums\WalletDeductionStatus;
use App\Models\Currency;
use App\Models\User;
use App\Models\WalletDeductionRecord;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class InternalWalletDeductionApiTest extends TestCase
{
    use RefreshDatabase;

    public function test_can_get_deduction_records_list()
    {
        // 创建测试数据
        $user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password',
        ]);
        
        $currency = Currency::create([
            'name' => 'US Dollar',
            'code' => 'USD',
            'symbol' => '$',
        ]);

        // 创建补差记录
        WalletDeductionRecord::create([
            'user_id' => $user->id,
            'wallet_transaction_id' => 1,
            'order_id' => 1,
            'artist_user_id' => $user->id,
            'artist_withdraw_account_id' => 1,
            'amount' => 1000,
            'currency_id' => $currency->id,
            'status' => WalletDeductionStatus::Pending,
        ]);

        // 调用API (使用内部API token)
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . config('auth.internal_api_token', 'test-token')
        ])->getJson('/api/internal/wallet-deduction/records');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'data' => [
                        '*' => [
                            'id',
                            'user_id',
                            'amount',
                            'status',
                            'created_at',
                        ]
                    ],
                    'pagination'
                ]);
    }

    public function test_can_get_statistics()
    {
        // 创建测试数据
        $user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password',
        ]);
        
        $currency = Currency::create([
            'name' => 'US Dollar',
            'code' => 'USD',
            'symbol' => '$',
        ]);

        // 创建不同状态的记录
        WalletDeductionRecord::create([
            'user_id' => $user->id,
            'wallet_transaction_id' => 1,
            'order_id' => 1,
            'artist_user_id' => $user->id,
            'artist_withdraw_account_id' => 1,
            'amount' => 1000,
            'currency_id' => $currency->id,
            'status' => WalletDeductionStatus::Pending,
        ]);

        WalletDeductionRecord::create([
            'user_id' => $user->id,
            'wallet_transaction_id' => 2,
            'order_id' => 2,
            'artist_user_id' => $user->id,
            'artist_withdraw_account_id' => 1,
            'amount' => 2000,
            'currency_id' => $currency->id,
            'status' => WalletDeductionStatus::Completed,
        ]);

        // 调用API (使用内部API token)
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . config('auth.internal_api_token', 'test-token')
        ])->getJson('/api/internal/wallet-deduction/statistics');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'data' => [
                        'total_records',
                        'pending_count',
                        'processing_count',
                        'completed_count',
                        'failed_count',
                        'total_amount',
                        'completed_amount',
                        'pending_amount',
                    ]
                ]);

        $data = $response->json('data');
        $this->assertEquals(2, $data['total_records']);
        $this->assertEquals(1, $data['pending_count']);
        $this->assertEquals(1, $data['completed_count']);
        $this->assertEquals(3000, $data['total_amount']);
        $this->assertEquals(2000, $data['completed_amount']);
        $this->assertEquals(1000, $data['pending_amount']);
    }
}
