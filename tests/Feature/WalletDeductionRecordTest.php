<?php

namespace Tests\Feature;

use App\Enums\WalletDeductionStatus;
use App\Models\Currency;
use App\Models\User;
use App\Models\WalletDeductionRecord;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class WalletDeductionRecordTest extends TestCase
{
    use RefreshDatabase;

    public function test_can_create_wallet_deduction_record()
    {
        // 创建测试数据
        $user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password',
        ]);

        $artistUser = User::create([
            'name' => 'Artist User',
            'email' => '<EMAIL>',
            'password' => 'password',
        ]);

        $currency = Currency::create([
            'name' => 'US Dollar',
            'code' => 'USD',
            'symbol' => '$',
        ]);

        // 直接创建补差记录，不依赖其他复杂的关联
        $deductionRecord = WalletDeductionRecord::create([
            'user_id' => $user->id,
            'wallet_transaction_id' => 1, // 假设的ID
            'order_id' => 1, // 假设的ID
            'artist_user_id' => $artistUser->id,
            'artist_withdraw_account_id' => 1, // 假设的ID
            'amount' => 1000,
            'currency_id' => $currency->id,
            'status' => WalletDeductionStatus::Pending,
            'metadata' => [
                'work_task_id' => 1,
                'payment_type' => 'stage_pay',
            ],
        ]);

        // 验证记录创建成功
        $this->assertDatabaseHas('wallet_deduction_records', [
            'id' => $deductionRecord->id,
            'user_id' => $user->id,
            'status' => 'pending',
            'amount' => 1000,
        ]);

        // 验证基本属性
        $this->assertEquals($user->id, $deductionRecord->user_id);
        $this->assertEquals($artistUser->id, $deductionRecord->artist_user_id);
        $this->assertEquals(1000, $deductionRecord->amount);
        $this->assertEquals(WalletDeductionStatus::Pending, $deductionRecord->status);
        $this->assertIsArray($deductionRecord->metadata);
    }

    public function test_wallet_deduction_record_scopes()
    {
        $user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password',
        ]);

        $currency = Currency::create([
            'name' => 'US Dollar',
            'code' => 'USD',
            'symbol' => '$',
        ]);

        // 创建不同状态的记录
        WalletDeductionRecord::create([
            'user_id' => $user->id,
            'wallet_transaction_id' => 1,
            'order_id' => 1,
            'artist_user_id' => $user->id,
            'artist_withdraw_account_id' => 1,
            'amount' => 1000,
            'currency_id' => $currency->id,
            'status' => WalletDeductionStatus::Pending,
        ]);

        WalletDeductionRecord::create([
            'user_id' => $user->id,
            'wallet_transaction_id' => 2,
            'order_id' => 2,
            'artist_user_id' => $user->id,
            'artist_withdraw_account_id' => 1,
            'amount' => 2000,
            'currency_id' => $currency->id,
            'status' => WalletDeductionStatus::Completed,
        ]);

        // 测试作用域
        $this->assertEquals(1, WalletDeductionRecord::pending()->count());
        $this->assertEquals(1, WalletDeductionRecord::completed()->count());
        $this->assertEquals(0, WalletDeductionRecord::processing()->count());
        $this->assertEquals(0, WalletDeductionRecord::failed()->count());
    }
}
