<?php

namespace Tests\Unit\Service;

use App\Models\Project;
use App\Models\Service;
use App\Models\Stage;
use App\Service\PaymentService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PaymentServiceTest extends TestCase
{
    use RefreshDatabase;

    /**
     * A basic unit test example.
     */
    public function test_service(): void
    {
        $instance = new PaymentService();

        $stages = Stage::factory()->count(4)->create();
        $service = Service::factory()->create();

        $percents = [10, 30, 50, 100];
        foreach ($stages as $key => $stage) {
            $service->stages()->attach($stage, ['percent' => $percents[$key]]);
        }

        $total_price = 100;
        $ret = $instance->calcStageAmounts($service->stages, $total_price);

        $assertAmounts = [10, 20, 20, 50];

        $this->assertCount(4, $ret);

        $amountTotal = collect($ret)->sum('amount');
        $this->assertEquals($amountTotal, $total_price);

        foreach ($ret as $key => $stage) {
            $this->assertEquals($assertAmounts[$key], $stage->amount);
        }

    }

    public function test_project(): void
    {
        $instance = new PaymentService();

        $stages = Stage::factory()->count(4)->create();
        $project = Project::factory()->create();

        $percents = [10, 30, 50, 100];
        foreach ($stages as $key => $stage) {
            $project->stages()->attach($stage, ['percent' => $percents[$key]]);
        }

        $total_price = 100;
        $ret = $instance->calcStageAmounts($project->stages, $total_price);

        $assertAmounts = [10, 20, 20, 50];

        $this->assertCount(4, $ret);

        $amountTotal = collect($ret)->sum('amount');
        $this->assertEquals($amountTotal, $total_price);

        foreach ($ret as $key => $stage) {
            $this->assertEquals($assertAmounts[$key], $stage->amount);
        }

    }
}
