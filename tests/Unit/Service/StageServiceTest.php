<?php

namespace Tests\Unit\Service;

use App\Models\Stage;
use App\Service\StageService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class StageServiceTest extends TestCase
{
    use RefreshDatabase;

    public function test_stage(): void
    {

        $endStage = Stage::factory()->create([
            'is_end_stage' => 1,
        ]);

        $stages = Stage::factory()->count(3)->create();

        $data = [
            [
                'id' => $stages[0]->id,
                'percent' => 50,
            ],
            [
                'id' => $stages[1]->id,
                'percent' => 20,
            ],
            [
                'id' => $stages[2]->id,
                'percent' => 60,
            ],
            [
                'id' => $stages[2]->id,
                'percent' => 100,
            ],
        ];
        $ret = StageService::convertToSyncStages($data, 'id');
        $this->assertCount(4, $ret);
        $this->assertEquals([
            $stages[1]->id => [
                'id' => $stages[1]->id,
                'percent' => 20,
            ],
            $stages[0]->id => [
                'id' => $stages[0]->id,
                'percent' => 50,
            ],
            $stages[2]->id => [
                'id' => $stages[2]->id,
                'percent' => 60,
            ],
            $endStage->id => [
                'id' => $endStage->id,
                'percent' => 100,
            ],
        ], $ret);
    }
}
