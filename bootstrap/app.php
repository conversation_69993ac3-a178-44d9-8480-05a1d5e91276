<?php

use App\Http\Middleware\ForceXmlHttpRequest;
use App\Http\Middleware\RequestLogger;
use App\Http\Middleware\TraceMiddleWare;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: [
            __DIR__.'/../routes/api.php',
            __DIR__.'/../routes/api/userApi.php',
            __DIR__.'/../routes/api/artistApi.php',
            __DIR__.'/../routes/api/contentApi.php',
            __DIR__.'/../routes/api/adminApi.php',
            __DIR__.'/../routes/api/internalApi.php',
        ],
        commands: __DIR__.'/../routes/console.php',
        channels: __DIR__.'/../routes/channels.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->validateCsrfTokens(except: [
        ]);

        $middleware->validateCsrfTokens(except: [
            'api/*',
            'stripe/*',
        ]);
        if (env('APP_ENV') === 'test') {
            $middleware->api(
                append: [
                    RequestLogger::class,
                ],
            );
        }
        $middleware->api(
            prepend: [
                ForceXmlHttpRequest::class,
                TraceMiddleWare::class,
            ],
            remove: [
            ]
        );
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
