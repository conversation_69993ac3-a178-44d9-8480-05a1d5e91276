{"apps": [{"name": "[pipipen-pre] [queue-worker] [translate]", "script": "artisan", "args": "queue:work --sleep=3 --tries=3 --queue=translate", "exec_interpreter": "php", "exec_mode": "fork", "watch": false, "autorestart": true, "max_restarts": 10, "restart_delay": 5000, "cwd": "/home/<USER>/codes/pre/pipipen-api"}, {"name": "[pipipen-pre] [queue-worker] [compress-image]", "script": "artisan", "args": "queue:work --sleep=3 --tries=3 --queue=compress_image", "exec_interpreter": "php", "exec_mode": "fork", "watch": false, "autorestart": true, "max_restarts": 10, "restart_delay": 5000, "cwd": "/home/<USER>/codes/pre/pipipen-api"}, {"name": "[pipipen-pre] [queue-worker] [default]", "script": "artisan", "args": "queue:work --sleep=3 --tries=3 --queue=default", "exec_interpreter": "php", "exec_mode": "fork", "watch": false, "autorestart": true, "max_restarts": 10, "restart_delay": 5000, "cwd": "/home/<USER>/codes/pre/pipipen-api"}, {"name": "[pipipen-pre] [schedule-worker]", "script": "artisan", "args": "schedule:work", "exec_interpreter": "php", "exec_mode": "fork", "watch": false, "autorestart": true, "max_restarts": 10, "restart_delay": 5000, "cwd": "/home/<USER>/codes/pre/pipipen-api"}, {"name": "[pipipen-pre] [reverb-worker]", "script": "artisan", "args": "reverb:start --debug", "exec_interpreter": "php", "exec_mode": "fork", "watch": false, "autorestart": true, "max_restarts": 10, "restart_delay": 5000, "cwd": "/home/<USER>/codes/pre/pipipen-api"}]}