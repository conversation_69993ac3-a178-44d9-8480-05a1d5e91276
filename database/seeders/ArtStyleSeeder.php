<?php

namespace Database\Seeders;

use App\Models\ArtStyle;
use File;
use Illuminate\Database\Seeder;

class ArtStyleSeeder extends Seeder
{
    public function run()
    {
        $data = json_decode(File::get(database_path('seeders/art_styles.json')), true);

        foreach ($data as $tag) {
            $tag['_lang'] = 'en';

            $enTag = $tag['en'];
            $exists = ArtStyle::query()->where('name->en', $enTag)->first();
            if ($exists) {
                $exists->update([
                    'name' => $tag,
                ]);

                continue;
            }

            ArtStyle::query()->create([
                'name' => $tag,
            ]);
        }
    }
}
