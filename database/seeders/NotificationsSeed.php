<?php

namespace Database\Seeders;

use App\Enums\SystemNotificationScene;
use App\Models\Group;
use App\Models\User;
use App\Notifications\SystemNotification;
use Illuminate\Database\Seeder;

class NotificationsSeed extends Seeder
{
    public static function run2()
    {
        $seeder = new NotificationsSeed();
        $seeder->run();
    }

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $user1 = User::first();
        $user2 = User::latest()->first();
        $group = Group::first();

        // $randomScene = SystemNotificationScene::cases()[rand(0, count(SystemNotificationScene::cases()) - 1)];

        $notify = SystemNotification::make()
            ->setScene(SystemNotificationScene::ServiceRequestCreated)
            ->setMeta([
                'worktask_id' => 73,
            ]);

        $user1->artist->notify($notify);
        $user1->notify($notify);

        // $group->notify($notify);
        // $user1->notify($notify
        //     ->setMeta([
        //         'worktask_id' => 73,
        //         'to' => 'artist_center',
        //     ]));
        // $user2->notify($notify
        //     ->setMeta([
        //         'worktask_id' => 73,
        //         'to' => 'artist_center',
        //     ]));

        // for ($i = 0; $i < 10; $i++) {

        //     $user->notify(new SystemNotification(
        //         [
        //             'zh' => "标题 $i",
        //             'en' => "Title $i",
        //             '_lang' => 'en',
        //         ],
        //         [
        //             'zh' => "内容 $i",
        //             'en' => "Content $i",
        //             '_lang' => 'en',
        //         ],
        //         'https://www.baidu.com',
        //     ));

        // }

    }
}
