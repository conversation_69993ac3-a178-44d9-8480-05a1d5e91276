<?php

namespace Database\Seeders;

use App\Models\Tag;
use File;
use Illuminate\Database\Seeder;

class TagSeeder extends Seeder
{
    public function run()
    {
        $data = json_decode(File::get(database_path('seeders/tags.json')), true);

        foreach ($data as $tag) {
            $tag['_lang'] = 'en';

            $enTag = $tag['en'];
            $exists = Tag::query()->where('name->en', $enTag)->first();
            if ($exists) {
                $exists->update([
                    'name' => $tag,
                ]);

                continue;
            }

            Tag::query()->create([
                'name' => $tag,
            ]);
        }
    }

    public function updateSearchText()
    {
        $tags = Tag::query()->get();

        foreach ($tags as $tag) {
            $names = $tag->name;
            $ret = '';
            foreach ($names as $locale => $name) {
                if ($locale == '_lang') {
                    continue;
                }
                $name = strtolower($name);
                $ret .= $name.' ';
            }
            $tag->search_text = $ret;
            $tag->save();
        }
    }
}
