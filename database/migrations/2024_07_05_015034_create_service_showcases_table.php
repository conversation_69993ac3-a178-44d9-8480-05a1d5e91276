<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('service_showcases', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('service_id')->nullable();
            $table->enum('type', ['artwork', 'upload_image', 'youtube_link', 'bilibili_link'])->nullable();
            $table->integer('artwork_id')->nullable();
            $table->integer('upload_image_id')->nullable();
            $table->longText('video_url')->nullable();
            $table->integer('sort')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('service_showcases');
    }
};
