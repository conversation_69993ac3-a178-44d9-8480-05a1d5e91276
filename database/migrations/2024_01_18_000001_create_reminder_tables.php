<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('reminder_categories', function (Blueprint $table) {
            $table->id();
            $table->json('name');
            $table->json('description')->nullable();
            $table->timestamps();
        });

        Schema::create('reminder_templates', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('category_id');
            $table->json('title');
            $table->json('content')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->index('category_id');
        });

        Schema::create('group_reminders', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('group_id');
            $table->unsignedBigInteger('sender_id');
            $table->unsignedBigInteger('template_id');
            $table->json('mentioned_user_ids')->comment('被提醒的用户ID列表');
            $table->timestamp('remind_at')->comment('提醒时间');
            $table->enum('status', ['pending', 'successed', 'failed'])->default('pending');
            $table->timestamps();
            $table->softDeletes();

            // 添加索引以优化查询性能
            $table->index('group_id');
            $table->index('sender_id');
            $table->index('template_id');
            $table->index(['sender_id', 'created_at']); // 用于24小时限制检查的复合索引
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('group_reminders');
        Schema::dropIfExists('reminder_templates');
        Schema::dropIfExists('reminder_categories');
    }
};
