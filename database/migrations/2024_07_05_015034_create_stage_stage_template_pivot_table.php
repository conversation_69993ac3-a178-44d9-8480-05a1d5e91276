<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stage_stage_template_pivot', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('stage_template_id')->nullable()->index('stage_temaple_id');
            $table->unsignedInteger('stage_id')->nullable();
            $table->unsignedInteger('percent')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stage_stage_template_pivot');
    }
};
