<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_sku_items', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('product_id')->nullable();
            $table->string('sku_name')->nullable();
            $table->integer('sku_num')->nullable();
            $table->longText('sku_attribute')->nullable();
            $table->string('file_url')->nullable();
            $table->decimal('price', 10)->nullable();
            $table->integer('stock')->nullable();
            $table->integer('sales')->nullable();
            $table->integer('max_per_user')->nullable()->default(1);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_sku_items');
    }
};
