<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('project_manager_language_pivot', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('language_id')->nullable()->index('language_id');
            $table->integer('project_manager_id')->nullable()->index('project_manager_id');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('project_manager_language_pivot');
    }
};
