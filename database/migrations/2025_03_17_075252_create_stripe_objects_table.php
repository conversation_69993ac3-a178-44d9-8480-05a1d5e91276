<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stripe_objects', function (Blueprint $table) {
            $table->id();
            $table->string('stripe_id');      // Stripe 对象 ID
            $table->string('type');           // 对象类型，如 'session', 'payment_intent'
            $table->json('data');             // 原始数据
            $table->timestamps();

            $table->unique(['stripe_id', 'type']); // 确保唯一性
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stripe_objects');
    }
};
