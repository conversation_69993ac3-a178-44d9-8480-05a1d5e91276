<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('artworks', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('artist_id')->nullable();
            $table->tinyInteger('type')->nullable();
            $table->integer('is_show')->nullable()->default(0);
            $table->string('title')->nullable();
            $table->longText('detail')->nullable();
            $table->enum('upload_type', ['upload_image', 'youtube_link', 'bilibili_link'])->nullable();
            $table->string('url_sm')->default('');
            $table->string('url_md')->default('');
            $table->string('url_lg')->default('');
            $table->string('video_url')->nullable();
            $table->integer('sort')->nullable();
            $table->integer('likes')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('artworks');
    }
};
