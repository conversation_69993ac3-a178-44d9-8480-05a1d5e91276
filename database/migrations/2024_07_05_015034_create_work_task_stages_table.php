<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('work_task_stages', function (Blueprint $table) {
            $table->integer('id', true);
            $table->integer('work_task_id');
            $table->enum('type', ['service', 'project']);
            $table->integer('service_id')->nullable();
            $table->integer('project_id')->nullable();
            $table->json('name');
            $table->integer('percent');
            $table->integer('is_paid')->default(0);
            $table->enum('work_status', ['pending', 'working', 'finished'])->nullable()->default('pending');
            $table->integer('amount');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('work_task_stages');
    }
};
