<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('upload_images', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('artist_id')->default(0);
            $table->integer('user_id')->default(0);
            $table->integer('work_task_file_change_request_id')->default(0);
            $table->integer('work_task_file_id')->default(0);
            $table->string('type');
            $table->string('url_og');
            $table->string('url_sm');
            $table->string('url_md');
            $table->string('url_lg');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('upload_images');
    }
};
