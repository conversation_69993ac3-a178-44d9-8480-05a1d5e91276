<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stripe_checkout_sessions', function (Blueprint $table) {
            $table->integer('id', true);
            $table->string('checkout_session_id');
            $table->enum('process_status', ['pending', 'processing', 'finished'])->default('pending');
            $table->enum('pay_type', ['full_pay', 'stage_pay']);
            $table->integer('work_task_id');
            $table->json('work_task_stage_ids');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stripe_checkout_sessions');
    }
};
