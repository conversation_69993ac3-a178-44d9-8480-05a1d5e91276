<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('work_task_price_changes', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('work_task_id');
            $table->unsignedBigInteger('currency_id');
            $table->morphs('initiator'); // 发起人 user 或 artist
            $table->morphs('approver'); // 审核人 user 或 artist
            $table->decimal('old_price', 10, 2);
            $table->decimal('new_price', 10, 2);
            $table->enum('status', [
                'pending',           // 待审核
                'wait_pay',          // 已同意待支付
                'paid',             // 已支付完成
                'rejected',          // 已拒绝
                'canceled',           // 已取消
            ]);
            $table->timestamp('approved_at')->nullable(); // 审核时间
            $table->timestamp('paid_at')->nullable(); // 支付时间
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('work_task_price_changes');
    }
};
