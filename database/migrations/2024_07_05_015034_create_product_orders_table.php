<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_orders', function (Blueprint $table) {
            $table->increments('id');
            $table->string('order_number', 191);
            $table->integer('user_id')->nullable();
            $table->integer('status')->nullable();
            $table->integer('pay_status')->nullable();
            $table->decimal('price', 10)->nullable();
            $table->decimal('point_discount', 10)->nullable();
            $table->decimal('point_used', 10)->nullable();
            $table->timestamp('paid_at')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_orders');
    }
};
