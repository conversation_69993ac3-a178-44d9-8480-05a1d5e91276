<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('token_transactions', function (Blueprint $table) {
            $table->integer('id', true);
            $table->integer('transaction_id')->nullable();
            $table->integer('user_id')->nullable();
            $table->enum('type', ['plus', 'minus'])->nullable();
            $table->integer('input_token')->nullable();
            $table->integer('output_token')->nullable();
            $table->integer('total_token')->nullable();
            $table->json('message')->nullable();
            $table->longText('related_data')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('token_transactions');
    }
};
