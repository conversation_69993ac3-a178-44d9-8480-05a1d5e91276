<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('services', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('artist_id')->nullable();
            $table->integer('status')->nullable();
            $table->json('name')->nullable();
            $table->json('content')->nullable();
            $table->integer('price')->nullable();
            $table->integer('base_currency_price')->nullable();
            $table->enum('fee_type', ['buyer', 'seller'])->nullable();
            $table->enum('creation_type', ['custom', 'template'])->nullable();
            $table->enum('workflow_type', ['normal', 'ez'])->nullable();
            $table->integer('currency_id')->nullable();
            $table->tinyText('currency_symbol')->nullable();
            $table->integer('stock')->nullable();
            $table->integer('days_need')->nullable();
            $table->integer('usage')->nullable();
            $table->integer('review_count')->nullable();
            $table->decimal('rating_score', 5, 1)->nullable()->default(5);
            $table->string('color_mode')->nullable();
            $table->string('size_spec')->nullable();
            $table->json('prod_format')->nullable();
            $table->string('use_range')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('services');
    }
};
