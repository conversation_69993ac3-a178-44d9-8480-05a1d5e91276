<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('artists', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('user_id')->nullable();
            $table->integer('status')->nullable();
            $table->string('uid')->nullable();
            $table->string('name')->nullable();
            $table->longText('avatar')->nullable();
            $table->longText('cover')->nullable();
            $table->longText('intro')->nullable();
            $table->string('link_twitter')->nullable();
            $table->string('link_pixiv')->nullable();
            $table->string('link_weibo')->nullable();
            $table->string('link_bilibili')->nullable();
            $table->string('link_mihuashi')->nullable();
            $table->timestamps();
            $table->integer('sort')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('artists');
    }
};
