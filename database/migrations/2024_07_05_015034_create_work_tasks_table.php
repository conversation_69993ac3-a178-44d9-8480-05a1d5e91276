<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('work_tasks', function (Blueprint $table) {
            $table->integer('id', true);
            $table->integer('artist_id')->nullable();
            $table->integer('user_id')->nullable();
            $table->string('reqable_type')->nullable();
            $table->integer('reqable_id')->nullable();
            $table->string('busable_type')->nullable();
            $table->integer('busable_id')->nullable();
            $table->json('busable_snap')->nullable();
            $table->json('reqable_snap')->nullable();
            $table->enum('status', ['pending', 'working', 'user_canceled', 'artist_rejected', 'artist_accepted', 'wait_pay'])->nullable();
            $table->integer('currency_id')->nullable();
            $table->json('name')->nullable();
            $table->integer('price')->nullable();
            $table->timestamp('deadline')->nullable();
            $table->enum('reject_type', ['style_not_suitable'])->nullable();
            $table->json('reject_content')->nullable();
            $table->timestamp('pay_expired_at')->nullable();
            $table->integer('payment_fee')->nullable();
            $table->integer('plat_fee')->nullable();
            $table->enum('plat_fee_type', ['buyer', 'seller'])->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('work_tasks');
    }
};
