<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('wallet_deduction_records', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->comment('用户ID');
            $table->unsignedInteger('wallet_transaction_id')->comment('关联的wallet_transactions表ID');
            $table->unsignedBigInteger('order_id')->comment('关联的订单ID');
            $table->unsignedBigInteger('artist_user_id')->comment('艺术家用户ID');
            $table->unsignedBigInteger('artist_withdraw_account_id')->comment('艺术家Stripe提现账户ID');
            $table->integer('amount')->comment('补差金额(单位:分)');
            $table->unsignedBigInteger('currency_id')->comment('货币ID');
            $table->enum('status', ['pending', 'processing', 'completed', 'failed', 'cancelled'])
                ->default('pending')
                ->comment('补差状态');
            $table->string('stripe_transfer_id')->nullable()->comment('Stripe转账ID');
            $table->timestamp('compensated_at')->nullable()->comment('补差完成时间');
            $table->text('failure_reason')->nullable()->comment('失败原因');
            $table->text('remark')->nullable()->comment('备注');
            $table->json('metadata')->nullable()->comment('额外元数据');
            $table->timestamps();

            // 索引
            $table->index(['user_id', 'status']);
            $table->index(['artist_user_id', 'status']);
            $table->index('wallet_transaction_id');
            $table->index('order_id');
            $table->index('artist_withdraw_account_id');
            $table->index('stripe_transfer_id');
            $table->index(['status', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('wallet_deduction_records');
    }
};
