
<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" itemscope itemtype="http://schema.org/WebPage">

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <title>
    Elda Solar Studio
  </title>
  @include('frontend/css')
</head>

<body class="coworking bg-gray-200">
  <!-- Navbar -->
  @include('frontend/topnav')
  <!-- -------- START HEADER 1 w/ text and image on right ------- -->
  {{-- <header>
    <div class="page-header min-vh-80" style="background-image: url('{{asset('/image/pia_bg_4k.jpg')}}');" loading="lazy">
      <span class="mask bg-gradient-dark opacity-0"></span>
      <div class="container">
        <div class="row">
          <div class="col-lg-6 col-md-7 d-flex justify-content-center text-md-start text-center flex-column mt-sm-0 mt-7">
            <h1 class="text-white">Elda Solar Studio</h1>
            <p class="lead pe-md-5 me-md-5 text-white opacity-8">A journey of a thousand miles begins with a single step. Start your vtuber adventure with us today.</p>
            <div class="buttons">
              <a href="{{url('/request')}}" type="button" class="btn bg-gradient-info mt-4">Request Now</a>

            </div>
          </div>
        </div>
      </div>
    </div>
  </header> --}}
  <header>
    <div class="page-header min-vh-60" style="background-image: url('{{asset('/image/raingirl_1_lg.jpg')}}');background-position:middle" loading="lazy">
      <span class="mask bg-gradient-dark opacity-5"></span>
      <div class="container">
        <div class="row">
          <div class="col-lg-6 col-md-7 d-flex justify-content-center text-md-start text-center flex-column mt-sm-0 mt-7">
            <h1 class="text-white">Marketplace</h1>
            <p class="lead pe-md-5 me-md-5 text-white opacity-8">Find what they're looking for easily, effectively, and safely. </p>
            {{-- <div class="buttons">
              <a href="{{url('/request')}}" type="button" class="btn bg-gradient-info mt-4">Request Now</a>
            </div> --}}
          </div>
        </div>
      </div>
    </div>
  </header>
  <!-- -------- END HEADER 1 w/ text and image on right ------- -->
  <div class="card card-body blur shadow-blur mx-2 mx-lg-3 mt-n6 mb-4">
    <a href="{{url('cart')}}" class="btn btn-outline-dark ms-auto position-sticky top-3" type="button" name="button">
      <i class="material-icons me-2">shopping_cart</i><span id="cart_count">{{count(session()->get('cart')??[])}}</span>
    </a>
    <div id="app">
      <section class="pt-3 pb-0">
      <div class="container">
        <div class="row">
          <div class="col-lg-3 col-12">
            <div class="card">
              <div class="card-body">
                <div class="accordion" id="accordionProduct">
                  <div class="accordion-item mb-1">
                    <h6 class="accordion-header" id="headingOne">
                      <button class="accordion-button border-bottom font-weight-bold text-start" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                        Category
                        <i class="collapse-close fa fa-plus text-xs pt-1 position-absolute end-0"></i>
                        <i class="collapse-open fa fa-minus text-xs pt-1 position-absolute end-0"></i>
                      </button>
                    </h6>
                    <div id="collapseOne" class="accordion-collapse collapse show" aria-labelledby="headingOne" >
                      <div class="accordion-body opacity-8">
                        {{-- <div class="form-check ps-0 pe-2 d-inline-block">
                          <input class="form-check-input product-all" type="checkbox" v-model="product_all" id="product_all">
                          <label class="custom-control-label ms-0 cursor-pointer" for="product_all">All </label>
                        </div><br> --}}
                        
                        @foreach($categories as $category)
                        <div class="form-check ps-0 me-2 d-inline-block">
                          <input class="form-check-input product-c" type="checkbox" id="product-c-{{$category->id}}" value="{{$category->id}}" v-model="checkedCategories" @change="check()">
                          <label class="custom-control-label ms-0 cursor-pointer ps-1" for="product-c-{{$category->id}}"> {{$category->name}} </label>
                        </div>
                        @endforeach
                      </div>
                    </div>
                  </div>
                  <div class="accordion-item mb-1">
                    <h6 class="accordion-header" id="headingTwo">
                      <button class="accordion-button border-bottom font-weight-bold text-start" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="true" aria-controls="collapseTwo">
                        Price
                        <i class="collapse-close fa fa-plus text-xs pt-1 position-absolute end-0"></i>
                        <i class="collapse-open fa fa-minus text-xs pt-1 position-absolute end-0"></i>
                      </button>
                    </h6>
                    <div id="collapseTwo" class="accordion-collapse collapse show" aria-labelledby="headingTwo" >
                      <div class="accordion-body text-sm opacity-8">
                        <div class="row">
                          <div class="col-sm-6">
                            <label for="firstName" class="form-label">From</label>
                            <input type="number" class="form-control border ps-2"  placeholder="$" v-model="price_from">
                          </div>
                          <div class="col-sm-6">
                            <label for="firstName" class="form-label">To</label>
                            <input type="number" class="form-control border ps-2"  placeholder="$" v-model="price_to">
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  {{-- <div class="accordion-item mb-1">
                    <h6 class="accordion-header" id="headingThree">
                      <button class="accordion-button border-bottom font-weight-bold text-start" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="true" aria-controls="collapseThree">
                        Search
                        <i class="collapse-close fa fa-plus text-xs pt-1 position-absolute end-0"></i>
                        <i class="collapse-open fa fa-minus text-xs pt-1 position-absolute end-0"></i>
                      </button>
                    </h6>
                    <div id="collapseThree" class="accordion-collapse collapse show" aria-labelledby="headingThree">
                      <div class="accordion-body text-sm opacity-8">
                        <div class="row">
                          <div class="col-sm-12">
                            <label for="firstName" class="form-label">Keyword</label>
                            <input type="text" class="form-control border ps-2"  placeholder="" v-model="key_word">
                          </div>
                        </div>
                      </div>
                    </div>
                  </div> --}}
                </div>
                <div class="text-end">
                  <a class="btn bg-gradient-dark mb-0 mt-lg-auto me-2 btn-sm" type="button" href="{{url('/shop')}}">Clear</a>
                  <button class="btn bg-gradient-info mb-0 mt-lg-auto btn-sm" type="button" name="button" @click="filter()">Filter</button>
      
                </div>
                

              </div>
              
            </div>
          </div>
          <div class="col-9">
            <div class="row ">
              @foreach($products as $product)
              <div class="col-lg-4 col-md-6">
                <div class="card shadow-lg mb-3">
                  <div class="card-header p-3 position-relative z-index-1">
                    <a href="{{url('product/'.$product->id)}}">
                      <img class="img-fluid border-radius-lg product-image-pv" src="{{asset('storage/').'/'.json_decode($product->images)[0]}}" />
                    </a>         
                  </div>
                  <div class="card-body p-3 pt-0">
                    <a href="{{url('product/'.$product->id)}}" class="text-dark product-title d-block">
                      {{$product->name}}
                    </a>
                    <div class="text-gradient text-warning text-uppercase font-weight-bold my-2">
                      @if($product->skus->isNotEmpty())
                        @if(count($product->skus) > 1 && $product->skus->first()->price != $product->skus->last()->price)
                          {{'$'.$product->skus->first()->price . ' - ' . '$' . $product->skus->last()->price}}
                        @else
                          {{'$'.$product->skus[0]->price }}
                        @endif
                      @else
                        -
                      @endif
                    </div>
                    <div class="author align-items-center">
                      @foreach($product->artists as $artist)
                      <a href="{{url('artist/'.$artist->id)}}" class="avatar avatar-lg rounded-circle border me-n3" data-bs-toggle="tooltip" data-bs-placement="bottom" title="{{$artist->name}}">
                        <img alt="Image placeholder" src="{{asset('storage/'.$artist->avatar)}}">
                      </a>
                      @endforeach
                    </div>
                    <div class="text-end mt-2">
                      {{-- <button type="button" class="btn btn-outline-info btn-sm mb-0 btn-add-cart" product-id="{{$product->id}}">Add To Card</button> --}}
                    </div>
                  </div>
                </div>
              </div>
              @endforeach
            </div>

          </div>
          <div class="col-sm-12 ms-auto text-end">

            {{-- {{ $products->links() }} --}}
            <ul class="pagination pagination-info mt-4">
              <li class="page-item ms-auto">
                <a class="page-link" href="{{$products->previousPageUrl()}}" aria-label="Previous">
                  <span aria-hidden="true"><i class="fa fa-angle-double-left" aria-hidden="true"></i></span>
                </a>
              </li>
              @for($i = 1; $i<=$products->lastPage();$i++)
              <li class="page-item {{$products->currentPage() == $i ?"active":""}}">
                <a class="page-link" href="{{$products->url($i)}}">{{$i}}</a>
              </li>
              @endfor
              <li class="page-item">
                <a class="page-link" href="{{$products->nextPageUrl()}}" aria-label="Next">
                  <span aria-hidden="true"><i class="fa fa-angle-double-right" aria-hidden="true"></i></span>
                </a>
              </li>
            </ul>
          </div>
        </div>
        
      </div>
      </section>
    </div>
  </div>
  @include('frontend/footer')
  @include('frontend/js')
  <script src="{{asset('js/vue/vue.js')}}"></script>
  <script type="module">
    const { createApp, ref } = Vue
    createApp({
      data() {
        return {
          checkedCategories:[],
          product_all:true,
          filter_parameter:"",
          price_from:"",
          price_to:"",
          
          // outside_data:outside_data,
          // categories: {!! json_encode($categories) !!},
          // products:{!! json_encode($products) !!},
        }
      },
      methods:{
        check: function() {
          // this.product_all = false
        },
        filter:function(){
          let url_params = "";
          let checkedCategories = this.checkedCategories.toString();
          let price_from = this.price_from;
          let price_to = this.price_to
          if(checkedCategories){
            url_params +="categories="+checkedCategories+"&"
          }
          if(price_from){
            url_params +="price_from="+price_from+"&"
          }
          if(price_to){
            url_params +="price_to="+price_to+"&"
          }
         
          // let price_range = "100,2000";
          // let url_params = "category="+checkedCategories+"&"+"price="+price_range
          window.location.href = "{{url('/shop')}}"+"?"+url_params;
        },
      },
      created(){
        let params = new URLSearchParams(window.location.search)
        let categories = params.get('categories')
        let price_from = params.get('price_from')
        let price_to = params.get('price_to')
        if(categories){
          this.checkedCategories = categories.split(",")
        }
        if(price_from){
          this.price_from =price_from
        }
        if(price_to){
          this.price_to =price_to
        }
      }
    }).mount('#app')
  </script>
</body>

</html>
