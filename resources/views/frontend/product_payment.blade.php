
<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" itemscope itemtype="http://schema.org/WebPage">

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <title>
    ESS Product
  </title>
  @include('frontend/css')
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@8/swiper-bundle.min.css"/>
</head>

<body class="coworking bg-gray-200">
  <!-- Navbar -->
  @include('frontend/topnav')
  <!-- -------- START HEADER 1 w/ text and image on right ------- -->
  <header>
    <div class="page-header min-vh-50" style="background-image: url('{{asset('/image/raingirl_2.jpg')}}');background-position:top" loading="lazy">
      <span class="mask bg-gradient-dark opacity-7"></span>
      <div class="container">
        
      </div>
    </div>
  </header>
  <!-- -------- END HEADER 1 w/ text and image on right ------- -->
  <div class="card card-body blur shadow-blur mx-2 mx-lg-3 mt-n6 mb-4">
    <div class="section my-4 my-lg-5">
      <div class="container">
        <div class="text-start">
          <a href="{{url('/shop')}}" class="btn btn-outline-info btn-sm">Back To Shop</a>
        </div>
        <h5 class="text-center">Order Checkout</h5>
        <div class="row my-4">
          <div class="col-md-12 col-lg-12 col-sm-10 mx-auto">
            @if($payment->pay_status == 0)
            <div class="card">
              <div class="card-header text-center">
                <div class="row justify-content-between">
                  <div class="col-md-4 text-start">
                    <img class="mb-2 w-25" src="{{asset('/image/logo_nb.png')}}" alt="Logo">
                    <h6>
                      Elda Solar Studio
                    </h6>
                    {{-- <p class="d-block text-secondary">Email: <EMAIL></p> --}}
                  </div>
                  <div class="col-lg-3 col-md-7 text-md-end text-start mt-5">
                    <h6 class="d-block mt-2 mb-0">User: {{$user->email}}</h6>
                    <p class="text-secondary"><br>
                      
                    </p>
                  </div>
                </div>
                <br>
                <div class="row justify-content-md-between">
                  <div class="col-md-4 mt-auto">
                    <h6 class="mb-0 text-start text-secondary font-weight-normal">
                      Order # {{$order->id}}</br>
                    </h6>
                    
                    <h5 class="text-start mb-0">
                      
                      
                    </h5>
                  </div>
                  <div class="col-lg-5 col-md-7 mt-auto">
                    <div class="row mt-md-5 mt-4 text-md-end text-start">
                      <div class="col-md-6">
                        {{-- <h6 class="text-secondary font-weight-normal mb-0">Invoice date:</h6> --}}
                      </div>
                      <div class="col-md-6">
                        <h6 class="text-dark mb-0"></h6>
                      </div>
                    </div>
                    <div class="row text-md-end text-start">
                      {{-- <div class="col-md-6">
                        <h6 class="text-secondary font-weight-normal mb-0">Due date:</h6>
                      </div>
                      <div class="col-md-6">
                        <h6 class="text-dark mb-0">11/03/2019</h6>
                      </div> --}}
                    </div>
                  </div>
                </div>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-12">
                    <div class="table-responsive">
                      <table class="table text-right">
                        <thead>
                          <tr>
                            <th scope="col" class="pe-2 text-start ps-2">Item</th>
                            <th scope="col" class="pe-2">Qty</th>
                            <th scope="col" class="pe-2" colspan="2">Rate</th>
                            <th scope="col" class="pe-2">Amount</th>
                          </tr>
                        </thead>
                        <tbody>
                          @foreach($order_items as $item)
                          <tr>
                            <td class="text-start">{{$item->product_name}}</td>
                            <td class="ps-4">{{$item->quantity}}</td>
                            <td class="ps-4" colspan="2">$ {{$item->unit_price}}</td>
                            <td class="ps-4">$ {{$item->total_price}}</td>
                          </tr>
                          @endforeach
                          @if($order->point_discount)
                          <tr>
                            <td class="text-start">Point Used : {{$order->point_used}}</td>
                            <td class="ps-4">1</td>
                            <td class="ps-4" colspan="2">- $ {{$order->point_discount}}</td>
                            <td class="ps-4">- $ {{$order->point_discount}}</td>
                          </tr>
                          @endif
                        </tbody>
                        <tfoot>
                          <tr>
                            <th></th>
                            <th></th>
                            <th class="h6 ps-4" colspan="2">Total</th>
                            <th colspan="1" class="text-right h6 ps-4">$ {{$order->price}}</th>
                          </tr>
                        </tfoot>
                      </table>
                    </div>
                  </div>
                </div>
                <div class="mt-5 text-end">
                  <h6>Payment Detail : </h6>
                  <h6></h6>
                  <h4>Total : ${{$payment->pay_amount}}</h4>
                  
                </div>
              </div>
              <div class="card-footer mt-md-5 mt-4">
                <div class="row">
                  <div class="col-lg-5 text-left">
                    <h5>Thank you!</h5>
                    <div class="text-secondary text-sm">If you encounter any issues with placing an order, you can contact us at:</div>
                    <h6 class="text-secondary font-weight-normal mb-0">
                      <span class="text-dark"><EMAIL></span>
                    </h6>
                  </div>
                  <div class="col-lg-4 text-left">
                    
                  </div>
                  <div class="col-lg-3 text-md-end mt-md-0 mt-3">
                    @if($payment->pay_amount == 0)
                    <button type="button" class="btn bg-gradient-info" id="place_order">Place Order</button>
                    @else
                    <div id="paypal-button-container"></div>
                    @endif
                  </div>
                </div>
              </div>
            </div>
            @elseif($payment->pay_status == 1)
            <div class="card paid">
              <div class="card-body">
                <div class="text-center">
                  <img src="{{asset('image/pia_emote/love.gif')}}" class="w-30" alt="avatar image">
                  <h3 class="text-info-gradiant mt-2">Thank you</h3>
                  <p>You can go to <a href = "{{url('/dashboard/orders')}}">My Orders</a> to view & download your items</p>
                  {{-- <p>You may now close this window.</p> --}}
                  {{-- <a href="{{url('/dashboard/commissions')}}" type="button" class="btn bg-gradient-info mt-4">Close Window</a> --}}
                </div>
                
                
              </div>
              <div class="card-footer mt-md-5 mt-4">
                <div class="row">
                  <div class="col-lg-12 text-left">
                    <h5>Thank you!</h5>
                    <p class="text-secondary text-sm">If you encounter any issues, you can contact us at:</p>
                    <h6 class="text-secondary font-weight-normal mb-0">
                      email:
                      <span class="text-dark"><EMAIL></span>
                    </h6>
                    
                  </div>
                  <div class="col-lg-8 text-md-end mt-md-0 mt-3">
                    {{-- <button class="btn bg-gradient-info mt-lg-7 mb-0" onClick="window.print()" type="button" name="button">Pay Now</button> --}}
                    {{-- <div id="paypal-button-container"></div> --}}
                  </div>
                  <div class="col-lg-4 text-md-end mt-md-0 mt-3">
                    {{-- <button class="btn bg-gradient-info mt-lg-7 mb-0" onClick="window.print()" type="button" name="button">Pay Now</button> --}}
                    {{-- <div id="paypal-button-container"></div> --}}
                  </div>
                </div>
              </div>
            </div>
            @endif
            
          </div>
        </div>
      </div>
    </div>
  </div>
    <!-- END Teams w/ 4 cards w/ image on left & descriptio/social link on right -->
    <!-- -------- END PRE-FOOTER 8 w/ TEXT, BG IMAGE AND 2 BUTTONS ------- -->
  </div>
  @include('frontend/footer')
  @include('frontend/js')
  <script src="https://www.paypal.com/sdk/js?client-id={{ env('PAYPAL_SANDBOX_CLIENT_ID') }}&currency=USD"></script>
  <script>
    paypal.Buttons({
        // Sets up the transaction when a payment button is clicked
        createOrder: (data, actions) => {
          return actions.order.create({
            purchase_units: [{
              amount: {
                value: '{{$payment->pay_amount}}' // Can also reference a variable or function
              }
            }]
          });
        },
        // Finalize the transaction after payer approval
        onApprove: (data, actions) => {
          return actions.order.capture().then(function(paypal_order) {
            // Successful capture! For dev/demo purposes:
            // console.log('Capture result', paypal_order, JSON.stringify(paypal_order, null, 2));
            // const transaction = paypal_order.purchase_units[0].payments.captures[0];
            let paypal_data = {
              "_token": "{{ csrf_token() }}",
              "order_id":"{{$order->id}}",
              'payment_id':"{{$payment->id}}",
              "paypal_order_id":paypal_order.id,
              'paid_amount':paypal_order.purchase_units[0].amount.value,
              'full_data':paypal_order,
            }

            $.ajax({
              method: "POST",
              url: "{{url('product_payment/approve/paypal')}}",
              data: paypal_data,
              success: function(response) {
                location.reload();
              }
            });            
          });
        }
      }).render('#paypal-button-container');


      $('#place_order').on('click', function(){
        let order_data = {
          "_token": "{{ csrf_token() }}",
          "order_id":"{{$order->id}}",
          'payment_id':"{{$payment->id}}",
        }

        $.ajax({
          method: "POST",
          url: "{{url('product_payment/place_no_pay')}}",
          data: order_data,
          success: function(response) {
            location.reload();
          }
        });            

      });
  </script>
</body>


</html>
