<!--   Core JS Files   -->
<script src="{{asset('frontend/js/core/popper.min.js')}}" type="text/javascript"></script>
<script src="{{asset('frontend/js/core/bootstrap.min.js')}}" type="text/javascript"></script>
<script src="{{asset('frontend/js/plugins/perfect-scrollbar.min.js')}}"></script>
<!--  Plugin for TypedJS, full documentation here: https://github.com/mattboldt/typed.js/ -->
{{-- <script src="{{asset('frontend/js/plugins/typedjs.js')}}"></script> --}}
<!--  Plugin for Parallax, full documentation here: https://github.com/wagerfield/parallax  -->
{{-- <script src="{{asset('frontend/js/plugins/parallax.min.js')}}"></script> --}}
<!--  Plugin for the Sliders, full documentation here: http://refreshless.com/nouislider/ -->
{{-- <script src="{{asset('frontend/js/plugins/nouislider.min.js')}}" type="text/javascript"></script> --}}
<!--  Plugin for the blob animation -->
<script src="{{asset('frontend/js/plugins/anime.min.js')}}" type="text/javascript"></script>
<script src="{{asset('frontend/js/plugins/choices.min.js')}}" type="text/javascript"></script>
<!-- Control Center for Material UI Kit: parallax effects, scripts for the example pages etc -->
<!--  Google Maps Plugin    -->
{{-- <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyDTTfWur0PDbZWPr7Pmq8K3jiDp0_xUziI"></script> --}}

<script src="{{asset('frontend/js/material-kit-pro.js')}}?v=3.0.3" type="text/javascript"></script>

<!--  jquery libraries    -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js" integrity="sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=" crossorigin="anonymous"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>

<script src="{{asset('frontend/fancybox/jquery.fancybox.min.js')}}" type="text/javascript"></script>
<script src="{{asset('js/zoom/jquery.zoom.min.js')}}" type="text/javascript"></script>

<script type="application/ld+json">{"@context":"http://schema.org","@type":"Organization","name":"Elda Solar Studio","url":"https://eldasolar.com/","address":"","sameAs":["https://twitter.com/EldaSolarStudio"]}</script>

{{-- <script>
$(".nav-soon").hover(
  function () {
    $(this).append($("<span> Coming Soon...</span>"));
  },
  function () {
    $(this).find("span:last").remove();
  }
);
</script> --}}
