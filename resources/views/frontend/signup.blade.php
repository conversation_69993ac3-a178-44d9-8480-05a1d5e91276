<!DOCTYPE html>
<html lang="en" itemscope itemtype="http://schema.org/WebPage">

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <link rel="apple-touch-icon" sizes="76x76" href="../../assets/img/apple-icon.png">
  <link rel="icon" type="image/png" href="../../assets/img/favicon.png">
  <title>
    Elda Solar Studio
  </title>
  @include('frontend/css')
</head>

<body class="sign-up-basic overflow-hidden">
  <!-- Navbar Transparent -->
  @include('frontend/topnav-tp')

  <!-- End Navbar -->


  <div class="page-header min-vh-100 position-relative">

    <video class="z-index-1" playsinline="playsinline" autoplay="true" muted="unmute" loop="loop" loading="lazy">
      <source src="{{asset('storage/background-6.mp4')}}" type="video/mp4">
    </video>
    <div class="mask bg-gradient-dark opacity-0 z-index-2"></div>
    <div class="container my-auto z-index-3">
      <div class="row">
        <div class="col-lg-4 col-md-8 mx-auto">
          <div class="card z-index-0 shadow-none bg-dark-blur fadeIn3 fadeInBottom">
            {{-- <div class="card-header p-0 position-relative mt-n4 mx-3 z-index-2">
              <div class="bg-gradient-success shadow-success border-radius-lg py-3 pe-1">
                <h4 class="text-white font-weight-bolder text-center mt-2 mb-0">Register with</h4>
                <div class="row mt-3">
                  <div class="col-2 text-center ms-auto">
                    <a class="btn btn-link px-3" href="javascript:;">
                      <i class="fa fa-facebook text-white text-lg"></i>
                    </a>
                  </div>
                  <div class="col-2 text-center px-1">
                    <a class="btn btn-link px-3" href="javascript:;">
                      <i class="fa fa-github text-white text-lg"></i>
                    </a>
                  </div>
                  <div class="col-2 text-center me-auto">
                    <a class="btn btn-link px-3" href="javascript:;">
                      <i class="fa fa-google text-white text-lg"></i>
                    </a>
                  </div>
                </div>
              </div>
            </div> --}}
            {{-- <div class="row px-xl-5 px-sm-4 px-3">
              <div class="mt-2 position-relative text-center">
                <p class="text-sm font-weight-bold mb-2 text-secondary text-border d-inline z-index-2 bg-white px-3">
                  or
                </p>

              </div>

            </div> --}}
            <div class="card-body">
              <div class="shadow-none">
                <h4 class="text-white font-weight-bolder text-center mt-2 mb-0">Sign Up</h4>
                <div class="row mt-3">
                  {{-- <div class="col-2 text-center ms-auto">
                    <a class="btn btn-link px-3" href="javascript:;">
                      <i class="fa fa-facebook text-white text-lg"></i>
                    </a>
                  </div>
                  <div class="col-2 text-center px-1">
                    <a class="btn btn-link px-3" href="javascript:;">
                      <i class="fa fa-github text-white text-lg"></i>
                    </a>
                  </div>
                  <div class="col-2 text-center me-auto">
                    <a class="btn btn-link px-3" href="javascript:;">
                      <i class="fa fa-google text-white text-lg"></i>
                    </a>
                  </div> --}}
                </div>
              </div>
              {{-- <div class="input-group input-group-dynamic mb-3">
                <label class="form-label text-white">Name</label>
                <input type="text" class="form-control text-white" aria-label="Name">
              </div> --}}
              <div class="input-group input-group-dynamic mb-4">
                <label class="form-label text-white">Email</label>
                <input type="text" class="form-control text-white" autocomplete=""  id="em">
              </div>
              <div class="input-group input-group-dynamic mb-4">
                <label class="form-label text-white">Name</label>
                <input type="text" class="form-control text-white"  id="na">
              </div>

              <div class="input-group input-group-dynamic mb-4">
                <label class="form-label text-white">Password</label>
                <input type="password" class="form-control text-white" aria-label="Password" id="password">
              </div>
              <div class="input-group input-group-dynamic mb-4">
                <label class="form-label text-white">Confirm Password</label>
                <input type="password" class="form-control text-white" aria-label="Password" id="confirm_password">
              </div>
              <div class="form-check text-start ps-0">
                <input class="form-check-input bg-dark border-dark" type="checkbox" value="" id="terms_checkbox" checked>
                <label class="form-check-label text-white" for="terms_checkbox">
                  I agree the <a href="{{url('terms-detail')}}" target="_blank" class="text-white font-weight-bolder">Terms and Conditions</a>
                </label>
              </div>
              <div class="text-center">
                <div type="button shadow-none" class="btn btn-outline-white w-100 my-4 mb-2" id="signup">Sign up</div>
              </div>

              <p class="text-sm mt-3 mb-0 text-white">Already have an account? <a href="{{url('signin')}}" class="text-white font-weight-bolder">Sign in</a></p>

            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
  <!--   Core JS Files   -->
  @include('frontend/js')
  <script>

    const random_string = Math.random().toString(36).substring(2,7);
    $('#em').attr('autocomplete',random_string)
    
    $( "#signup" ).click(function() {
      signup()
    });

    $(document).keypress(function(event){
      var keycode = (event.keyCode ? event.keyCode : event.which);
      if(keycode == '13'){
        signup()
      }
    });

    function signup(){
      let signup_data = {
        'email':$('#em').val(),
        'name':$('#na').val(),
        'password':$('#password').val(),
        "_token": "{{ csrf_token() }}"
      }
      var mailformat = /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/;
      if(!$('#em').val().match(mailformat)){
        toastr.error("Please enter correct email format", 'Error',{"positionClass": "toast-top-center",})
      }
      else if(!$('#em').val() || !$('#na').val() || !$('#password').val() ){
        toastr.error("Please fill in all fields", 'Error',{"positionClass": "toast-top-center",})
      }
      else if($('#password').val() != $('#confirm_password').val()){
        toastr.error("Two passwords must be same", 'Error',{"positionClass": "toast-top-center",})
      }
      else if(!$('#terms_checkbox').is(':checked')){
        toastr.error("You need to agree with our Terms & Conditions", 'Error',{"positionClass": "toast-top-center",})
      }
      else{
        $.ajax({
          method: "POST",
          url: "{{url('user/signup')}}",
          data: signup_data,
          success: function(response) {
            if(response.code == 200){
              window.location.href = "{{url('/dashboard/commissions')}}";
            // toastr.success('data.message', 'Success',{"positionClass": "toast-top-center",})
            }
            else{
              toastr.error(response.message, 'Error',{"positionClass": "toast-top-center",})
            }
          },
        // error: function (request, status, error) {
        //   console.log(error)
        // }
        });
      }
    }
  </script>

</body>



</html>


