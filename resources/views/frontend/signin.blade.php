<!DOCTYPE html>
<html lang="en" itemscope itemtype="http://schema.org/WebPage">

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <link rel="apple-touch-icon" sizes="76x76" href="../../assets/img/apple-icon.png">
  <link rel="icon" type="image/png" href="../../assets/img/favicon.png">
  <title>
    Elda Solar Studio
  </title>
  @include('frontend/css')
</head>

<body class="sign-up-basic overflow-hidden">
  <!-- Navbar Transparent -->
  @include('frontend/topnav-tp')

  <!-- End Navbar -->
  <div class="page-header align-items-start min-vh-100">
    <span class="mask bg-gradient-dark opacity-0 z-index-2"></span>
    <video class="z-index-1" playsinline="playsinline" autoplay="true" muted="unmute" loop="loop" loading="lazy">
      <source src="{{asset('storage/background-6.mp4')}}" type="video/mp4">
    </video>


    <div class="container my-auto z-index-3">
      <div class="row">
        <div class="col-lg-4 col-md-8 col-12 mx-auto">
          <div class="card z-index-0 shadow-none bg-dark-blur fadeIn3 fadeInBottom">
            {{-- <div class="card-header p-0 bg-dark-blurposition-relative mt-n4 mx-3 z-index-2">
              <div class="shadow-none">
                <h4 class="text-white font-weight-bolder text-center mt-2 mb-0">Sign in</h4>
                <div class="row mt-3">
                  <div class="col-2 text-center ms-auto">
                    <a class="btn btn-link px-3" href="javascript:;">
                      <i class="fa fa-facebook text-white text-lg"></i>
                    </a>
                  </div>
                  <div class="col-2 text-center px-1">
                    <a class="btn btn-link px-3" href="javascript:;">
                      <i class="fa fa-github text-white text-lg"></i>
                    </a>
                  </div>
                  <div class="col-2 text-center me-auto">
                    <a class="btn btn-link px-3" href="javascript:;">
                      <i class="fa fa-google text-white text-lg"></i>
                    </a>
                  </div>
                </div>
              </div>
            </div> --}}
            <div class="card-body">
              <div class="shadow-none">
                <h4 class="text-white font-weight-bolder text-center mt-2 mb-0">Sign In</h4>
                <div class="row mt-3">
                  {{-- <div class="col-2 text-center ms-auto">
                    <a class="btn btn-link px-3" href="javascript:;">
                      <i class="fa fa-facebook text-white text-lg"></i>
                    </a>
                  </div>
                  <div class="col-2 text-center px-1">
                    <a class="btn btn-link px-3" href="javascript:;">
                      <i class="fa fa-github text-white text-lg"></i>
                    </a>
                  </div>
                  <div class="col-2 text-center me-auto">
                    <a class="btn btn-link px-3" href="javascript:;">
                      <i class="fa fa-google text-white text-lg"></i>
                    </a>
                  </div> --}}
                </div>
              </div>
              <div class="input-group input-group-outline mb-4">
                <label class="form-label text-white">Email</label>
                <input type="email" class="form-control text-white" autocomplete="dontasd" id="email">
              </div>
              <div class="input-group input-group-outline mb-4">
                <label class="form-label text-white">Password</label>
                <input type="password" class="form-control text-white" id="password">
              </div>
              <div class="form-check form-switch d-flex align-items-center mb-4">
                <input class="form-check-input" type="checkbox" id="rememberMe" checked>
                <label class="form-check-label mb-0 ms-3 text-white" for="rememberMe">Remember me</label>
              </div>
              <div class="text-center">
                <div type="button" class="btn btn-outline-primary w-100 mb-4" id="signin">Sign in</div>
              </div>
              <p class="mb-2 text-sm text-left text-white">
                Don't have an account? <a href="{{url('signup')}}" class="text-primary font-weight-bolder">Sign Up</a>
              </p>
              <p class="text-sm text-left text-white">
                Forget Password ?
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
    {{-- <footer class="footer position-absolute bottom-2 py-2 w-100">
      <div class="container">
        <div class="row align-items-center justify-content-lg-between">
          <div class="col-12 col-md-6 my-auto">
            <div class="copyright text-center text-sm text-white text-lg-start">
              © <script>
                document.write(new Date().getFullYear())
              </script>,
              made with <i class="fa fa-heart" aria-hidden="true"></i> by
              <a href="https://www.creative-tim.com" class="font-weight-bold text-white" target="_blank">Creative Tim</a>
              for a better web.
            </div>
          </div>
          <div class="col-12 col-md-6">
            <ul class="nav nav-footer justify-content-center justify-content-lg-end">
              <li class="nav-item">
                <a href="https://www.creative-tim.com" class="nav-link text-white" target="_blank">Creative Tim</a>
              </li>
              <li class="nav-item">
                <a href="https://www.creative-tim.com/presentation" class="nav-link text-white" target="_blank">About Us</a>
              </li>
              <li class="nav-item">
                <a href="https://www.creative-tim.com/blog" class="nav-link text-white" target="_blank">Blog</a>
              </li>
              <li class="nav-item">
                <a href="https://www.creative-tim.com/license" class="nav-link pe-0 text-white" target="_blank">License</a>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </footer> --}}
  </div>
  <!--   Core JS Files   -->
  @include('frontend/js')

  <script>
    $(document).keypress(function(event){
      var keycode = (event.keyCode ? event.keyCode : event.which);
      if(keycode == '13'){
        signin()
      }
    });


    $( "#signin" ).click(function() {
      signin()
    });

    function signin(){
      let signin_data = {
        'email':$('#email').val(),
        'password':$('#password').val(),
        'remember':$('#rememberMe').is(':checked')?1:0,
        "_token": "{{ csrf_token() }}"
      }

      $.ajax({
        method: "POST",
        url: "{{url('user/signin')}}",
        data: signin_data,
        success: function(response) {
          if(response.code == 200 ){
            const urlParams = new URLSearchParams(window.location.search);
            const origin = urlParams.get('origin')
            if(origin){
              window.location.href = "{{url('/')}}/"+origin+"";
            }
            else{
              window.location.href = "{{url('/dashboard/commissions')}}";
            }
          }
          else{
            toastr.error(response.message, 'Error',{"positionClass": "toast-top-center",})
          }

        }
      });
    }
  </script>

</body>



</html>


