
<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" itemscope itemtype="http://schema.org/WebPage">

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <title>
    Elda Solar Studio
  </title>
  @include('frontend/css')
</head>

<body class="coworking bg-gray-200">
  <!-- Navbar -->
  @include('frontend/topnav')
  <!-- -------- START HEADER 1 w/ text and image on right ------- -->
  {{-- <header>
    <div class="page-header min-vh-80" style="background-image: url('{{asset('/image/pia_bg_4k.jpg')}}');" loading="lazy">
      <span class="mask bg-gradient-dark opacity-0"></span>
      <div class="container">
        <div class="row">
          <div class="col-lg-6 col-md-7 d-flex justify-content-center text-md-start text-center flex-column mt-sm-0 mt-7">
            <h1 class="text-white">Elda Solar Studio</h1>
            <p class="lead pe-md-5 me-md-5 text-white opacity-8">A journey of a thousand miles begins with a single step. Start your vtuber adventure with us today.</p>
            <div class="buttons">
              <a href="{{url('/request')}}" type="button" class="btn bg-gradient-info mt-4">Request Now</a>

            </div>
          </div>
        </div>
      </div>
    </div>
  </header> --}}
  <header class="position-relative">

    <div class="page-header min-vh-80" style="background-image: url('{{asset('/image/pia_bg_4k.jpg')}}');" loading="lazy">
      <span class="mask bg-gradient-dark opacity-2"></span>
      <div class="container">
        <div class="row">
          <div class="col-lg-6 col-md-7 d-flex justify-content-center text-md-start text-center flex-column mt-sm-0 mt-7">
            <h1 class="text-white">Your adventure starts here.</h1>
            <p class="lead pe-md-5 me-md-5 text-white opacity-8"></p>
            <div class="buttons">
              {{-- <a href="{{url('/request')}}" type="button" class="btn bg-gradient-info mt-4">Request Now</a> --}}
              {{-- <button type="button" class="btn text-white shadow-none mt-4">Read more</button> --}}
            </div>
          </div>
        </div>
      </div>
    </div>
  </header>
  <!-- -------- END HEADER 1 w/ text and image on right ------- -->
  <div class="card card-body blur shadow-blur mx-2 mx-lg-3 mt-n6 mb-4">
    <section class="py-7">
      <div class="container">
        <div class="row">
          <div class="col-lg-6">
            <h3 class="mt-4">Select Your Request Type</h3>
            {{-- <p>We understand the difficulty of finding the right people to start your adventure as a Vtuber, that is exactly why we exist: to make the behind-the-scene work easy so you can focus on putting on a show.</p> --}}
          </div>
        </div>
        <div class="row row-eq-height mt-4">
          <div class="col-lg-6 mb-lg-0 mb-4">
            <a class="request-link" href="{{url('/form/model')}}">
              <div class="out-border border-radius-lg aspect-ratio-16-9 position-relative">
                <div class="width"></div>
                <div class="height"></div>
                <div class="overflow-hidden inner border-radius-lg w-100 h-100">
                  <div class="card border-radius-lg overflow-hidden text-center request-card w-100 h-100">
                    <div class="bg-dark-blur-xl z-index-3 border-radius-lg request-layer text-start">
                      <div class="mt-auto position-absolute" style="bottom:0">
                        <h3 class="text-white request-title mx-lg-4 mx-2 mt-4">L2D Model</h3>
                        <p class="text-white request-txt mx-lg-4 mx-2 mt-1 w-lg-60 w-md-100">With some of the best craftsmans and attention to detail, our team will help you create a stunning model that will leave a lasting impression. </p>
                        {{-- <p class="text-white request-txt mx-lg-4 mx-2 mt-1 w-lg-60 w-md-100">Transform your digital presence with our Live2D model!  With some of the best craftsmans and attention to detail, our team will help you create a stunning model that will leave a lasting impression. </p> --}}
                      </div>
                    </div>
                    <img class="w-100 h-100 object-fit-cover" src="{{asset('/image/eldasolar_breakdown.jpg')}}">
                  </div>
                </div>

              </div>
              {{-- <div class="card border-radius-lg overflow-hidden text-center aspect-ratio-16-9 request-card">
               <div class="w-100 ">
                <div class="bg-dark-blur-xl w-100 h-100 position-absolute z-index-3 border-radius-lg request-layer">
                  <div class="border inner-border border-radius-lg">

                  </div>
                  <h2 class="text-white absolute-center z-index-3 request-title">L2D Model</h2>
                </div>
                <img class="w-100 h-100 object-fit-cover" src="{{asset('/image/eldasolar_breakdown.jpg')}}">
               </div>

              </div> --}}
            </a>
          </div>
          <div class="col-lg-6 mb-lg-0 mb-4">

            <a class="l2d-link" href="{{url('/form/rig')}}">
              <div class="out-border border-radius-lg aspect-ratio-16-9 position-relative">
                <div class="width"></div>
                <div class="height"></div>
                <div class="overflow-hidden inner border-radius-lg w-100 h-100">
                  <div class="card border-radius-lg overflow-hidden text-center request-card w-100 h-100">
                    <div class="bg-dark-blur-xl z-index-3 border-radius-lg request-layer text-start">
                      <div class="mt-auto position-absolute" style="bottom:0">
                        <h3 class="text-white request-title mx-lg-4 mx-2 mt-4">L2D Rig</h3>
                        <p class="text-white request-txt mx-lg-4 mx-2 mt-1 w-lg-60 w-md-100">We will bring your character to life with fluid and dynamic animations that will captivate your audience. </p>
                        {{-- <p class="text-white request-txt mx-lg-4 mx-2 mt-1 w-lg-60 w-md-100">We will bring your character to life with fluid and dynamic animations that will captivate your audience. Pick a tier that fits you, or add additional details to show off your styles, we can make them happen! </p> --}}
                      </div>
                    </div>
                    <div class="h-100 position-absolute">
                      <canvas id="live2d"  class="w-100 z-index-3"></canvas>
                    </div>
                  </div>
                </div>

              </div>
              {{-- <div class="out-border border border-radius-lg p-2 aspect-ratio-16-9 shadow-lg">
                <div class="card border-radius-lg overflow-hidden text-center request-card w-100 h-100">
                  <div class="w-100 ">
                    <div class="bg-dark-blur-xl z-index-3 border-radius-lg request-layer">
                      <h2 class="text-white text-start z-index-3 request-title">L2D Rig</h2>
                    </div>
                    <div class="h-100 position-absolute">
                      <canvas id="live2d"  class="w-100 z-index-3"></canvas>
                    </div>
                  </div>
                </div>
              </div> --}}

            </a>
          </div>
          {{-- <div class="col-lg-6">
            <a href="javascript:;">
              <div class="card card-background h-100">
                <div class="full-background" style="background-image: url({{asset('/image/raingirl_2_sm_cut.jpg')}})"></div>
                <div class="card-body pt-5 pt-lg-12">
                  <h4 class="text-white">Evocation</h4>
                  <p class="text-white">Supportive magic is just as important as offensive ones, and we are here to provide as much of the support on your goal as to our promise in quality. We offer competitive pricing, on-time delivery, and various flexible plans to suit your specific needs.</p>
                </div>
              </div>
            </a>
          </div> --}}
        </div>
        <div class="row row-eq-height mt-4">
          <div class="col-lg-6 mb-lg-0 mb-4">
            <a class="request-link" href="{{url('/form/design')}}">
              <div class="out-border border-radius-lg aspect-ratio-16-9 position-relative">
                <div class="width"></div>
                <div class="height"></div>
                <div class="overflow-hidden inner border-radius-lg w-100 h-100">
                  <div class="card border-radius-lg overflow-hidden text-center request-card w-100 h-100" style="background-image:url({{asset('/image/web_draft_bg.png')}});background-position:center;background-size:cover;">
                    <div class="bg-dark-blur-xl z-index-3 border-radius-lg request-layer text-start">
                      <div class="mt-auto position-absolute" style="bottom:0">
                        <h3 class="text-white request-title mx-lg-4 mx-2 mt-4">Character Design</h3>
                        <p class="text-white request-txt mx-lg-4 mx-2 mt-1 w-lg-60 w-md-100">We craft strong character design, so you can create memorable and relatable characters that connect with your audience on a deeper level. </p>
                        {{-- <p class="text-white request-txt mx-lg-4 mx-2 mt-1 w-lg-60 w-md-100">Character design is the backbone and soul of any live2d model. It's the magic that brings characters to life and captivates audiences. We can craft strong character design, so you can create memorable and relatable characters that connect with your audience on a deeper level, and leave a lasting impression. </p> --}}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </a>
          </div>
          <div class="col-lg-6 mb-lg-0 mb-4">
            <a class="request-link" href="{{url('/form/illu')}}">
              <div class="out-border border-radius-lg aspect-ratio-16-9 position-relative">
                <div class="width"></div>
                <div class="height"></div>
                <div class="overflow-hidden inner border-radius-lg w-100 h-100"">
                  <div class="card border-radius-lg overflow-hidden text-center request-card w-100 h-100">
                    <div class="bg-dark-blur-xl z-index-3 border-radius-lg request-layer text-start">
                      <div class="mt-auto position-absolute" style="bottom:0">
                        <h3 class="text-white request-title mx-lg-4 mx-2 mt-4">Illustration</h3>
                        <p class="text-white request-txt mx-lg-4 mx-2 mt-1 w-lg-60 w-md-100">We specialize in creating engaging and unique illustrations, including headshots, half body, full body, and more to meet all your needs. </p>
                        {{-- <p class="text-white request-txt mx-lg-4 mx-2 mt-1 w-lg-60 w-md-100">Looking to add some personality and life to your branding? Look no further! We specialize in creating engaging and unique illustrations, including headshots, half body, full body, bust up, with backgrounds, and even animated options. Let's work together to bring your vision to life and make a lasting impression on your audience. </p> --}}
                      </div>
                    </div>
                    <video id="illu_video" class="z-index-4 w-100 h-100 absolute-center border-radius-lg" style="object-fit:cover" playsinline="playsinline" muted="unmute" loop="loop" loading="lazy">
                      <source src="{{asset('storage/background-5_x264.mp4')}}" type="video/mp4">
                    </video>
                  </div>
                </div>

              </div>


            </a>
          </div>
          {{-- <div class="col-lg-6">
            <a href="javascript:;">
              <div class="card card-background h-100">
                <div class="full-background" style="background-image: url({{asset('/image/raingirl_2_sm_cut.jpg')}})"></div>
                <div class="card-body pt-5 pt-lg-12">
                  <h4 class="text-white">Evocation</h4>
                  <p class="text-white">Supportive magic is just as important as offensive ones, and we are here to provide as much of the support on your goal as to our promise in quality. We offer competitive pricing, on-time delivery, and various flexible plans to suit your specific needs.</p>
                </div>
              </div>
            </a>
          </div> --}}
        </div>
        <div class="row row-eq-height mt-4">
          <div class="col-lg-6 mb-lg-0 mb-4">
            <a class="request-link" href="{{url('/form/emote')}}">
              <div class="out-border border-radius-lg aspect-ratio-16-9 position-relative">
                <div class="width"></div>
                <div class="height"></div>
                <div class="overflow-hidden inner border-radius-lg w-100 h-100">
                  <div class="card border-radius-lg overflow-hidden text-center request-card w-100 h-100">
                    <div class="bg-dark-blur-xl z-index-3 border-radius-lg request-layer text-start">
                      <div class="mt-auto position-absolute" style="bottom:0">
                        <h3 class="text-white request-title mx-lg-4 mx-2 mt-4">Emote</h3>
                        <p class="text-white request-txt mx-lg-4 mx-2 mt-1 w-lg-60 w-md-100">Let us help you create unique and personalized emotes that truly reflect your brand or personality.  </p>
                        {{-- <p class="text-white request-txt mx-lg-4 mx-2 mt-1 w-lg-60 w-md-100">Let us help you create unique and personalized emotes that truly reflect your brand or personality. From static emotes to eye-catching animated ones, our artists will help you bring your vision to life. </p> --}}
                      </div>
                    </div>
                    <img class="w-50 h-100 object-fit-cover mx-auto" src="{{asset('/image/pia_emote/ok.gif')}}">
                  </div>
                </div>
              </div>
            </a>
          </div>
          <div class="col-lg-6 mb-lg-0 mb-4">
            <a class="request-link" href="{{url('/form/other')}}">
              <div class="out-border border-radius-lg aspect-ratio-16-9 position-relative">
                <div class="width"></div>
                <div class="height"></div>
                <div class="overflow-hidden inner border-radius-lg w-100 h-100">
                  <div class="card border-radius-lg overflow-hidden text-center request-card w-100 h-100">
                    <div class="bg-dark-blur-xl z-index-3 border-radius-lg request-layer text-start">
                      <div class="mt-auto position-absolute" style="bottom:0">
                        <h3 class="text-white request-title mx-lg-4 mx-2 mt-4">Others</h3>
                        <p class="text-white request-txt mx-lg-4 mx-2 mt-1 w-lg-60 w-md-100"> Any other requests, overlays, stream assets, or even stylized chat, we got them all. </p>
                        {{-- <p class="text-white request-txt mx-lg-4 mx-2 mt-1 w-lg-60 w-md-100"> Looking to elevate your game in streaming? We can help with personalized stream overlay to add a professional and eye-catching touch to your content. Whether you're a beginner or an experienced streamer, our experienced artists can create a unique and personalized overlay that perfectly matches your brand and style. </p> --}}
                      </div>
                    </div>
                    <img class="w-1000 h-100 object-fit-cover" src="{{asset('/image/pia_bg2_1080.jpg')}}">
                  </div>
                </div>
              </div>

            </a>
          </div>
          {{-- <div class="col-lg-6">
            <a href="javascript:;">
              <div class="card card-background h-100">
                <div class="full-background" style="background-image: url({{asset('/image/raingirl_2_sm_cut.jpg')}})"></div>
                <div class="card-body pt-5 pt-lg-12">
                  <h4 class="text-white">Evocation</h4>
                  <p class="text-white">Supportive magic is just as important as offensive ones, and we are here to provide as much of the support on your goal as to our promise in quality. We offer competitive pricing, on-time delivery, and various flexible plans to suit your specific needs.</p>
                </div>
              </div>
            </a>
          </div> --}}
        </div>

      </div>
    </section>





  </div>
  <div class="position-fixed min-vh-100 min-vw-100 justify-content-center align-items-center" style="display:flex; top:0;" id="loading_screen">
    <div class="lds-spinner"><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div></div>
  </div>
  @include('frontend/footer')
  @include('frontend/js')
</body>


<script src="{{asset('/live2d/core/live2dcubismcore.js')}}"></script>
<script src="{{asset('/live2d/plugins/pixi.min.js')}}"></script>
<script src="{{asset('/live2d/plugins/cubism4.min.js')}}"></script>
<script>
  const live2d = PIXI.live2d;

  const cubism4Model ="{{asset('/models/pia/PIA.model3.json')}}";
  (async function main() {
      const canvasElement = document.getElementById("live2d");
      const app = new PIXI.Application({
          view:canvasElement,
          autoStart: true,
          height:800,
          autoResize: false,
          backgroundAlpha:0,
      });

      const model = await PIXI.live2d.Live2DModel.from(cubism4Model,{
        autoInteract: false,
        // preserveExpressionOnMotion:false
      });


      // canvasElement.addEventListener('pointermove', (event) => model.focus(event.clientX, event.clientY));
      // canvasElement.addEventListener('pointerdown', (event) => model.tap(event.clientX, event.clientY));

      app.stage.addChild(model);
      model.scale.set(0.5,0.5);
      model.x = -450;
      model.y = -300;


      $('.l2d-link').mouseenter(function() {
        model.autoInteract = true;
        model.motion('play', 0);
        // $(this).find('.request-layer').css({'backdrop-filter':'blur( 0px )','background':'rgba( 0, 0, 0, 0.1)'});
        // $(this).find('.request-title').css({'top':'-120%'})
      });

      $('.l2d-link').mouseleave(function() {

        // model.internalModel.motionManager.stopAllMotions();
        // $(this).find('.request-layer').css({'backdrop-filter':'blur( 15px )','background':'rgba( 0, 0, 0, 0.7)'});
        // $(this).find('.request-title').css({'top':'0'})
      });

      // $( "#cry" ).click(function() {
      //   model.expression();
      // });

      // $( "#angry" ).click(function() {
      //     model.expression('angry');
      // });

      // $( "#smile" ).click(function() {
      //     model.expression('shy');
      // });
  })();

</script>
<script>
  $( window ).ready(function() {
    $('#loading_screen').fadeOut(500);
    $('.request-link').mouseenter(function() {
      $(this).find('video').trigger('play');
      // $(this).find('.request-layer').css({'backdrop-filter':'blur( 0px )','background':'rgba( 0, 0, 0, 0.1)'});
      // $(this).find('.request-title').css({'top':'-120%'})
    });
    $('.request-link').mouseleave(function() {
      $(this).find('video').trigger('pause');
      // $(this).find('.request-layer').css({'backdrop-filter':'blur( 15px )','background':'rgba( 0, 0, 0, 0.7)'});
      // $(this).find('.request-title').css({'top':'0'})
    });
  });
</script>

</html>
