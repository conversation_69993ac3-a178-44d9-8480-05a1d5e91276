
<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" itemscope itemtype="http://schema.org/WebPage">

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <title>
    Elda Solar Studio
  </title>
  @include('frontend/css')
</head>

<body class="coworking bg-gray-200">
  <!-- Navbar -->
  @include('frontend/topnav')
  <!-- -------- START HEADER 1 w/ text and image on right ------- -->
  <header>
    <div class="page-header min-vh-75" style="background-image: url('{{asset('/image/pia_bg_4k.jpg')}}');" loading="lazy">
      <span class="mask bg-gradient-dark opacity-5"></span>
      <div class="container">
        <div class="row">
          <div class="col-lg-6 col-md-7 d-flex justify-content-center text-md-start text-center flex-column mt-sm-0 mt-7">
            <h1 class="text-white">Your adventure starts here.</h1>
            <p class="lead pe-md-5 me-md-5 text-white opacity-8">The time is now for it be okay to be great. People in this world shun people for being nice.</p>
            <div class="buttons">
              {{-- <button type="button" class="btn bg-gradient-primary mt-4">Get Started</button>
              <button type="button" class="btn text-white shadow-none mt-4">Read more</button> --}}
            </div>
          </div>
        </div>
      </div>
    </div>
  </header>
  <!-- -------- END HEADER 1 w/ text and image on right ------- -->
  <div class="card card-body blur shadow-blur mx-2 mx-lg-3 mt-n6 mb-4">
    <!-- -------- START Features w/ 4 cols w/ colored icon & title & text -------- -->
    <section>
      <div class="container py-4">

        <div class="row">
          <div class="col-lg-12 mx-auto d-flex justify-content-center flex-column">
            @if(true)
            <h3 class="text-center mb-5 commission-form-content">Emote Request Form</h3>
            <div class="card mt-5 commission-form-content">
              <div class="card-header p-0 position-relative mt-n5 mx-3 z-index-2">
                  <div class="bg-gradient-secondary shadow-secondary border-radius-lg pt-4 pb-3">
                    <div class="multisteps-form__progress">
                        <button class="multisteps-form__progress-btn js-active" type="button" title="Request Type">
                            <span>1. Basic Info</span>
                        </button>
                        <button class="multisteps-form__progress-btn" type="button" title="Basic Info">2. Emote Detail</button>
                        {{-- <button class="multisteps-form__progress-btn" type="button" title="Socials">3. Animation Detail</button> --}}
                        {{-- <button class="multisteps-form__progress-btn" type="button" title="Pricing">4. L2d Rigging</button> --}}
                    </div>
                  </div>
              </div>
              <div class="card-body">
                <form class="multisteps-form__form">
                <!--single form panel-->
                <div class="multisteps-form__panel pt-3 border-radius-xl bg-white js-active" data-animation="FadeIn">
                  <h5 class="font-weight-bolder">Basic Infomation </h5>
                  <div class="multisteps-form__content">
                  <div class="row mt-5">
                      <div class="col-6">
                        <div class="input-group input-group-dynamic">
                            <label class="form-label">Client Name</label>
                            <input class="multisteps-form__input form-control" id="client_name" type="text" title="Your name, or the name you wish to be called."/>
                        </div>
                        <div class="input-group input-group-dynamic mt-5">
                          <label class="form-label">Email</label>
                          <input class="multisteps-form__input form-control" id="email" type="text" />
                        </div>
                        <div class="input-group input-group-dynamic mt-5">
                          <label class="form-label">Social Link</label>
                          <input class="multisteps-form__input form-control" id="social_link" type="text" title="Any social media where we can reach you."/>
                        </div>

                      </div>
                      <div class="col-6">
                        <div class="input-group input-group-dynamic">
                          <label class="form-label">Commission Name</label>
                          <input class="multisteps-form__input form-control" id="project_name" type="text" title="Simple name helps you differentiate commission requests."/>
                        </div>
                        <div class="input-group input-group-dynamic mt-5">
                          <label class="form-label">Budget (USD)</label>
                          <input class="multisteps-form__input form-control" id="budget" type="number" />
                        </div>
                        <div class="input-group input-group-dynamic mt-5">
                          <div class="input-group input-group-static">
                            <input class="form-control datepicker" placeholder="Deadline" type="text" id="deadline">
                          </div>
                        </div>
                      </div>
                  </div>
                  <div class="row">
                      <div class="button-row d-flex mt-4 col-12">
                        <button class="btn bg-gradient-dark ms-auto mb-0 js-btn-next" type="button" title="Next">Next</button>
                      </div>
                  </div>
                  </div>
                </div>
                <!--single form panel-->
                <div class="multisteps-form__panel pt-3 border-radius-xl bg-white" data-animation="FadeIn">
                  <h5 class="font-weight-bolder">Emote Detail</h5>
                  <div class="multisteps-form__content">
                    <div class="row mt-5 model-need">
                      <div class="col-6">
                        <div class="input-group input-group-static">
                          <label class=""># of Static Emotes</label>
                          <input class="form-control mt-3" id="static_emote_num" type="number" placeholder="Please enter the number of static emotes need"/>
                        </div>
                        {{-- <select class="form-control choice mt-3" name="choices-category" id="illu_type">
                          <option value="Full Body" selected="">Full Body</option>
                          <option value="Half Body" >Half Body</option>
                          <option value="Half Body" >Bust Up</option>
                        </select> --}}
                      </div>
                      <div class="col-6">
                        <div class="input-group input-group-static">
                          <label class=""># of Animated Emotes</label>
                          <input class="form-control mt-3" id="animate_emote_num" type="number" placeholder="Please enter the number of animated emotes need"/>
                        </div>
                      </div>
                    </div>

                    <div class="row mt-3 model-need">
                      <h6 class="mt-4">Detail Requirements</h6>
                      <div class="col-12">
                        <div class="input-group input-group-dynamic">
                            {{-- <label class="form-label">Gender</label> --}}
                            <textarea class="form-control" id="emote_detail" rows="4" placeholder="Please enter the detail for your emotes request."></textarea>
                        </div>
                      </div>
                    </div>
                    
                    <div class="row mt-3 model-need">
                      <h6 class="mt-4">Reference</h6>
                      <div class="col-12">
                        <label class="form-control mb-0">Upload Refrence Files</label>
                        <div action="" class="form-control border dropzone dz-clickable" id="emote_ref" file-type="emotes">
                          <div class="dz-default dz-message"><button class="dz-button" type="button">Drop files here to upload</button>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="button-row d-flex mt-4">
                      <button class="btn bg-gradient-light mb-0 js-btn-prev" type="button" title="Prev">Prev</button>
                      <button class="btn bg-gradient-success ms-auto mb-0" type="button" title="Next" id="finish">Finish</button>
                    </div>
                  </div>
                </div>
                </form>
              </div>
            </div>

            
            <div class="row my-7 ty-message d-none-ni">
              <div class="col-lg-12 m-auto text-center">
                <img src="{{asset('image/pia_emote/ty.png')}}" class="w-15">
                <h2 class="text mt-3">Thank You For Your Request</h2>
                <p class="lead">You can go to <a href="{{url('dashboard/commissions')}}">Dashboard</a> to view your submited request</p>
                {{-- <p>If you wish to start a new request, you can delete your preious request and submit again</p> --}}
              </div>
            </div>
            
            @else
            <div class="row my-7 ty-message">
              <div class="col-lg-12 m-auto text-center">
                <img src="{{asset('image/pia_emote/ty.png')}}" class="w-15">
                <h2 class="text mt-3">Thank You For Your Request</h2>
                <p class="lead">You can go to <a href="{{url('dashboard/commissions')}}">Dashboard</a> to view your submited request</p>
                {{-- <p>If you wish to start a new request, you can delete your preious request and submit again</p> --}}
              </div>
            </div>
            @endif
          </div>
        </div>
      </div>
    </section>
    {{-- FAQ --}}
    {{-- <section class="py-4">
      <div class="container">
          <div class="row my-5">
          <div class="col-md-6 mx-auto text-center">
              <h2>Frequently Asked Questions</h2>
              <p>A lot of people don&#39;t appreciate the moment until it’s passed. I&#39;m not trying my hardest, and I&#39;m not trying to do </p>
          </div>
          </div>
          <div class="row">
          <div class="col-md-10 mx-auto">
              <div class="accordion" id="accordionRental">
              <div class="accordion-item mb-3">
                  <h5 class="accordion-header" id="headingOne">
                  <button class="accordion-button border-bottom font-weight-bold" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                      What are the Levels
                      <i class="collapse-close fa fa-plus text-xs pt-1 position-absolute end-0"></i>
                      <i class="collapse-open fa fa-minus text-xs pt-1 position-absolute end-0"></i>
                  </button>
                  </h5>
                  <div id="collapseOne" class="accordion-collapse collapse show" aria-labelledby="headingOne" data-bs-parent="#accordionRental">
                  <div class="accordion-body text-sm opacity-8">
                      We’re not always in the position that we want to be at. We’re constantly growing. We’re constantly making mistakes. We’re constantly trying to express ourselves and actualize our dreams. If you have the opportunity to play this game
                      of life you need to appreciate every moment. A lot of people don’t appreciate the moment until it’s passed.
                  </div>
                  </div>
              </div>
              <div class="accordion-item mb-3">
                  <h5 class="accordion-header" id="headingTwo">
                  <button class="accordion-button border-bottom font-weight-bold" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                      How can i make the payment?
                      <i class="collapse-close fa fa-plus text-xs pt-1 position-absolute end-0"></i>
                      <i class="collapse-open fa fa-minus text-xs pt-1 position-absolute end-0"></i>
                  </button>
                  </h5>
                  <div id="collapseTwo" class="accordion-collapse collapse" aria-labelledby="headingTwo" data-bs-parent="#accordionRental">
                  <div class="accordion-body text-sm opacity-8">
                      It really matters and then like it really doesn’t matter. What matters is the people who are sparked by it. And the people who are like offended by it, it doesn’t matter. Because it&#39;s about motivating the doers. Because I’m here to follow my dreams and inspire other people to follow their dreams, too.
                      <br>
                      We’re not always in the position that we want to be at. We’re constantly growing. We’re constantly making mistakes. We’re constantly trying to express ourselves and actualize our dreams. If you have the opportunity to play this game of life you need to appreciate every moment. A lot of people don’t appreciate the moment until it’s passed.
                  </div>
                  </div>
              </div>
              <div class="accordion-item mb-3">
                  <h5 class="accordion-header" id="headingThree">
                  <button class="accordion-button border-bottom font-weight-bold" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                      How much time does it take to receive the order?
                      <i class="collapse-close fa fa-plus text-xs pt-1 position-absolute end-0"></i>
                      <i class="collapse-open fa fa-minus text-xs pt-1 position-absolute end-0"></i>
                  </button>
                  </h5>
                  <div id="collapseThree" class="accordion-collapse collapse" aria-labelledby="headingThree" data-bs-parent="#accordionRental">
                  <div class="accordion-body text-sm opacity-8">
                      The time is now for it to be okay to be great. People in this world shun people for being great. For being a bright color. For standing out. But the time is now to be okay to be the greatest you. Would you believe in what you believe in, if you were the only one who believed it?
                      If everything I did failed - which it doesn&#39;t, it actually succeeds - just the fact that I&#39;m willing to fail is an inspiration. People are so scared to lose that they don&#39;t even try. Like, one thing people can&#39;t say is that I&#39;m not trying, and I&#39;m not trying my hardest, and I&#39;m not trying to do the best way I know how.
                  </div>
                  </div>
              </div>
              <div class="accordion-item mb-3">
                  <h5 class="accordion-header" id="headingFour">
                  <button class="accordion-button border-bottom font-weight-bold" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFour" aria-expanded="false" aria-controls="collapseFour">
                      Can I resell the products?
                      <i class="collapse-close fa fa-plus text-xs pt-1 position-absolute end-0"></i>
                      <i class="collapse-open fa fa-minus text-xs pt-1 position-absolute end-0"></i>
                  </button>
                  </h5>
                  <div id="collapseFour" class="accordion-collapse collapse" aria-labelledby="headingFour" data-bs-parent="#accordionRental">
                  <div class="accordion-body text-sm opacity-8">
                      I always felt like I could do anything. That’s the main thing people are controlled by! Thoughts- their perception of themselves! They&#39;re slowed down by their perception of themselves. If you&#39;re taught you can’t do anything, you won’t do anything. I was taught I could do everything.
                      <br><br>
                      If everything I did failed - which it doesn&#39;t, it actually succeeds - just the fact that I&#39;m willing to fail is an inspiration. People are so scared to lose that they don&#39;t even try. Like, one thing people can&#39;t say is that I&#39;m not trying, and I&#39;m not trying my hardest, and I&#39;m not trying to do the best way I know how.
                  </div>
                  </div>
              </div>
              <div class="accordion-item mb-3">
                  <h5 class="accordion-header" id="headingFifth">
                  <button class="accordion-button border-bottom font-weight-bold" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFifth" aria-expanded="false" aria-controls="collapseFifth">
                      Where do I find the shipping details?
                      <i class="collapse-close fa fa-plus text-xs pt-1 position-absolute end-0"></i>
                      <i class="collapse-open fa fa-minus text-xs pt-1 position-absolute end-0"></i>
                  </button>
                  </h5>
                  <div id="collapseFifth" class="accordion-collapse collapse" aria-labelledby="headingFifth" data-bs-parent="#accordionRental">
                  <div class="accordion-body text-sm opacity-8">
                      There’s nothing I really wanted to do in life that I wasn’t able to get good at. That’s my skill. I’m not really specifically talented at anything except for the ability to learn. That’s what I do. That’s what I’m here for. Don’t be afraid to be wrong because you can’t learn anything from a compliment.
                      I always felt like I could do anything. That’s the main thing people are controlled by! Thoughts- their perception of themselves! They&#39;re slowed down by their perception of themselves. If you&#39;re taught you can’t do anything, you won’t do anything. I was taught I could do everything.
                  </div>
                  </div>
              </div>
              </div>
          </div>
          </div>
      </div>
    </section> --}}
  </div>
  @include('frontend/footer')
  @include('frontend/js')
  <script src="{{asset('dashboard/js/plugins/choices.min.js')}}"></script>
  <script src="{{asset('dashboard/js/plugins/multistep-form.js')}}"></script>
  <script src="{{asset('dashboard/js/plugins/dropzone.min.js')}}"></script>
  <script src="{{asset('dashboard/js/plugins/flatpickr.min.js')}}"></script>

  <script>
      $('.choice').each(function(i, obj) {
        const example = new Choices(obj, {
          searchEnabled: false,
          shouldSort: false,
        });
      });

      // $("#cd_need_select").change(function() {
      //   if($(this).find(":selected").val() == 1){
      //     $('.cd-need').show()
      //     setFormHeight()
      //   }
      //   else if($(this).find(":selected").val() == 2){
      //     $('.cd-need').hide()
      //     $('.cd-ref').show()
      //     setFormHeight()
      //   }
      //   else if($(this).find(":selected").val() == 0){
      //     $('.cd-need').hide()
      //     $('.cd-ref').hide()
      //     setFormHeight()
      //   }
      // });

      $("#ani_need_select").change(function() {
        if($(this).find(":selected").val() == 1){
          $('.ani-need').show()
          setFormHeight()
        }
        else if($(this).find(":selected").val() == 0){
          $('.ani-need').hide()
          setFormHeight()
        }
      });

      // $("#rig_need_select").change(function() {
      //   if($(this).find(":selected").val() == 1){
      //     $('.rig-need').show()
      //     setFormHeight()
      //   }
      //   else if($(this).find(":selected").val() == 0){
      //     $('.rig-need').hide()
      //     setFormHeight()
      //   }
      // });

      flatpickr(".datepicker");
      // var uploaded_files = {};
      var uploaded_files = [];
      Dropzone.autoDiscover = false;
      var dropzone = $('.dropzone').dropzone({
        url: '{{url('user/request/refrence')}}',
        method: 'post',
        addRemoveLinks: true,
        headers: {
            'X-CSRF-TOKEN': "{{ csrf_token() }}"
        },
        init: function() {
          this.on('sending', function(file, xhr, formData){
            let dropzone_id = this.element.getAttribute('file-type');
            formData.append('dropzone_id', dropzone_id);
          });
          this.on("addedfile", file => {
            setFormHeight()
          });
          this.on("removedfile", file => {
            setFormHeight()
            let file_name = file.previewElement.id;
            let file_index = uploaded_files.indexOf(file_name);
            if(file_index > -1){
              uploaded_files.splice(file_index,1)
            }
            console.log(uploaded_files);
            
            
          });
          this.on("success", function(file, response){
            // let dropzone_id = this.element.getAttribute('id');
            // if(uploaded_files[dropzone_id]){
            //   uploaded_files[dropzone_id].push(response['file_name'])
            // }
            // else{
            //   uploaded_files[dropzone_id] = [];
            //   uploaded_files[dropzone_id].push(response['file_name'])
            // }
            
            uploaded_files.push(response['file_name'])
            file.previewElement.id  = response['file_name'];
            console.log(uploaded_files);
           
          });
        }
      });


      $( "#finish" ).click(function() {
        let request_data = {
          "_token": "{{ csrf_token() }}",
          'type':"Emotes",
          "client_name":$('#client_name').val(),
          'email':$('#email').val(),
          'social_link':$('#social_link').val(),
          'project_name':$('#project_name').val(),
          'budget':$('#budget').val(),
          'deadline':$('#deadline').val(),

          'static_emote_num':$('#static_emote_num').val(),
          'animate_emote_num':$('#animate_emote_num').val(),
          'emote_detail':$('#emote_detail').val(),

          // 'need_cd':$('#cd_need_select').val(),
          // 'gender':$('#gender').val(),
          // 'age':$('#age').val(),
          // 'height':$('#height').val(),
          // 'weight':$('#weight').val(),
          // 'body_shape':$('#body_shape').val(),
          // 'skin_color':$('#skin_color').val(),
          // 'race':$('#race').val(),
          // 'eyes':$('#eyes').val(),
          // 'hair':$('#hair').val(),
          // 'face':$('#face').val(),
          // 'outfit':$('#outfit').val(),
          // 'color_theme':$('#height').val(),
          // 'cd_other_detail':$('#cd_other_detail').val(),

          // 'need_model':$('#model_need_select').val(),
          // 'ma_lv':$('#ma_lv').val(),
          // 'ma_type':$('#ma_type').val(),
          // 'model_exp':$('#model_exp').val(),
          // 'model_ani':$('#model_ani').val(),
          // 'model_extra':$('#model_extra').val(),

          // 'need_rig':$('#rig_need_select').val(),
          // 'rig_lv':$('#rig_lv').val(),
          // 'rig_type':$('#rig_type').val(),
          // 'rig_exp':$('#rig_exp').val(),
          // 'rig_ani':$('#rig_ani').val(),
          // 'rig_extra':$('#rig_extra').val(),
          'uploaded_files':uploaded_files,
        }
        console.log(request_data);

        $.ajax({
            method: "POST",
            url: "{{url('user/request/submit')}}",
            data: request_data,
            success: function(response) {
              console.log(response);
              if(response.code == 200 ){
                $('.commission-form-content').hide();
                $('.ty-message').fadeIn(500);
              }
              // else{
              //   toastr.error(response.message, 'Error',{"positionClass": "toast-top-center",})
              // }
            }
        });
    });





      // dropzone.on("addedfile", function(file) {
      //   console.log(12312312);
      // });










  </script>
</body>

</html>
