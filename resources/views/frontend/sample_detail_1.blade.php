
<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" itemscope itemtype="http://schema.org/WebPage">

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <title>
    Elda Solar Studio
  </title>
  @include('frontend/css')
</head>

<body class="coworking bg-gray-200">
  <!-- Navbar -->
  @include('frontend/topnav')
  <!-- -------- START HEADER 1 w/ text and image on right ------- -->
  <header>
    <div class="page-header min-vh-75" style="background-image: url('{{asset('/image/bg3.jpg')}}');" loading="lazy">
      <span class="mask bg-gradient-dark opacity-5"></span>
      <div class="container">
        <div class="row">
          <div class="col-lg-6 col-md-7 d-flex justify-content-center text-md-start text-center flex-column mt-sm-0 mt-7">
            <h1 class="text-white">Check Our Finished Works</h1>
            <p class="lead pe-md-5 me-md-5 text-white opacity-8">We will have what you are looking for, can't find it? We will find it for you.</p>
            <div class="buttons">
              <a href="{{url('/request')}}" type="button" class="btn bg-gradient-info mt-4">Get Started</button>
              <a href="{{url('/sample')}}" type="button" class="btn text-white shadow-none mt-4">Read more</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </header>
  <!-- -------- END HEADER 1 w/ text and image on right ------- -->
  <div class="card card-body blur shadow-blur mx-2 mx-lg-3 mt-n6 mb-4">
    <div class="section my-4 my-lg-5">
      <div class="container">

        <div class="row flex-row">
          <div class="col-lg-6">
            <div class="text-center">
              <img class="w-50 border-radius-lg mx-auto" src="{{asset('image/kuroi_nbg_lg.png')}}" alt="ladydady" loading="lazy">
            </div>
            
            <div class="row p-0 mx-5 mt-3">
              <div class="col-4 square p-0">
                <a data-fancybox="artist-" href="{{asset('image/kuroi_nbg_lg.png')}}">
                  <img class="square-content shadow border-radius-lg" src="{{asset('image/kuroi_nbg_lg.png')}}" />
                </a>         
              </div>
              <div class="col-4 square p-0">
                <a data-fancybox="artist-detail-" href="{{asset('image/kuroi_xl.jpg')}}" >
                  <img class="square-content shadow border-radius-lg"  style="object-position: left" src="{{asset('image/kuroi_sm.jpg')}}" />
                </a>
              </div>
              <div class="col-4 square p-0">
                <a data-fancybox="artist-detail-" href="https://youtu.be/Hjy89t0FYjk">
                  <img src="{{asset('image/kuroi_preview.jpg')}}" class="square-content shadow border-radius-lg" style="object-position: right" />
                  <img src="{{asset('image/play_btn.png')}}" class="w-100 h-100 position-absolute z-index-2"/>
                </a>
              </div>
            </div>
          </div>
          <div class="col-lg-6">
            <div>
              <h3 class="mt-lg-0 mt-4">KUROI</h3>
              <span class="badge badge-success">LV1 MODEL & RIGGING</span>
              {{-- <h6 class="mb-0 mt-2">Estimate Cost</h6>
              <h5>$ 800</h5> --}}
              <div class="accordion" id="accordionProduct">
                <div class="accordion-item mb-1">
                  <h6 class="accordion-header" id="headingOne">
                    <button class="accordion-button border-bottom font-weight-bold text-start" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                      Project Infomation
                      <i class="collapse-close fa fa-plus text-xs pt-1 position-absolute end-0"></i>
                      <i class="collapse-open fa fa-minus text-xs pt-1 position-absolute end-0"></i>
                    </button>
                  </h6>
                  <div id="collapseOne" class="accordion-collapse collapse show" aria-labelledby="headingOne" >
                    <div class="accordion-body text-sm opacity-8">
                      Simple and adorable, Kuroi, who just started her days as a maid got a thing or two to show. She has a Basic LV1 Model and Rigging kit, with movements such as eye tracking and simple XYZ physics.
                    </div>
                  </div>
                </div>
                <div class="accordion-item mb-1">
                  <h6 class="accordion-header" id="headingTwo">
                    <button class="accordion-button border-bottom font-weight-bold text-start" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="true" aria-controls="collapseTwo">
                      Artists Information
                      <i class="collapse-close fa fa-plus text-xs pt-1 position-absolute end-0"></i>
                      <i class="collapse-open fa fa-minus text-xs pt-1 position-absolute end-0"></i>
                    </button>
                  </h6>
                  <div id="collapseTwo" class="accordion-collapse collapse show" aria-labelledby="headingTwo" >
                    <div class="accordion-body text-sm opacity-8">
                      <div class="row">
                        @foreach($lv1_artists as $lv1_artist)
                        <div class="col-lg-4 col-md-4 col-sm-6 col-6 text-left">
                          <div class="text-center" style="">
                            <a href="{{url('/artist').'/'.$lv1_artist->id}}" class="avatar avatar-xxl rounded-circle border border-info">
                              <img alt="Image placeholder" class="p-1" src="{{asset('storage/'.$lv1_artist->avatar)}}">
                            </a>
                            <p class="mb-0 text-md text-info bold">{{$lv1_artist->name}}</p>
                            {{-- <p class="mb-0 text-sm">Art & Rig</p> --}}
                          </div>
                        </div>
                        @endforeach
                      </div>
                      {{-- <div class="col-lg-4 col-md-4 col-sm-6 col-6 text-left">
                        <div class="text-center" style="">
                          <a href="{{url('/artist').'/'.$lv1_artist->id}}" class="avatar avatar-xxl rounded-circle border border-info">
                            <img alt="Image placeholder" class="p-1" src="{{asset('storage/'.$lv1_artist->avatar)}}">
                          </a>
                          <p class="mb-0 text-md text-info bold">{{$lv1_artist->name}}</p>
                          <p class="mb-0 text-sm">{{$lv1_artist->role}}</p>
                        </div>
                      </div> --}}
                    </div>
                  </div>
                </div>
                <div class="accordion-item mb-1">
                  <h6 class="accordion-header" id="headingThree">
                    <button class="accordion-button border-bottom font-weight-bold text-start" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="true" aria-controls="collapseThree">
                      Detail
                      <i class="collapse-close fa fa-plus text-xs pt-1 position-absolute end-0"></i>
                      <i class="collapse-open fa fa-minus text-xs pt-1 position-absolute end-0"></i>
                    </button>
                  </h6>
                  <div id="collapseThree" class="accordion-collapse collapse show" aria-labelledby="headingThree">
                    <div class="accordion-body text-sm opacity-8">
                      <ul>
                        {{-- <li>L2D Model Estimate Cost : $500</li>
                        <li>Rigging Estimate Cost : $300</li> --}}
                        <li>L2D Model Estimate Time : 14 days</li>
                        <li>Rigging Estimate Time : 14 days</li>
                      </ul>
                      {{-- <button class="btn bg-gradient-info mb-0 mt-lg-auto" type="button" name="button">View Model Detail</button> --}}
                    </div>
                  </div>
                </div>
              </div>
              {{-- <div class="row mt-5">
                <div class="col-lg-6 mt-lg-0 mt-2">
                  <label class="ms-0">Select Color</label>
                  <select class="form-control" name="choices-material" id="choices-material">
                    <option value="Choice 1" selected>Black</option>
                    <option value="Choice 2">Blue</option>
                    <option value="Choice 3">Gray</option>
                    <option value="Choice 4">Brown</option>
                  </select>
                </div>
                <div class="col-lg-6 mt-lg-0 mt-2">
                  <label class="ms-0">Select Size</label>
                  <select class="form-control" name="choices-colors" id="choices-colors">
                    <option value="Choice 1" selected>Small</option>
                    <option value="Choice 2">Extra Small</option>
                    <option value="Choice 3">Medium</option>
                    <option value="Choice 4">Large</option>
                    <option value="Choice 5">XXL</option>
                  </select>
                </div>
              </div> --}}
              <div class="row mt-4">
                <div class="col-lg-6 ms-auto text-end">
                  
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
    <!-- END Teams w/ 4 cards w/ image on left & descriptio/social link on right -->
    <!-- -------- END PRE-FOOTER 8 w/ TEXT, BG IMAGE AND 2 BUTTONS ------- -->
  </div>
  <script src="{{asset('/live2d/core/live2dcubismcore.js')}}"></script>
  <script src="{{asset('/live2d/plugins/pixi.min.js')}}"></script>
  <script src="{{asset('/live2d/plugins/cubism4.min.js')}}"></script>
  <script>
    // const live2d = PIXI.live2d;

    // const cubism4Model ="{{asset('/models/wolfgirl/wolfgirl.model3.json')}}";
    // (async function main() {
    //     const canvasElement = document.getElementById("live2d");
    //     const app = new PIXI.Application({
    //         view:canvasElement,
    //         autoStart: true,
    //         height:850,
    //         autoResize: false,
    //         backgroundColor: 000000,
    //         backgroundAlpha:0,
    //         preserveExpressionOnMotion:false
    //     });

    //     const model = await PIXI.live2d.Live2DModel.from(cubism4Model,{
    //       // autoInteract: false,
    //       // preserveExpressionOnMotion:false
    //     });
        

    //     // canvasElement.addEventListener('pointermove', (event) => model.focus(event.clientX, event.clientY));
    //     // canvasElement.addEventListener('pointerdown', (event) => model.tap(event.clientX, event.clientY));
        
    //     app.stage.addChild(model);
    //     model.scale.set(0.1);
    //     model.x = 0;

    //     // model.on('hit', hitAreaNames => {
    //     //   if (hitAreaNames.includes('Body')) {
    //     //       model.motion('TapBody', 3);
    //     //       console.log(11321321321);
    //     //   }
    //     // });

    //     $( "#cry" ).click(function() {
    //       model.expression();
    //     });

    //     $( "#angry" ).click(function() {
    //         model.expression('angry');
    //     });

    //     $( "#smile" ).click(function() {
    //         model.expression('shy');
    //     });

        










    // })();





</script>
  @include('frontend/footer')
  @include('frontend/js')
</body>

</html>
