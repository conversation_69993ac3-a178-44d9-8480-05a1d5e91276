
<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" itemscope itemtype="http://schema.org/WebPage">

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <title>
    EldaSolar Studio
  </title>
  @include('frontend/css')
</head>

<body class="coworking bg-gray-200">
  <!-- Navbar -->
  @include('frontend/topnav')
  <!-- -------- START HEADER 1 w/ text and image on right ------- -->

  <header>
    <div class="page-header min-height-500" style="background-image: url('{{$artist->cover ? Voyager::image($artist->cover) : asset('/image/raingirl_1_lg.jpg')}}');" loading="lazy">
      <span class="mask bg-gradient-dark opacity-2"></span>
    </div>
  </header>
  <!-- -------- <PERSON>ND HEADER 1 w/ text and image on right ------- -->
  <div class="card card-body blur shadow-blur mx-2 mx-lg-3 mt-n6 mb-4">
    <section class="py-sm-5 py-3 position-relative">
      <div class="container">
        <div class="row">
          <div class="col-12 mx-auto">
            <div class="mt-n7 mt-md-n8 text-center">
              <img class="avatar avatar-xxl shadow-xl position-relative z-index-2 blur" src="{{asset('storage/'.$artist->avatar)}}" alt="artist avatar" loading="lazy">
            </div>
            <div class="row py-5">
              <div class="col-lg-7 col-md-7 z-index-2 position-relative px-md-2 px-sm-5 mx-auto">
                <div class="d-flex justify-content-between align-items-center mb-2">
                  <h3 class="mb-0">{{$artist->name}}</h3>
                  <div class="d-block">
                    {{-- <div type="button" class="btn btn-sm btn-outline-info text-nowrap mb-0">Add To Commission</div> --}}
                  </div>
                </div>
                <div class="row">
                  @if($artist->link_twitter)
                  <div class="col-auto">
                    <a href="{{$artist->link_twitter}}" class="h6 text-info" target="_blank">
                      <i class="fa-brands fa-twitter"></i>
                    </a>
                  </div>
                  @endif
                  @if($artist->link_pixiv)
                  <div class="col-auto">
                    <a href="{{$artist->link_pixiv}}" class="h6 text-info" target="_blank">
                      <i class="fa-solid fa-p"></i>
                    </a>
                  </div>
                  @endif
                  @if($artist->link_weibo)
                  <div class="col-auto">
                    <a href="{{$artist->link_weibo}}" class="h6 text-info" target="_blank">
                      <i class="fa-brands fa-weibo"></i>
                    </a>
                  </div>
                  @endif
                  @if($artist->link_bilibili)
                  <div class="col-auto">
                    <a href="{{$artist->link_bilibili}}" class="h6 text-info" target="_blank">
                      <i class="fa-brands fa-bilibili"></i>
                    </a>
                  </div>
                  @endif
                  @if($artist->link_mihuashi)
                  <div class="col-auto">
                    <a href="#" class="h6 text-info">
                      <i class="fa-brands fa-octopus-deploy"></i>
                    </a>
                  </div>
                  @endif
                </div>
                <div class="row my-3">
                  <div class="col-12">
                    @foreach($tags as $tag)
                    <span class="badge bg-gradient-info">{{$tag->name}}</span>
                    @endforeach
                  </div>
                </div>
                <p class="text-lg mb-0">
                  {!!$artist->intro!!}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section class="">
      <div class="container">
        <div class="row">
          <div class="col-lg-6">
            <h3 class="">Portfolios</h3>
          </div>
        </div>
        <div class="row p-0">
          @foreach($artist->images as $image)
            <div class="square col-4 col-lg-3 mb-lg-0 mb-2 p-0 ">
              <a data-fancybox="artist-{{$artist->id}}" href="{{asset('storage/'.$image->image_url)}}">
                @if($image->small_url)
                <img class="square-content shadow border-radius-lg" src="{{asset('storage/'.$image->small_url)}}" />
                @else
                <img class="square-content shadow border-radius-lg" src="{{asset('storage/'.$image->image_url)}}" />
                @endif
              </a>
            </div>
          @endforeach
        </div>
      </div>
    </section>
    <!-- END Teams w/ 4 cards w/ image on left & descriptio/social link on right -->
    <!-- -------- END PRE-FOOTER 8 w/ TEXT, BG IMAGE AND 2 BUTTONS ------- -->
  </div>
  @include('frontend/footer')
  @include('frontend/js')
</body>

</html>
