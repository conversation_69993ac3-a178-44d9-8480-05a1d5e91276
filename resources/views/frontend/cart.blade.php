
<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" itemscope itemtype="http://schema.org/WebPage">

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <title>
    ESS Product
  </title>
  @include('frontend/css')
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@8/swiper-bundle.min.css"/>
</head>

<body class="coworking bg-gray-200">
  <!-- Navbar -->
  @include('frontend/topnav')
  <!-- -------- START HEADER 1 w/ text and image on right ------- -->
  <header>
    <div class="page-header min-vh-50" style="background-image: url('{{asset('/image/bg1.jpg')}}');background-position:middle" loading="lazy">
      <span class="mask bg-gradient-dark opacity-7"></span>
      <div class="container">
        
      </div>
    </div>
  </header>
  <!-- -------- END HEADER 1 w/ text and image on right ------- -->
  <div class="card card-body blur shadow-blur mx-2 mx-lg-3 mt-n6 mb-4" id="app">
    <div class="section my-4 my-lg-5">
      <div class="container">
        <div class="text-start">
          <a href="{{url('/shop')}}" class="btn btn-outline-info btn-sm">Back To Shop</a>
        </div>
        <h5 class="text-center">Shopping Cart</h5>

        @if(session('cart'))
        <div class="row d-flex justify-content-center my-4">
          <div class="col-md-8">
            <div class="card mb-4">
              <div class="card-header py-3">
                <h5 class="mb-0">Cart</h5>
              </div>
              <div class="card-body">
                <!-- Single item -->
                @foreach(session('cart')??[] as $id => $product)
                <div class="row">
                  <div class="col-lg-3 col-md-12 mb-2 mb-lg-0">
                    <!-- Image -->
                    <div class="" data-mdb-ripple-color="light">
                      <img src="{{asset('storage/'.$product['image'])}}" class="w-100 aspect-ratio-1 object-fit-contain p-2 border" />
                    </div>
                  </div>
    
                  <div class="col-lg-5 col-md-6 mb-2 mb-lg-0">
                    <!-- Data -->
                    <p><strong>{{$product['name']}}</strong></p>
                    <p>Price : ${{$product['price']}}</p>
                    @foreach($product['sku_attribute'] as $key=>$atr)
                      <div class="text-sm">{{$key}}: {{$atr}}</div>
                    @endforeach
                    {{-- <div>Size: M</div>
                    <div>Size: M</div> --}}
                    
                    
                    <!-- Data -->
                  </div>
    
                  <div class="col-lg-4 col-md-6 mb-2 mb-lg-0">
                    <!-- Quantity -->
                    <div class="text-end mb-2 align-items-end" style="max-width: 300px" >
                      {{-- <button class="btn btn-info overflow-visible btn-mns my-0 me-2 o" onclick="this.parentNode.querySelector('input[type=text]').stepDown()">
                        <i class="fas fa-minus"></i>
                      </button> --}}
                      <div class="form-outline w-75 ms-auto">
                        <input class="form-control border text-center" id="form1" min="1" name="quantity" value="{{$product['quantity']}}" type="text" disabled/>
                      </div>
                      {{-- <button class="btn btn-info overflow-visible btn-add my-0 ms-2">
                        <i class="fas fa-plus"></i>
                      </button> --}}
                      
                    </div>
                    <!-- Quantity -->
    
                    <!-- Price -->
                    <div class="text-end">
                      <strong>Total : ${{$product['total']}}</strong>
                    </div>
                    <div class="text-end mt-5">
                      <button type="button" class="btn btn-danger btn-sm btn-remove m-0" data-mdb-toggle="tooltip" title="Remove item" product-id="{{$id}}" @click="removeItem({{$id}})">
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                    <!-- Price -->
                  </div>
                </div>
                <hr class="horizontal dark my-4">
                @endforeach
                
    
                
    
                
              </div>
            </div>
            {{-- <div class="card mb-4">
              <div class="card-body">
                <p><strong>Expected shipping delivery</strong></p>
                <p class="mb-0">12.10.2020 - 14.10.2020</p>
              </div>
            </div>
            <div class="card mb-4 mb-lg-0">
              <div class="card-body">
                <p><strong>We accept</strong></p>
                <img class="me-2" width="45px"
                  src="https://mdbcdn.b-cdn.net/wp-content/plugins/woocommerce-gateway-stripe/assets/images/visa.svg"
                  alt="Visa" />
                <img class="me-2" width="45px"
                  src="https://mdbcdn.b-cdn.net/wp-content/plugins/woocommerce-gateway-stripe/assets/images/amex.svg"
                  alt="American Express" />
                <img class="me-2" width="45px"
                  src="https://mdbcdn.b-cdn.net/wp-content/plugins/woocommerce-gateway-stripe/assets/images/mastercard.svg"
                  alt="Mastercard" />
                <img class="me-2" width="45px"
                  src="https://mdbcdn.b-cdn.net/wp-content/plugins/woocommerce/includes/gateways/paypal/assets/images/paypal.webp"
                  alt="PayPal acceptance mark" />
              </div>
            </div> --}}
          </div>
          <div class="col-md-4">
            <div class="card mb-4">
              <div class="card-header py-3">
                <h5 class="mb-0">Summary</h5>
              </div>
              <div class="card-body">
                <ul class="list-group list-group-flush">
                  <li
                    class="list-group-item d-flex justify-content-between align-items-center border-0 px-0 pb-0">
                    Sub Total
                    <span>${{$total}}</span>
                  </li>
                  <li class="list-group-item d-flex justify-content-between align-items-center px-0">
                    @if($points->balance > 0)
                    <div class="form-check ps-0 me-2 d-inline-block">
                     
                      <div>
                        <label class="custom-control-label ms-0 mb-0 cursor-pointer pe-1" for="point-checkbox">Use Points</label>
                        
                        <input type="checkbox" class="form-check-input" id="point-checkbox" @change="checkUsePoint()" v-model="usePoint">
                        
                      </div>
                     
                      <div class="text-xxs">
                        You have @{{user_points}} points
                      </div>
                    </div>
                    <div>
                      <div>- $@{{point_money}}</div>
                      <div class="text-xxs text-end">- @{{used_points}}</div>
                    </div>
                    @endif
                    {{-- <span>- $@{{point_money}}</span> --}}
                    {{-- <div class="form-check ps-0 me-2 d-inline-block">
                      <input class="form-check-input product-c" type="checkbox" id="product-c-{{$category->id}}" value="{{$category->id}}" v-model="checkedCategories" @change="check()">
                      <label class="custom-control-label ms-0 cursor-pointer ps-1" for="product-c-{{$category->id}}"> {{$category->name}} </label>
                    </div> --}}
                  </li>
                  <li
                    class="list-group-item d-flex justify-content-between align-items-center border-0 px-0 mb-3">
                    <div>
                      <strong>Total amount</strong>
                      {{-- <strong>
                        <p class="mb-0">(including VAT)</p>
                      </strong> --}}
                    </div>
                    <span><strong>$@{{total}}</strong></span>
                  </li>
                </ul>
                <div class="text-end">
                  <button type="button" class="btn bg-gradient-info" @click="checkout">Check Out</button>
                </div>
              </div>
            </div>
          </div>
        </div>
        @else
        <div class="row d-flex justify-content-center my-4 sry-message">
          <div class="col-lg-12 m-auto text-center">
            <div class="card">
              <div class="card-body">
                <img src="{{asset('image/pia_emote/alright.png')}}" class="w-15">
                <h3 class="text mt-3">Your Cart Is Empty</h3>
                <a href="{{url('/shop')}}" class="btn btn-info">Back To Shop</a>
                {{-- <p class="lead">You can go to <a href="{{url('/shop')}}">Shop</a> to view our products</p> --}}
              </div>
            </div>
            {{-- <p>If you wish to start a new request, you can delete your preious request and submit again</p> --}}
          </div>
        </div>
        @endif
      </div>
    </div>
  </div>
    <!-- END Teams w/ 4 cards w/ image on left & descriptio/social link on right -->
    <!-- -------- END PRE-FOOTER 8 w/ TEXT, BG IMAGE AND 2 BUTTONS ------- -->
  @include('frontend/footer')
  @include('frontend/js')
  <script type="module">
    import { createApp } from 'https://unpkg.com/vue@3/dist/vue.esm-browser.js'
    createApp({
      data() {
        return {
          total:"{{$total}}",
          usePoint:false,
          point_money:0,
          user_points:"{{$points->balance}}",
          used_points:0,
          
        }
      },
      methods:{
        removeItem: function(product_id) {
          let remove_data = {
            "_token": "{{ csrf_token() }}",
            'id':product_id,
          }
          
          $.ajax({
              method: "POST",
              url: "{{url('cart/remove')}}",
              data: remove_data,
              success: function(response) {
                if(response.code == 200 ){
                  toastr.success(response.message, 'Success',{"positionClass": "toast-top-center",})
                  location.reload();
                }
                else{
                  toastr.error(response.message, 'Error',{"positionClass": "toast-top-center",})
                }
              }
          });
          
        },
        checkUsePoint:function(){
          if(this.usePoint){
            if(this.user_points >= this.total*100){
              let used_points = this.total*100
              this.used_points = used_points
              this.total = 0
              this.point_money = "{{$total}}"
            }
            else{
              let used_points = this.user_points;
              let point_money = used_points/100;
              let total = this.total - point_money;
              this.used_points = used_points
              this.point_money = point_money
              this.total = total.toFixed(2);
            }
          }
          else{
            this.total = "{{$total}}";
            this.point_money = 0
            this.used_points = 0
          }
          
        },
        checkout(){
          if(this.usePoint){
            window.location="{{url('/checkout')}}"+"?used_points="+this.used_points;
          }
          else{
            window.location="{{url('/checkout')}}";
          }
        }
      },
      created(){
       
      }
    }).mount('#app')
  </script>
  <script>
    // $( ".btn-remove" ).click(function() {
    //   let product_id = $(this).attr('product-id');
    //   let remove_data = {
    //     "_token": "{{ csrf_token() }}",
    //     'id':product_id,
    //   }
      
    //   $.ajax({
    //     method: "POST",
    //     url: "{{url('cart/remove')}}",
    //     data: remove_data,
    //     success: function(response) {
    //       if(response.code == 200 ){
    //         toastr.success(response.message, 'Success',{"positionClass": "toast-top-center",})
    //         location.reload();
    //       }
    //       else{
    //         toastr.error(response.message, 'Error',{"positionClass": "toast-top-center",})
    //       }
    //     }
    //   });
    // });

    // $( ".btn-remove" ).click(function() {


    // })
  </script>
</body>


</html>
