<!DOCTYPE html>
<html lang="en" itemscope itemtype="http://schema.org/WebPage">

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <link rel="apple-touch-icon" sizes="76x76" href="../../assets/img/apple-icon.png">
  <link rel="icon" type="image/png" href="../../assets/img/favicon.png">
  <title>
    Elda Solar Studio
  </title>
  @include('frontend/css')
</head>

<body class="sign-up-basic overflow-hidden">

  <div class="page-header min-vh-100 position-relative">

    <video class="z-index-1 w-100" playsinline="playsinline" autoplay="true" muted="unmute" loop="loop" loading="lazy">
      <source src="{{asset('storage/background-3.mp4')}}" type="video/mp4">
    </video>
    <div class="container my-auto">
      <div class="row">
        <div class="col-lg-12 m-auto text-center">
          {{-- <h1 class="display-1 text-bolder text-white">Error</h1> --}}
          <img src="{{asset('image/pia_emote/sorry.gif')}}" class="w-15">
          <h2 class="text-white">{{$error_data['title']}}</h2>
          <p class="lead text-white">{{$error_data['message']}}</p>
          <a href="{{route($error_data['btn_link'])}}?{{$error_data['parameter']}}" class="btn btn-white mt-4">{{$error_data['btn_txt']}}</a>
        </div>
      </div>
    </div>
  </div>
  <!--   Core JS Files   -->
  @include('frontend/js')

  <script>
    // $( "#signin" ).click(function() {
    //     let signin_data = {
    //         'email':$('#email').val(),
    //         'password':$('#password').val(),
    //         'remember':$('#rememberMe').is(':checked')?1:0,
    //         "_token": "{{ csrf_token() }}"
    //     }



    //     $.ajax({
    //         method: "POST",
    //         url: "{{url('user/signin')}}",
    //         data: signin_data,
    //         success: function(response) {
    //           if(response.code == 200 ){
    //             window.location.href = "{{url('/dashboard/home')}}";
    //           }
    //           else{
    //             toastr.error(response.message, 'Error',{"positionClass": "toast-top-center",})
    //           }

    //         }
    //     });


    //     console.log(signin_data)
    // });
  </script>

</body>



</html>


