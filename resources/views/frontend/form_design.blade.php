
<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" itemscope itemtype="http://schema.org/WebPage">

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <title>
    Elda Solar Studio
  </title>
  @include('frontend/css')
</head>

<body class="coworking bg-gray-200">
  <!-- Navbar -->
  @include('frontend/topnav')
  <!-- -------- START HEADER 1 w/ text and image on right ------- -->
  <header>
    <div class="page-header min-vh-75" style="background-image: url('{{asset('/image/pia_bg_4k.jpg')}}');" loading="lazy">
      <span class="mask bg-gradient-dark opacity-5"></span>
      <div class="container">
        <div class="row">
          <div class="col-lg-6 col-md-7 d-flex justify-content-center text-md-start text-center flex-column mt-sm-0 mt-7">
            <h1 class="text-white">Your adventure starts here.</h1>
            <p class="lead pe-md-5 me-md-5 text-white opacity-8">The time is now for it be okay to be great. People in this world shun people for being nice.</p>
            <div class="buttons">
              {{-- <button type="button" class="btn bg-gradient-primary mt-4">Get Started</button>
              <button type="button" class="btn text-white shadow-none mt-4">Read more</button> --}}
            </div>
          </div>
        </div>
      </div>
    </div>
  </header>
  <!-- -------- END HEADER 1 w/ text and image on right ------- -->
  <div class="card card-body blur shadow-blur mx-2 mx-lg-3 mt-n6 mb-4">
    <!-- -------- START Features w/ 4 cols w/ colored icon & title & text -------- -->
    <section>
      <div class="container py-4">

        <div class="row">
          <div class="col-lg-12 mx-auto d-flex justify-content-center flex-column">
            @if(true)
            <h3 class="text-center mb-5 commission-form-content">Character Design Request Form</h3>
            <div class="card mt-5 commission-form-content">
              <div class="card-header p-0 position-relative mt-n5 mx-3 z-index-2">
                  <div class="bg-gradient-secondary shadow-secondary border-radius-lg pt-4 pb-3">
                    <div class="multisteps-form__progress">
                        <button class="multisteps-form__progress-btn js-active" type="button" title="Request Type">
                            <span>1. Basic Info</span>
                        </button>
                        <button class="multisteps-form__progress-btn" type="button" title="Basic Info">2. Character Design</button>
                        {{-- <button class="multisteps-form__progress-btn" type="button" title="Socials">3. Managment Detail</button> --}}
                        {{-- <button class="multisteps-form__progress-btn" type="button" title="Pricing">4. L2d Rigging</button> --}}
                    </div>
                  </div>
              </div>
              <div class="card-body">
                <form class="multisteps-form__form">
                <!--single form panel-->
                <div class="multisteps-form__panel pt-3 border-radius-xl bg-white js-active" data-animation="FadeIn">
                  <h5 class="font-weight-bolder">Basic Infomation </h5>
                  <div class="multisteps-form__content">
                  <div class="row mt-5">
                      <div class="col-6">
                        <div class="input-group input-group-dynamic">
                            <label class="form-label">Client Name</label>
                            <input class="multisteps-form__input form-control" id="client_name" type="text" title="Your name, or the name you wish to be called."/>
                        </div>
                        <div class="input-group input-group-dynamic mt-5">
                          <label class="form-label">Email</label>
                          <input class="multisteps-form__input form-control" id="email" type="text" />
                        </div>
                        <div class="input-group input-group-dynamic mt-5">
                          <label class="form-label">Social Link</label>
                          <input class="multisteps-form__input form-control" id="social_link" type="text" title="Any social media where we can reach you."/>
                        </div>

                      </div>
                      <div class="col-6">
                        <div class="input-group input-group-dynamic">
                          <label class="form-label">Commission Name</label>
                          <input class="multisteps-form__input form-control" id="project_name" type="text" title="Simple name helps you differentiate commission requests."/>
                        </div>
                        <div class="input-group input-group-dynamic mt-5">
                          <label class="form-label">Budget (USD)</label>
                          <input class="multisteps-form__input form-control" id="budget" type="number" />
                        </div>
                        <div class="input-group input-group-dynamic mt-5">
                          <div class="input-group input-group-static">
                            <input class="form-control datepicker" placeholder="Deadline" type="text" id="deadline">
                          </div>
                        </div>
                      </div>

                  </div>
                  <div class="row">
                      <div class="button-row d-flex mt-4 col-12">
                        <button class="btn bg-gradient-dark ms-auto mb-0 js-btn-next" type="button" title="Next">Next</button>
                      </div>
                  </div>
                  </div>
                </div>
                <!--single form panel-->
                <div class="multisteps-form__panel pt-3 border-radius-xl bg-white" data-animation="FadeIn">
                    {{-- <h5 class="font-weight-bolder">Character Detail</h5> --}}
                  <div class="multisteps-form__content">
                    <div class="row mt-3">
                      <div class="col-12">
                        <h6>Design Process</h6>
                        <select class="form-control choice" name="choices-category" id="design_flow">
                          <option value="I have a very clear idea"  selected="">I have a very clear idea of what I'm looking for, and have detailed references that support that thought.</option>
                          <option value="I have a vague idea">I have a vague idea of what I am looking for and some references ready. I would like some suggestions from the artist.</option>
                          <option value="I have a little idea ">I have a little idea of what I'm looking for with no references. I will let the artist handle most of the creation based on my idea.</option>
                          <option value="I have no idea">I have no idea of what I'm looking for, or it can be summed up in a few words. I fully trust the artist and will hardly have complaints.</option>
                          <option value="picky" disabled>I have no idea of what I'm looking for, and also very picky at the same time. ╮(๑•́ ₃•̀๑)╭</option>

                        </select>

                      </div>
                    </div>
                    <div class="d-flex mt-3">
                      <h6 class="my-4">Character Details</h6>
                      {{-- <div class="d-flex ms-auto align-items-center">
                        <div class="mb-0">Simple Form</div>
                        <div class="form-check form-switch d-flex align-items-center mx-3">
                          <input class="form-check-input" type="checkbox" id="form_type" checked>
                        </div>
                        <div class="mb-0">Detail Form</div>
                      </div> --}}
                    </div>
                    <div class="form-detail">
                      <div class="row">
                        <div class="col-6">
                          <div class="input-group input-group-dynamic">
                              <label class="form-label">Gender</label>
                              <input class="multisteps-form__input form-control" id="gender" type="text" />
                          </div>
                          <div class="input-group input-group-dynamic mt-3">
                            <label class="form-label">Age</label>
                            <input class="multisteps-form__input form-control" id="age" type="number" />
                          </div>
                          <div class="input-group input-group-dynamic mt-3">
                            <label class="form-label">Height</label>
                            <input class="multisteps-form__input form-control" id="height" type="text" />
                          </div>
                          <div class="input-group input-group-dynamic mt-3">
                            <label class="form-label">Weight</label>
                            <input class="multisteps-form__input form-control" id="weight" type="text" />
                          </div>
                          <div class="input-group input-group-dynamic mt-3">
                            <label class="form-label">Body Shape</label>
                            <input class="multisteps-form__input form-control" id="body_shape" type="text" />
                          </div>
                          <div class="input-group input-group-dynamic mt-3">
                            <label class="form-label">Skin Color</label>
                            <input class="multisteps-form__input form-control" id="skin_color" type="text" />
                          </div>
                        </div>
                        <div class="col-6">
                          <div class="input-group input-group-dynamic">
                            <label class="form-label">Race</label>
                            <input class="multisteps-form__input form-control" id="race"type="text" />
                          </div>
                          <div class="input-group input-group-dynamic mt-3">
                              <label class="form-label">Eyes</label>
                              <input class="multisteps-form__input form-control" id="eyes" type="text" />
                          </div>
                          <div class="input-group input-group-dynamic mt-3">
                            <label class="form-label">Hair</label>
                            <input class="multisteps-form__input form-control" id="hair" type="text" />
                          </div>
                          <div class="input-group input-group-dynamic mt-3">
                            <label class="form-label">Face</label>
                            <input class="multisteps-form__input form-control" id="face" type="text" />
                          </div>
                          <div class="input-group input-group-dynamic mt-3">
                            <label class="form-label">Outfit</label>
                            <input class="multisteps-form__input form-control" id="outfit" type="text" />
                          </div>
                          <div class="input-group input-group-dynamic mt-3">
                            <label class="form-label">Color Theme</label>
                            <input class="multisteps-form__input form-control" id="color_theme" type="text" />
                          </div>
  
                        </div>
                      </div>
                      <div class="row mt-3">
                        <h6 class="mt-4">Other Details</h6>
                        <div class="col-12">
                          <div class="input-group input-group-dynamic">
                              {{-- <label class="form-label">Gender</label> --}}
                              <textarea class="form-control" id="cd_other_detail" rows="4" placeholder="Anything that can help us understand your character better, like the charatcer vibe, personality, favorite things, etc..."></textarea>
                          </div>
  
                        </div>
                      </div>
                      <div class="row mt-3">
                        <h6 class="my-4">Reference</h6>
                        <div class="col-12">
                          <label class="form-control mb-0">Upload Refrence Files</label>
                          <div class="form-control border dropzone dz-clickable" id="cd_ref" file-type="design">
                            <div class="dz-default dz-message"><button class="dz-button" type="button">Drop files here to upload</button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {{-- <div class="form-simple d-none-ni">
                      <div class="row">
                        <div class="col-6">
                          <div class="input-group input-group-dynamic">
                              <label class="form-label">Gender</label>
                              <input class="multisteps-form__input form-control" id="gender" type="text" />
                          </div>
                          <div class="input-group input-group-dynamic mt-3">
                            <label class="form-label">Age</label>
                            <input class="multisteps-form__input form-control" id="age" type="number" />
                          </div>
                          
                        </div>
                        <div class="col-6">
                          <div class="input-group input-group-dynamic">
                            <label class="form-label">Race</label>
                            <input class="multisteps-form__input form-control" id="race"type="text" />
                          </div>
                          <div class="input-group input-group-dynamic mt-3">
                              <label class="form-label">Eyes</label>
                              <input class="multisteps-form__input form-control" id="eyes" type="text" />
                          </div>
                          
  
                        </div>
                      </div>

                      <div class="row mt-3">
                        <h6 class="mt-4">Other Details</h6>
                        <div class="col-12">
                          <div class="input-group input-group-dynamic">
                              
                              <textarea class="form-control" id="cd_other_detail" rows="4"></textarea>
                          </div>
  
                        </div>
                      </div>
                    </div> --}}


                    
                    


                    <div class="button-row d-flex mt-4">
                      <button class="btn bg-gradient-light mb-0 js-btn-prev" type="button" title="Prev">Prev</button>
                      <button class="btn bg-gradient-success ms-auto mb-0" type="button" title="Next" id="finish">Finish</button>
                      {{-- <button class="btn bg-gradient-dark ms-auto mb-0 js-btn-next" type="button" title="Next">Next</button> --}}
                    </div>
                  </div>
                </div>
                <!--single form panel-->
                
                <!--single form panel-->
                
                <!--single form panel-->
                
                </form>
              </div>
            </div>

            
            <div class="row my-7 ty-message d-none-ni">
              <div class="col-lg-12 m-auto text-center">
                <img src="{{asset('image/pia_emote/ty.png')}}" class="w-15">
                <h2 class="text mt-3">Thank You For Your Request</h2>
                <p class="lead">You can go to <a href="{{url('dashboard/commissions')}}">Dashboard</a> to view your submited request</p>
                {{-- <p>If you wish to start a new request, you can delete your preious request and submit again</p> --}}
              </div>
            </div>
            
            @else
            <div class="row my-7 ty-message">
              <div class="col-lg-12 m-auto text-center">
                <img src="{{asset('image/pia_emote/ty.png')}}" class="w-15">
                <h2 class="text mt-3">Thank You For Your Request</h2>
                <p class="lead">You can go to <a href="{{url('dashboard/commissions')}}">Dashboard</a> to view your submited request</p>
                {{-- <p>If you wish to start a new request, you can delete your preious request and submit again</p> --}}
              </div>
            </div>
            @endif
          </div>
        </div>
      </div>
    </section>
    {{-- FAQ --}}
    {{-- <section class="py-4">
      <div class="container">
          <div class="row my-5">
          <div class="col-md-6 mx-auto text-center">
              <h2>Frequently Asked Questions</h2>
              <p>A lot of people don&#39;t appreciate the moment until it’s passed. I&#39;m not trying my hardest, and I&#39;m not trying to do </p>
          </div>
          </div>
          <div class="row">
          <div class="col-md-10 mx-auto">
              <div class="accordion" id="accordionRental">
              <div class="accordion-item mb-3">
                  <h5 class="accordion-header" id="headingOne">
                  <button class="accordion-button border-bottom font-weight-bold" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                      What are the Levels
                      <i class="collapse-close fa fa-plus text-xs pt-1 position-absolute end-0"></i>
                      <i class="collapse-open fa fa-minus text-xs pt-1 position-absolute end-0"></i>
                  </button>
                  </h5>
                  <div id="collapseOne" class="accordion-collapse collapse show" aria-labelledby="headingOne" data-bs-parent="#accordionRental">
                  <div class="accordion-body text-sm opacity-8">
                      We’re not always in the position that we want to be at. We’re constantly growing. We’re constantly making mistakes. We’re constantly trying to express ourselves and actualize our dreams. If you have the opportunity to play this game
                      of life you need to appreciate every moment. A lot of people don’t appreciate the moment until it’s passed.
                  </div>
                  </div>
              </div>
              <div class="accordion-item mb-3">
                  <h5 class="accordion-header" id="headingTwo">
                  <button class="accordion-button border-bottom font-weight-bold" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                      How can i make the payment?
                      <i class="collapse-close fa fa-plus text-xs pt-1 position-absolute end-0"></i>
                      <i class="collapse-open fa fa-minus text-xs pt-1 position-absolute end-0"></i>
                  </button>
                  </h5>
                  <div id="collapseTwo" class="accordion-collapse collapse" aria-labelledby="headingTwo" data-bs-parent="#accordionRental">
                  <div class="accordion-body text-sm opacity-8">
                      It really matters and then like it really doesn’t matter. What matters is the people who are sparked by it. And the people who are like offended by it, it doesn’t matter. Because it&#39;s about motivating the doers. Because I’m here to follow my dreams and inspire other people to follow their dreams, too.
                      <br>
                      We’re not always in the position that we want to be at. We’re constantly growing. We’re constantly making mistakes. We’re constantly trying to express ourselves and actualize our dreams. If you have the opportunity to play this game of life you need to appreciate every moment. A lot of people don’t appreciate the moment until it’s passed.
                  </div>
                  </div>
              </div>
              <div class="accordion-item mb-3">
                  <h5 class="accordion-header" id="headingThree">
                  <button class="accordion-button border-bottom font-weight-bold" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                      How much time does it take to receive the order?
                      <i class="collapse-close fa fa-plus text-xs pt-1 position-absolute end-0"></i>
                      <i class="collapse-open fa fa-minus text-xs pt-1 position-absolute end-0"></i>
                  </button>
                  </h5>
                  <div id="collapseThree" class="accordion-collapse collapse" aria-labelledby="headingThree" data-bs-parent="#accordionRental">
                  <div class="accordion-body text-sm opacity-8">
                      The time is now for it to be okay to be great. People in this world shun people for being great. For being a bright color. For standing out. But the time is now to be okay to be the greatest you. Would you believe in what you believe in, if you were the only one who believed it?
                      If everything I did failed - which it doesn&#39;t, it actually succeeds - just the fact that I&#39;m willing to fail is an inspiration. People are so scared to lose that they don&#39;t even try. Like, one thing people can&#39;t say is that I&#39;m not trying, and I&#39;m not trying my hardest, and I&#39;m not trying to do the best way I know how.
                  </div>
                  </div>
              </div>
              <div class="accordion-item mb-3">
                  <h5 class="accordion-header" id="headingFour">
                  <button class="accordion-button border-bottom font-weight-bold" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFour" aria-expanded="false" aria-controls="collapseFour">
                      Can I resell the products?
                      <i class="collapse-close fa fa-plus text-xs pt-1 position-absolute end-0"></i>
                      <i class="collapse-open fa fa-minus text-xs pt-1 position-absolute end-0"></i>
                  </button>
                  </h5>
                  <div id="collapseFour" class="accordion-collapse collapse" aria-labelledby="headingFour" data-bs-parent="#accordionRental">
                  <div class="accordion-body text-sm opacity-8">
                      I always felt like I could do anything. That’s the main thing people are controlled by! Thoughts- their perception of themselves! They&#39;re slowed down by their perception of themselves. If you&#39;re taught you can’t do anything, you won’t do anything. I was taught I could do everything.
                      <br><br>
                      If everything I did failed - which it doesn&#39;t, it actually succeeds - just the fact that I&#39;m willing to fail is an inspiration. People are so scared to lose that they don&#39;t even try. Like, one thing people can&#39;t say is that I&#39;m not trying, and I&#39;m not trying my hardest, and I&#39;m not trying to do the best way I know how.
                  </div>
                  </div>
              </div>
              <div class="accordion-item mb-3">
                  <h5 class="accordion-header" id="headingFifth">
                  <button class="accordion-button border-bottom font-weight-bold" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFifth" aria-expanded="false" aria-controls="collapseFifth">
                      Where do I find the shipping details?
                      <i class="collapse-close fa fa-plus text-xs pt-1 position-absolute end-0"></i>
                      <i class="collapse-open fa fa-minus text-xs pt-1 position-absolute end-0"></i>
                  </button>
                  </h5>
                  <div id="collapseFifth" class="accordion-collapse collapse" aria-labelledby="headingFifth" data-bs-parent="#accordionRental">
                  <div class="accordion-body text-sm opacity-8">
                      There’s nothing I really wanted to do in life that I wasn’t able to get good at. That’s my skill. I’m not really specifically talented at anything except for the ability to learn. That’s what I do. That’s what I’m here for. Don’t be afraid to be wrong because you can’t learn anything from a compliment.
                      I always felt like I could do anything. That’s the main thing people are controlled by! Thoughts- their perception of themselves! They&#39;re slowed down by their perception of themselves. If you&#39;re taught you can’t do anything, you won’t do anything. I was taught I could do everything.
                  </div>
                  </div>
              </div>
              </div>
          </div>
          </div>
      </div>
    </section> --}}
  </div>
  @include('frontend/footer')
  @include('frontend/js')
  <script src="{{asset('dashboard/js/plugins/choices.min.js')}}"></script>
  <script src="{{asset('dashboard/js/plugins/multistep-form.js')}}"></script>
  <script src="{{asset('dashboard/js/plugins/dropzone.min.js')}}"></script>
  <script src="{{asset('dashboard/js/plugins/flatpickr.min.js')}}"></script>

  <script>
      $('.choice').each(function(i, obj) {
        const example = new Choices(obj, {
          searchEnabled: false,
          shouldSort: false,
        });
      });

      // $("#form_type").change(function() {
      //   if($(this).is(':checked')){
      //     $('.form-simple').hide()
      //     $('.form-detail').show();
      //     setFormHeight()
      //   }
      //   else{
      //     $('.form-detail').hide()
      //     $('.form-simple').show();
      //     setFormHeight()
      //   } 
      // });

      // $("#design_flow").change(function() {
      //   if($(this).find(":selected").val() == 1 || $(this).find(":selected").val() == 2){
      //     $("#form_type").prop('checked', true);
      //     $('.form-detail').show();
      //     $('.form-simple').hide()
      //     setFormHeight()
      //   }
      //   else if($(this).find(":selected").val() == 3 || $(this).find(":selected").val() == 4){
      //     $("#form_type").prop('checked', false);
      //     $('.form-detail').hide();
      //     $('.form-simple').show()
      //     setFormHeight()
      //   }
      // });


      

      // $("#model_need_select").change(function() {
      //   if($(this).find(":selected").val() == 1){
      //     $('.model-need').show()
      //     setFormHeight()
      //   }
      //   else if($(this).find(":selected").val() == 0){
      //     $('.model-need').hide()
      //     setFormHeight()
      //   }
      // });

      // $("#rig_need_select").change(function() {
      //   if($(this).find(":selected").val() == 1){
      //     $('.rig-need').show()
      //     setFormHeight()
      //   }
      //   else if($(this).find(":selected").val() == 0){
      //     $('.rig-need').hide()
      //     setFormHeight()
      //   }
      // });

      flatpickr(".datepicker");
      // var uploaded_files = {};
      var uploaded_files = [];
      Dropzone.autoDiscover = false;
      var dropzone = $('.dropzone').dropzone({
        url: '{{url('user/request/refrence')}}',
        method: 'post',
        addRemoveLinks: true,
        headers: {
            'X-CSRF-TOKEN': "{{ csrf_token() }}"
        },
        init: function() {
          this.on('sending', function(file, xhr, formData){
            let dropzone_id = this.element.getAttribute('file-type');
            formData.append('dropzone_id', dropzone_id);
          });
          this.on("addedfile", file => {
            setFormHeight()
          });
          this.on("removedfile", file => {
            setFormHeight()
            let file_name = file.previewElement.id;
            let file_index = uploaded_files.indexOf(file_name);
            if(file_index > -1){
              uploaded_files.splice(file_index,1)
            }
            console.log(uploaded_files);
            
            
          });
          this.on("success", function(file, response){
            // let dropzone_id = this.element.getAttribute('id');
            // if(uploaded_files[dropzone_id]){
            //   uploaded_files[dropzone_id].push(response['file_name'])
            // }
            // else{
            //   uploaded_files[dropzone_id] = [];
            //   uploaded_files[dropzone_id].push(response['file_name'])
            // }
            
            uploaded_files.push(response['file_name'])
            file.previewElement.id  = response['file_name'];
            console.log(uploaded_files);
           
          });
        }
      });


      $( "#finish" ).click(function() {
        let request_data = {
          "_token": "{{ csrf_token() }}",
          'type':"Character Design",
          "client_name":$('#client_name').val(),
          'email':$('#email').val(),
          'social_link':$('#social_link').val(),
          'project_name':$('#project_name').val(),
          'budget':$('#budget').val(),
          'deadline':$('#deadline').val(),
          'design_flow':$('#design_flow').val(),
          'gender':$('#gender').val(),
          'age':$('#age').val(),
          'height':$('#height').val(),
          'weight':$('#weight').val(),
          'body_shape':$('#body_shape').val(),
          'skin_color':$('#skin_color').val(),
          'race':$('#race').val(),
          'eyes':$('#eyes').val(),
          'hair':$('#hair').val(),
          'face':$('#face').val(),
          'outfit':$('#outfit').val(),
          'color_theme':$('#color_theme').val(),
          'cd_other_detail':$('#cd_other_detail').val(),
          'uploaded_files':uploaded_files,
        }
        console.log(request_data);

        $.ajax({
            method: "POST",
            url: "{{url('user/request/submit')}}",
            data: request_data,
            success: function(response) {
              console.log(response);
              if(response.code == 200 ){
                $('.commission-form-content').hide();
                $('.ty-message').fadeIn(500);
              }
              // else{
              //   toastr.error(response.message, 'Error',{"positionClass": "toast-top-center",})
              // }
            }
        });
    });





      // dropzone.on("addedfile", function(file) {
      //   console.log(12312312);
      // });










  </script>
</body>

</html>
