
<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" itemscope itemtype="http://schema.org/WebPage">

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <title>
    Elda Solar Studio
  </title>
  @include('frontend/css')
</head>

<body class="coworking bg-gray-200">
  <!-- Navbar -->
  @include('frontend/topnav')
  <!-- -------- START HEADER 1 w/ text and image on right ------- -->
  <header>
    <div class="page-header min-vh-80" style="background-image: url('{{asset('/image/pia_bg_4k.jpg')}}');" loading="lazy">
      <span class="mask bg-gradient-dark opacity-2"></span>
      <div class="container">
        <div class="row">
          <div class="col-lg-6 col-md-7 d-flex justify-content-center text-md-start text-center flex-column mt-sm-0 mt-7">
            <h1 class="text-white">Check Our Artists</h1>
            <p class="lead pe-md-5 me-md-5 text-white opacity-8">You may add them as prefer artists to your commission request.</p>
            <div class="buttons">
              <a href="{{url('/request')}}" type="button" class="btn bg-gradient-info mt-4">Request Now</a>
              {{-- <button type="button" class="btn text-white shadow-none mt-4">Read more</button> --}}
            </div>
          </div>
        </div>
      </div>
    </div>
  </header>
  <!-- -------- END HEADER 1 w/ text and image on right ------- -->
  <div class="card card-body blur shadow-blur mx-2 mx-lg-3 mt-n6 mb-4">

    <section class="pt-3">
      <div class="container">
        <div class="row border-radius-md py-3 mx-sm-0 position-relative border-bottom">
          <div class="col-md-3 mt-lg-n2">
            <label class="ms-0">Artists List</label>
          </div>

          <div class="col-lg-2 mt-lg-n2">
            {{-- <label>&nbsp;</label>
            <button type="button" class="btn bg-gradient-info w-100 mb-0">Search</button> --}}
          </div>

        </div>
        @foreach ($artists as $artist)
        <div class="row py-3 border-bottom">
          <div class="col-3 col-lg-2">
            <div class="w-lg-75 text-center">
              <div class="artist-avatar">
                <a href="{{url('/artist/').'/'.$artist->id}}" class="">
                  <img class="shadow-8 w-100 rounded-circle" alt="artist avatar" src="{{asset('storage/'.$artist->avatar)}}">
                  <div class="text-info font-weight-bolder d-inline-block mt-2">{{$artist->name}}</div>
                </a>
              </div>
              @if(count($commissions) > 0)
              <div class="btn bg-gradient-info btn-sm mt-2 add-artist-btn" artist-id="{{$artist->id}}">Select</div>
              @endif
            </div>
          </div>
          <div class="col-9 col-lg-10" style="white-space: nowrap;">
            <div class="row p-0">
            @foreach($artist->images as $image)
              <div class="square col-6 col-lg-3 mb-lg-0 mb-2 p-0 ">
                <a data-fancybox="artist-{{$artist->id}}" href="{{asset('storage/'.$image->image_url)}}">
                  @if($image->small_url)
                  <img class="square-content shadow border-radius-lg" src="{{asset('storage/'.$image->small_url)}}" />
                  @else
                  <img class="square-content shadow border-radius-lg" src="{{asset('storage/'.$image->image_url)}}" />
                  @endif
                </a>
              </div>
            @endforeach
            </div>
          </div>
        </div>
        @endforeach
        {{-- <div class="row py-3 border-bottom-xl">
          <div class="col-2">
            <div class="w-75 text-center">
              <a href="{{url('/artist/1')}}" class="">
                <img class="shadow-8 w-100 rounded-circle" alt="Image placeholder" src="{{asset('image/pia_emote/grow.png')}}">
                <div class="text-info  font-weight-bolder mt-2">Pia Pia</div>
              </a>
              <div class="btn bg-gradient-info btn-sm mt-2">Select</div>
            </div>
          </div>
          <div class="col-10 px-0">
            <div class="square px-1">
              <img class="square-content shadow-8" src="{{asset('sample_img/1.jpg')}}" />
            </div>
            <div class="square px-1">
              <img class="square-content shadow-8" src="{{asset('sample_img/2.jpg')}}" />
            </div>
            <div class="square px-1">
              <img class="square-content shadow-8" src="{{asset('sample_img/3.jpg')}}" />
            </div>
            <div class="square px-1">
              <img class="square-content shadow-8" src="{{asset('sample_img/4.jpg')}}" />
            </div>

          </div>
        </div>

        <div class="row py-3 border-bottom-xl">
          <div class="col-2">
            <div class="w-75 text-center">
              <a href="{{url('/artist_detail')}}" class="">
                <img class="shadow-8 w-100 rounded-circle" alt="Image placeholder" src="{{asset('image/pia_emote/grow.png')}}">
                <div class="text-info  font-weight-bolder mt-2">Pia Pia</div>
              </a>
              <div class="btn bg-gradient-info btn-sm mt-2">Select</div>
            </div>
          </div>
          <div class="col-10 px-0">
            <div class="square px-1">
              <img class="square-content shadow-8" src="{{asset('sample_img/1.jpg')}}" />
            </div>
            <div class="square px-1">
              <img class="square-content shadow-8" src="{{asset('sample_img/2.jpg')}}" />
            </div>
            <div class="square px-1">
              <img class="square-content shadow-8" src="{{asset('sample_img/3.jpg')}}" />
            </div>
            <div class="square px-1">
              <img class="square-content shadow-8" src="{{asset('sample_img/4.jpg')}}" />
            </div>

          </div>
        </div>

        <div class="row py-3 border-bottom-xl">
          <div class="col-2">
            <div class="w-75 text-center">
              <a href="{{url('/artist_detail')}}" class="">
                <img class="shadow-8 w-100 rounded-circle" alt="Image placeholder" src="{{asset('image/pia_emote/grow.png')}}">
                <div class="text-info  font-weight-bolder mt-2">Pia Pia</div>
              </a>
              <div class="btn bg-gradient-info btn-sm mt-2">Select</div>
            </div>
          </div>
          <div class="col-10 px-0">
            <div class="square px-1">
              <img class="square-content shadow-8" src="{{asset('sample_img/1.jpg')}}" />
            </div>
            <div class="square px-1">
              <img class="square-content shadow-8" src="{{asset('sample_img/2.jpg')}}" />
            </div>
            <div class="square px-1">
              <img class="square-content shadow-8" src="{{asset('sample_img/3.jpg')}}" />
            </div>
            <div class="square px-1">
              <img class="square-content shadow-8" src="{{asset('sample_img/4.jpg')}}" />
            </div>

          </div>
        </div>

        <div class="row py-3 border-bottom-xl">
          <div class="col-2">
            <div class="w-75 text-center">
              <a href="{{url('/artist_detail')}}" class="">
                <img class="shadow-8 w-100 rounded-circle" alt="Image placeholder" src="{{asset('image/pia_emote/grow.png')}}">
                <div class="text-info  font-weight-bolder mt-2">Pia Pia</div>
              </a>
              <div class="btn bg-gradient-info btn-sm mt-2">Select</div>
            </div>
          </div>
          <div class="col-10 px-0">
            <div class="square px-1">
              <img class="square-content shadow-8" src="{{asset('sample_img/1.jpg')}}" />
            </div>
            <div class="square px-1">
              <img class="square-content shadow-8" src="{{asset('sample_img/2.jpg')}}" />
            </div>
            <div class="square px-1">
              <img class="square-content shadow-8" src="{{asset('sample_img/3.jpg')}}" />
            </div>
            <div class="square px-1">
              <img class="square-content shadow-8" src="{{asset('sample_img/4.jpg')}}" />
            </div>

          </div>
        </div>

        <div class="row py-3 border-bottom-xl">
          <div class="col-2">
            <div class="w-75 text-center">
              <a href="{{url('/artist_detail')}}" class="">
                <img class="shadow-8 w-100 rounded-circle" alt="Image placeholder" src="{{asset('image/pia_emote/grow.png')}}">
                <div class="text-info  font-weight-bolder mt-2">Pia Pia</div>
              </a>
              <div class="btn bg-gradient-info btn-sm mt-2">Select</div>
            </div>
          </div>
          <div class="col-10 px-0">
            <div class="square px-1">
              <img class="square-content shadow-8" src="{{asset('sample_img/1.jpg')}}" />
            </div>
            <div class="square px-1">
              <img class="square-content shadow-8" src="{{asset('sample_img/2.jpg')}}" />
            </div>
            <div class="square px-1">
              <img class="square-content shadow-8" src="{{asset('sample_img/3.jpg')}}" />
            </div>
            <div class="square px-1">
              <img class="square-content shadow-8" src="{{asset('sample_img/4.jpg')}}" />
            </div>

          </div>
        </div> --}}

        <div class="ms-auto text-end">
          <ul class="pagination pagination-info mt-4">
            <li class="page-item ms-auto">
              <a class="page-link" href="{{$artists->previousPageUrl()}}" aria-label="Previous">
                <span aria-hidden="true"><i class="fa fa-angle-double-left" aria-hidden="true"></i></span>
              </a>
            </li>
            @for($i = 1; $i<=$artists->lastPage();$i++)
            <li class="page-item {{$artists->currentPage() == $i ?"active":""}}">
              <a class="page-link" href="{{$artists->url($i)}}">{{$i}}</a>
            </li>
            @endfor
            <li class="page-item">
              <a class="page-link" href="{{$artists->nextPageUrl()}}" aria-label="Next">
                <span aria-hidden="true"><i class="fa fa-angle-double-right" aria-hidden="true"></i></span>
              </a>
            </li>
          </ul>
        </div>
      </div>
    </section>
  </div>
  @include('frontend/footer')
  @include('frontend/js')
  <script src="{{asset('dashboard/js/plugins/choices.min.js')}}"></script>
</body>
<div class="modal fade" id="add_artist" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">Add to prefer list</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body text-center">
        <div class="w-25 m-auto" id="modal-artist" artist-id="">
          {{-- <a href="" class="">
            <img class="shadow-8 w-100 rounded-circle" alt="Image placeholder" src="">
            <div class="text-info  font-weight-bolder mt-2"></div>
          </a> --}}
        </div>
        <div class="mt-3">
          <label for="exampleFormControlSelect1" class="ms-0">Commission: </label>
          <select class="form-select w-50 m-auto px-2" aria-label="Default select example" id="com_select">
            @foreach($commissions as $com)
              <option value="{{$com->id}}">{{$com->id}} - {{$com->project_name ?? 'No Name'}}</option>
            @endforeach
          </select>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn bg-gradient-dark btn-sm" data-bs-dismiss="modal">Close</button>
        <button type="button" class="btn bg-gradient-info btn-sm" id="confirm">Confirm</button>
      </div>
    </div>
  </div>
</div>
<script>
  $('.choice').each(function(i, obj) {
        const example = new Choices(obj, {
          searchEnabled: false,
          shouldSort: false,
        });
  });

  $( ".add-artist-btn" ).click(function() {
    $('#add_artist').modal('show')
    let avatar_html = $(this).siblings(".artist-avatar").html();
    $('#modal-artist').html(avatar_html);
    let artist_id = $(this).attr('artist-id');
    $('#modal-artist').attr('artist-id',artist_id)
  })

  $( "#confirm" ).click(function() {
    let com_id = $('#com_select :selected').val();
    let artist_id = $('#modal-artist').attr('artist-id');
    let confirm_data = {
      "_token": "{{ csrf_token() }}",
      'artist_id': artist_id,
      'com_id':com_id
    }
    $.ajax({
      method: "POST",
      url: "{{url('user/request/addpref')}}",
      data: confirm_data,
      success: function(response) {
        if(response.code == 200 ){
          toastr.success(response.message, 'Success',{"positionClass": "toast-top-center",})
          $('#add_artist').modal('hide')
        }
        else{
          toastr.error(response.message, 'Error',{"positionClass": "toast-top-center",})
        }

      }
    });


  });


</script>



</html>
