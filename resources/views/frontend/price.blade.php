
<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" itemscope itemtype="http://schema.org/WebPage">

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <title>
    Elda Solar Studio
  </title>
  @include('frontend/css')
</head>

<body class="coworking bg-gray-200">
  <!-- Navbar -->
  @include('frontend/topnav')
  <!-- -------- START HEADER 1 w/ text and image on right ------- -->
  <header>
    <div class="page-header min-vh-80" style="background-image: url('{{asset('/image/pia_bg_4k.jpg')}}');" loading="lazy">
      <span class="mask bg-gradient-dark opacity-2"></span>
      <div class="container">
        <div class="row">
          <div class="col-lg-6 col-md-7 d-flex justify-content-center text-md-start text-center flex-column mt-sm-0 mt-7">
            <h1 class="text-white">Packages & Prices</h1>
            <p class="lead pe-md-5 me-md-5 text-white opacity-8">The time is now for it be okay to be great. People in this world shun people for being nice.</p>
            <div class="buttons">
              <a href="{{url('/request')}}" type="button" class="btn bg-gradient-info mt-4">Request Now</a>
              <a href="{{url('/sample')}}" class="btn text-white shadow-none mt-4">Samples</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </header>
  <!-- -------- END HEADER 1 w/ text and image on right ------- -->
  <div class="card card-body blur shadow-blur mx-2 mx-lg-3 mt-n6 mb-4">
    <!-- -------- START Features w/ 4 cols w/ colored icon & title & text -------- -->
    <section class="py-5">
      <div class="container">
        <div class="row">
          <div class="col-md-8 mx-auto text-center">
            <h3>Pick the best package for you</h3>
            <p>You have Free Premium Support on each package.</p>
          </div>
        </div>
        <div class="row">
          <div class="col-lg-10 col-md-10 col-12 mx-auto text-center">
            <div class="nav-wrapper mt-5">
              <ul class="nav nav-pills nav-pills-css nav-fill flex-row p-1 position-relative" id="tabs-pricing-4" role="tablist">
                <li class="nav-item">
                  <a class="nav-link mb-0 active" id="tabs-iconpricing-tab-1" data-bs-toggle="tab" href="#tabs-pricing-tab-1" role="tab" aria-controls="tabs-pricing-tab-1" aria-selected="true">
                    L2D Model Art
                  </a>
                </li>
                <li class="nav-item">
                  <a class="nav-link mb-0" id="tabs-iconpricing-tab-2" data-bs-toggle="tab" href="#tabs-pricing-tab-2" role="tab" aria-controls="tabs-pricing-tab-2" aria-selected="false">
                    L2D Rigging
                  </a>
                </li>
                <li class="nav-item">
                  <a class="nav-link mb-0" id="tabs-iconpricing-tab-3" data-bs-toggle="tab" href="#tabs-pricing-tab-3" role="tab" aria-controls="tabs-pricing-tab-3" aria-selected="false">
                   Character Design
                  </a>
                </li>
                <li class="nav-item">
                  <a class="nav-link mb-0" id="tabs-iconpricing-tab-4" data-bs-toggle="tab" href="#tabs-pricing-tab-4" role="tab" aria-controls="tabs-pricing-tab-4" aria-selected="false">
                    Illustration
                  </a>
                </li>
                <li class="nav-item">
                  <a class="nav-link mb-0" id="tabs-iconpricing-tab-4" data-bs-toggle="tab" href="#tabs-pricing-tab-5" role="tab" aria-controls="tabs-pricing-tab-5" aria-selected="false">
                    Emote
                  </a>
                </li>

              </ul>
            </div>
          </div>
        </div>
        <div class="tab-content" id="v-pills-tabContent">
          <div class="tab-pane fade pb-7 show active" id="tabs-pricing-tab-1" role="tabpanel" aria-labelledby="v-pills-home-tab">
            <div class="row mt-5">
              <div class="col-lg-3 col-sm-6 mb-lg-0 mb-4">
                <div class="card h-100">
                  <div class="card-header text-sm-start text-center pt-4 pb-3 px-4">
                    <h5 class="mb-1">Basic (LV1)</h5>
                    <p class="mb-3 text-sm">LV1 Model Art</p>
                    <h3 class="font-weight-bolder mt-3">
                      $399 <small class="text-sm text-secondary font-weight-bold">+</small>
                    </h3>
                    <a href="{{url('/form/model')}}" class="btn btn-sm bg-gradient-dark w-100 border-radius-md mt-4 mb-2">REQUEST NOW</a>
                  </div>
                  <hr class="horizontal dark my-0">
                  <div class="card-body">
                    <div class="d-flex pb-3">
                      <i class="material-icons my-auto text-dark">done</i>
                      <span class="text-sm ps-3">Full body art utilizing high resolution</span>
                    </div>

                    <div class="d-flex pb-3">
                      <i class="material-icons my-auto text-dark">done</i>
                      {{-- <span class="text-sm ps-3">Rig ready PSD file with all layers of the charater seperated and prepared for binding</span> --}}
                      <span class="text-sm ps-3">Rig ready PSD file with all layers seperated</span>
                    </div>

                    <div class="d-flex pb-3">
                      <i class="material-icons my-auto text-dark">done</i>
                      <span class="text-sm ps-3">Commercial use rights</span>
                    </div>
                    <div class="btn-group dropup">
                      <div class="dropdown-toggle text-dark cursor-pointer" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-plus-circle"></i>
                        <span class="text-sm ps-3">Add-on Options</span>
                      </div>
                      <ul class="dropdown-menu px-2 py-3" aria-labelledby="dropdownMenuButton">
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Simple expression: $30 +</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Extra asset: $50 +</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Extra arm: $50 +</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Extra hair style: $100 +</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Extra outfit: $200 +</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Complex design: $100 +</li>
                        {{-- <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Extra Expressions $30+</li> --}}
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              <div class="col-lg-3 col-sm-6 mb-lg-0 mb-4">
                <div class="card h-100">
                  <div class="card-header text-sm-start text-center pt-4 pb-3 px-4">
                    <h5 class="mb-1">Standard (LV2)</h5>
                    <p class="mb-3 text-sm">LV2 Model Art</p>
                    <h3 class="font-weight-bolder mt-3">
                      $599 <small class="text-sm text-secondary font-weight-bold">+</small>
                    </h3>
                    <a href="{{url('/form/model')}}" class="btn btn-sm bg-gradient-dark w-100 border-radius-md mt-4 mb-2">REQUEST NOW</a>
                  </div>
                  <hr class="horizontal dark my-0">
                  <div class="card-body">
                    <div class="d-flex pb-3">
                      <i class="material-icons my-auto text-dark">done</i>
                      <span class="text-sm ps-3">Everything in Basic package</span>
                    </div>

                    <div class="d-flex pb-3">
                      <i class="material-icons my-auto text-dark">done</i>
                      <span class="text-sm ps-3">Free simple assets such as tails and animal ears</span>
                    </div>

                    <div class="d-flex pb-3">
                      <i class="material-icons my-auto text-dark">done</i>
                      <span class="text-sm ps-3">Detailed model art</span>
                    </div>
                    {{-- <div class="d-flex pb-3">
                      <i class="material-icons my-auto text-dark">done</i>
                      <span class="text-sm ps-3">Finer PSD layer breakdown </span>
                    </div> --}}
                    <div class="d-flex pb-3">
                      <i class="material-icons my-auto text-dark">done</i>
                      <span class="text-sm ps-3">3 free expressions</span>
                    </div>
                    <div class="btn-group dropup">
                      <div class="dropdown-toggle text-dark cursor-pointer" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-plus-circle"></i>
                        <span class="text-sm ps-3">Add-on Options</span>
                      </div>
                      <ul class="dropdown-menu px-2 py-3" aria-labelledby="dropdownMenuButton">
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Simple expression: $30 +</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Extra asset: $50 +</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Extra arm: $100 +</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Extra hair style: $150 +</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Extra outfit: $250 +</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Complex design: $150 +</li>
                        {{-- <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Extra Expressions $30+</li> --}}
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              <div class="col-lg-3 col-sm-6 mb-lg-0 mb-4">
                <div class="card h-100">
                  <div class="card-header text-sm-start text-center pt-4 pb-3 px-4">
                    <h5 class="mb-1">Advance (LV3)</h5>
                    <p class="mb-3 text-sm">LV3 Model Art</p>
                    <h3 class="font-weight-bolder mt-3">
                      $899 <small class="text-sm text-secondary font-weight-bold">+</small>
                    </h3>
                    <a href="{{url('/form/model')}}" class="btn btn-sm bg-gradient-dark w-100 border-radius-md mt-4 mb-2">REQUEST NOW</a>
                  </div>
                  <hr class="horizontal dark my-0">
                  <div class="card-body">
                    <div class="d-flex pb-3">
                      <i class="material-icons my-auto text-dark">done</i>
                      <span class="text-sm ps-3">Everything in Standard package</span>
                    </div>

                    <div class="d-flex pb-3">
                      <i class="material-icons my-auto text-dark">done</i>
                      <span class="text-sm ps-3">Character design</span>
                    </div>

                    <div class="d-flex pb-3">
                      <i class="material-icons my-auto text-dark">done</i>
                      <span class="text-sm ps-3">High quality model art</span>
                    </div>

                    <div class="d-flex pb-3">
                      <i class="material-icons my-auto text-dark">done</i>
                      <span class="text-sm ps-3">Finer PSD layer breakdown </span>
                    </div>

                    <div class="d-flex pb-3">
                      <i class="material-icons my-auto text-dark">done</i>
                      <span class="text-sm ps-3">3 free expressions</span>
                    </div>
                    <div class="btn-group dropup">
                      <div class="dropdown-toggle text-dark cursor-pointer" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-plus-circle"></i>
                        <span class="text-sm ps-3">Add-on Options</span>
                      </div>
                      <ul class="dropdown-menu px-2 py-3" aria-labelledby="dropdownMenuButton">
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Simple expression: $30 +</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Extra asset: $50 +</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Extra arm: $100 +</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Extra hair style: $150 +</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Extra outfit: $250 +</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Complex design: $150 +</li>
                        {{-- <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Extra Expressions $30+</li> --}}
                      </ul>
                    </div>
                    {{-- <div class="d-flex pb-3">
                      <i class="material-icons my-auto text-dark">done</i>
                      <span class="text-sm ps-3">3 free emotes</span>
                    </div> --}}
                  </div>
                </div>
              </div>

              <div class="col-lg-3 col-sm-6 mb-lg-0 mb-4">
                <div class="card h-100">
                  <div class="card-header text-sm-start text-center pt-4 pb-3 px-4">
                    <h5 class="mb-1">Pro (LV4)</h5>
                    <p class="mb-3 text-sm">LV4 Model Art</p>
                    <h3 class="font-weight-bolder mt-3">
                      $1299 <small class="text-sm text-secondary font-weight-bold">+</small>
                    </h3>
                    <a href="{{url('/form/model')}}" class="btn btn-sm bg-gradient-dark w-100 border-radius-md mt-4 mb-2">REQUEST NOW</a>
                  </div>
                  <hr class="horizontal dark my-0">
                  <div class="card-body">
                    <div class="d-flex pb-3">
                      <i class="material-icons my-auto text-dark">done</i>
                      <span class="text-sm ps-3">Everything in Advance package</span>
                    </div>

                    <div class="d-flex pb-3">
                      <i class="material-icons my-auto text-dark">done</i>
                      <span class="text-sm ps-3">Professional design</span>
                    </div>

                    <div class="d-flex pb-3">
                      <i class="material-icons my-auto text-dark">done</i>
                      <span class="text-sm ps-3">Professional model art</span>
                    </div>

                    <div class="d-flex pb-3">
                      <i class="material-icons my-auto text-dark">done</i>
                      <span class="text-sm ps-3">Exclusive shadow and shading seperation in PSD</span>
                    </div>

                    

                    <div class="d-flex pb-3">
                      <i class="material-icons my-auto text-dark">done</i>
                      <span class="text-sm ps-3">5 free expression</span>
                    </div>

                    {{-- <div class="d-flex pb-3">
                      <i class="material-icons my-auto text-dark">done</i>
                      <span class="text-sm ps-3">5 free emotes</span>
                    </div> --}}
                    <div class="btn-group dropup">
                      <div class="dropdown-toggle text-dark cursor-pointer" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-plus-circle"></i>
                        <span class="text-sm ps-3">Add-on Options</span>
                      </div>
                      <ul class="dropdown-menu px-2 py-3" aria-labelledby="dropdownMenuButton">
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Simple expression: $50 +</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Extra asset: $70 +</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Extra arm: $150 +</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Extra hair style: $200 +</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Extra outfit: $300 +</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Complex design: $200 +</li>
                        {{-- <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Extra Expressions $30+</li> --}}
                      </ul>
                    </div>
                  </div>
                </div>
              </div>




            </div>
            <div class="row mt-3">
              <div class="col-lg-3 col-sm-6 mb-lg-0 mb-4">
                <div class="card bg-gradient-dark h-100">
                  <div class="card-header bg-transparent text-sm-start text-center pt-4 pb-3 px-4">
                    <h5 class="mb-1 text-white">Premium (LV5)</h5>
                    <p class="mb-3 text-sm text-white">Work with our most talented artists</p>
                    <h3 class="font-weight-bolder mt-3 text-white">
                      $1799 <small class="text-sm text-white font-weight-bold">+</small>
                    </h3>
                    <a href="{{url('/form/model')}}" class="btn btn-sm btn-white w-100 border-radius-md mt-4 mb-2">REQUEST NOW</a>
                  </div>
                  <hr class="horizontal light my-0">
                  <div class="card-body">
                    <div class="d-flex pb-3">
                      <i class="material-icons my-auto text-white">done</i>
                      <span class="text-sm text-white ps-3">Everything in Pro package</span>
                    </div>
                    <div class="d-flex pb-3">
                      <i class="material-icons my-auto text-white">done</i>
                      <span class="text-sm text-white ps-3">Premium design progress</span>
                    </div>

                    <div class="d-flex pb-3">
                      <i class="material-icons my-auto text-white">done</i>
                      <span class="text-sm text-white ps-3">Premium model art</span>
                    </div>

                    <div class="d-flex pb-3">
                      <i class="material-icons my-auto text-white">done</i>
                      <span class="text-sm text-white ps-3">Detailed mouth form</span>
                    </div>

                    <div class="d-flex pb-3">
                      <i class="material-icons my-auto text-white">done</i>
                      <span class="text-sm text-white ps-3">Detailed separation for full range movement support</span>
                    </div>

                    

                    <div class="d-flex pb-3">
                      <i class="material-icons my-auto text-white">done</i>
                      <span class="text-sm text-white ps-3">5 free expressions</span>
                    </div>

                    {{-- <div class="d-flex pb-3">
                      <i class="material-icons my-auto text-white">done</i>
                      <span class="text-sm text-white ps-3">Full emote package</span>
                    </div> --}}
                    <div class="btn-group dropup">
                      <div class="dropdown-toggle text-white cursor-pointer" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-plus-circle"></i>
                        <span class="text-sm ps-3">Add-on Options</span>
                      </div>
                      <ul class="dropdown-menu px-2 py-3" aria-labelledby="dropdownMenuButton">
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Simple expression: $50 +</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Extra asset: $70 +</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Extra arm: $150 +</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Extra hair style: $200 +</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Extra outfit: $300 +</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Complex design: $200 +</li>
                        {{-- <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Extra Expressions $30+</li> --}}
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              <div class="col-lg-3 col-sm-6 mb-lg-0 mb-4">
                {{-- <div class="card bg-gradient-dark h-100">
                  <div class="card-header bg-transparent text-sm-start text-center pt-4 pb-3 px-4">
                    <h5 class="mb-1 text-white">Enterprise </h5>
                    <p class="mb-3 text-sm text-white">Free access for 200 members</p>
                    <h3 class="font-weight-bolder mt-3 text-white">
                      $2200 <small class="text-sm text-white font-weight-bold">+</small>
                    </h3>
                    <a href="javascript:;" class="btn btn-sm btn-white w-100 border-radius-md mt-4 mb-2">REQUEST NOW</a>
                  </div>
                  <hr class="horizontal light my-0">
                  <div class="card-body">
                    <div class="d-flex pb-3">
                      <i class="material-icons my-auto text-white">done</i>
                      <span class="text-sm text-white ps-3">Complete documentation</span>
                    </div>

                    <div class="d-flex pb-3">
                      <i class="material-icons my-auto text-white">done</i>
                      <span class="text-sm text-white ps-3">Working materials in Sketch</span>
                    </div>

                    <div class="d-flex pb-3">
                      <i class="material-icons my-auto text-white">done</i>
                      <span class="text-sm text-white ps-3">20GB cloud storage</span>
                    </div>

                    <div class="d-flex pb-3">
                      <i class="material-icons my-auto text-white">done</i>
                      <span class="text-sm text-white ps-3">100 team members</span>
                    </div>


                  </div>
                </div> --}}
              </div>

            </div>
          </div>
          <div class="tab-pane fade pb-7" id="tabs-pricing-tab-2" role="tabpanel" aria-labelledby="v-pills-profile-tab">
            <div class="row mt-5">
              <div class="col-lg-4 mb-lg-0 mb-4">
                <div class="card shadow-lg">
                  <span class="badge bg-light text-dark  w-lg-30 w-50 mt-n2 mx-auto">Basic(LV1)</span>
                  <div class="card-header text-center pt-4 pb-3">
                    <h1 class="font-weight-bold mt-2">
                      <small class="text-lg align-top me-1">$</small>199 - 299<small class="text-lg">+</small>
                    </h1>
                  </div>
                  <div class="card-body text-start pt-0">
                    <div class="d-flex p-2">
                      <i class="material-icons my-auto">done</i>
                      <span class="ps-3">Eyes/Brows Movement/Blink</span>
                    </div>
                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto">done</i>
                      <span class="ps-3">Mouth open & close</span>
                    </div>
                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto">done</i>
                      <span class="ps-3">Simple Head XYZ</span>
                    </div>

                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto">done</i>
                      <span class="ps-3">Simple Body XYZ</span>
                    </div>
                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto">done</i>
                      <span class="ps-3">Basic Arm + Hand movements</span>
                    </div>

                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto">done</i>
                      <span class="ps-3">Basic Hair/Body Physics</span>
                    </div>

                    

                    {{-- <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto">done</i>
                      <span class="ps-3">Basic Body Physics</span>
                    </div>

                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto">done</i>
                      <span class="ps-3">Basic Clothing Physics</span>
                    </div> --}}

                    {{-- <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto">done</i>
                      <span class="ps-3">Simple Accessories/Tail/Ear Physics (3 max)</span>
                    </div> --}}

                    {{-- <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto">done</i>
                      <span class="ps-3 ">3 Free Toggles</span>
                    </div> --}}
                    <div class="btn-group dropup p-2">
                      <div class="dropdown-toggle text-dark cursor-pointer" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-plus-circle"></i>
                        <span class="text-sm ps-3">Add-on Options</span>
                      </div>
                      <ul class="dropdown-menu px-2 py-3" aria-labelledby="dropdownMenuButton">
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Toggle Expression $20 +</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Simple Animation $40 +</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Extra Asset (Tails, Ears, Horns) $40 +</li>

                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Extra Arm Pose $80+</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Extra Hair Style $100+</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Extra Outfit $150+</li>
                        
                        <li class="dropdown-item border-radius-md text-bolder">* Some options require corresponding model art</li>
                        
                      </ul>
                    </div>
                    
                    <div class="d-lg-flex justify-content-start p-2 hidden d-none">
                      <i class="material-icons my-auto">done</i>
                      <span class="ps-3">Place holder</span>
                    </div>
                    <div class="d-lg-flex justify-content-start p-2 hidden d-none">
                      <i class="material-icons my-auto">done</i>
                      <span class="ps-3">Place holder</span>
                    </div>
                    <div class="d-lg-flex justify-content-start p-2 hidden d-none">
                      <i class="material-icons my-auto">done</i>
                      <span class="ps-3">Place holder</span>
                    </div>

                    <a href="{{url('/form/rig')}}" class="btn btn-icon bg-gradient-dark d-lg-block mt-3 mb-0 w-100">
                      REQUEST NOW
                      <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                  </div>
                </div>
              </div>

              <div class="col-lg-4 mb-lg-0 mb-4">
                <div class="card shadow-lg">
                  <span class="badge bg-light text-dark  w-lg-30 w-50 mt-n2 mx-auto">Standard(LV2)</span>
                  <div class="card-header text-center pt-4 pb-3">
                    <h1 class="font-weight-bold mt-2">
                      <small class="text-lg align-top me-1">$</small>399 - 599<small class="text-lg">+</small>
                    </h1>
                  </div>
                  <div class="card-body text-lg-start pt-0">
                    {{-- <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto">done</i>
                      <span class="ps-3">Everything in BASIC Tier</span>
                    </div> --}}
                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto">done</i>
                      <span class="ps-3">Standard Eyes Physics</span>
                    </div>

                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto">done</i>
                      <span class="ps-3">3*3 Standard Mouth Form</span>
                    </div>
                    
                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto">done</i>
                      <span class="ps-3">Standard Head XYZ (30°/10°/15°)</span>
                    </div>

                    

                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto">done</i>
                      <span class="ps-3">Standard Body XYZ</span>
                    </div>

                    

                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto">done</i>
                      <span class="ps-3">Arm/Hand Movements </span>
                    </div>

                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto">done</i>
                      <span class="ps-3">Leg Movement</span>
                    </div>

                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto">done</i>
                      <span class="ps-3">Moderate Hair/Body/Cloth Physics</span>
                    </div>

                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto  ">done</i>
                      <span class="ps-3 ">Simple Idle Animation (Tails, ears...)</span>
                    </div>

                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto">done</i>
                      <span class="ps-3 ">3 Free Toggles</span>
                    </div>

                    {{-- <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto  ">done</i>
                      <span class="ps-3 ">+1 Additional Expression Toggles</span>
                    </div> --}}
                    <div class="btn-group dropup p-2">
                      <div class="dropdown-toggle text-dark cursor-pointer" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-plus-circle"></i>
                        <span class="text-sm ps-3">Add-on Options</span>
                      </div>
                      <ul class="dropdown-menu px-2 py-3" aria-labelledby="dropdownMenuButton">
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Toggle Expression $30 +</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Simple Animation $50 +</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Extra Asset (Tails, Ears, Horns) $50 +</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Extra Arm Pose $80+</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Extra Hair Style $150+</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Extra Outfit $200+</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Complex design: $200+</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Tongue out (IOS Only) $100+</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Vbridger (IOS Only) $200+</li>
                        
                        <li class="dropdown-item border-radius-md text-bolder">* Some options require corresponding model art</li>
                      </ul>
                    </div>
                    <a href="{{url('/form/rig')}}" class="btn btn-icon bg-gradient-dark d-lg-block mt-3 mb-0 w-100">
                      REQUEST NOW
                      <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                  </div>
                </div>
              </div>

              <div class="col-lg-4 mb-lg-0 mb-4">
                <div class="card shadow-lg">
                  <span class="badge bg-light text-dark  w-lg-30 w-50 mt-n2 mx-auto">Advance (LV3)</span>
                  <div class="card-header text-center pt-4 pb-3">
                    <h1 class="font-weight-bold mt-2">
                      <small class="text-lg align-top me-1">$</small>699 - 899<small class="text-lg">+</small>
                    </h1>
                  </div>
                  <div class="card-body text-start pt-0">
                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto">done</i>
                      <span class="ps-3">Detailed Eyes Physics</span>
                    </div>

                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto">done</i>
                      <span class="ps-3">3*4 Detailed Mouth Form</span>
                    </div>

                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto">done</i>
                      <span class="ps-3">Wider Head XYZ (35°/12°/18°)</span>
                    </div>

                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto">done</i>
                      <span class="ps-3">Wider Body XYZ</span>
                    </div>

                    

                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto">done</i>
                      <span class="ps-3">Wider Range Arm/Hand Movements </span>
                    </div>
                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto">done</i>
                      <span class="ps-3">Leg Movement</span>
                    </div>

                    {{-- <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto">done</i>
                      <span class="ps-3">Simple Finger Movement </span>
                    </div> --}}

                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto">done</i>
                      <span class="ps-3">Full Body Physics</span>
                    </div>
                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto  ">done</i>
                      <span class="ps-3 ">Breathing & Idle Animation</span>
                    </div>

                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto">done</i>
                      <span class="ps-3 ">3 Free Toggles</span>
                    </div>

                    {{-- <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto">done</i>
                      <span class="ps-3">Complex Clothing Physics</span>
                    </div> --}}

                    {{-- <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto  ">done</i>
                      <span class="ps-3 ">Additional Hand/Arm Rigging</span>
                    </div> --}}

                    <div class="btn-group dropup p-2">
                      <div class="dropdown-toggle text-dark cursor-pointer" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-plus-circle"></i>
                        <span class="text-sm ps-3">Add-on Options</span>
                      </div>
                      <ul class="dropdown-menu px-2 py-3" aria-labelledby="dropdownMenuButton">
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Toggle Expression $30 +</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Simple Animation $50 +</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Extra Asset (Tails, Ears, Horns) $50 +</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Extra Arm Pose $80+</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Extra Hair Style $150+</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Extra Outfit $200+</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Complex design: $200 +</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Tongue out (IOS Only) $100+</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Vbridger (IOS Only) $200+</li>
                        <li class="dropdown-item border-radius-md text-bolder">* Some options require corresponding model art</li>
                      </ul>
                    </div>
                    <a href="{{url('/form/rig')}}" class="btn btn-icon bg-gradient-dark d-lg-block mt-3 mb-0 w-100">
                      REQUEST NOW
                      <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                  </div>
                </div>
              </div>
            </div>
            <div class="row mt-5">
              <div class="col-lg-4 mb-lg-0 mb-4">
                <div class="card shadow-lg">
                  <span class="badge bg-light text-dark  w-lg-30 w-50 mt-n2 mx-auto">Pro (LV4)</span>
                  <div class="card-header text-center pt-4 pb-3">
                    <h1 class="font-weight-bold mt-2">
                      <small class="text-lg align-top me-1">$</small>1099 - 1299<small class="text-lg">+</small>
                    </h1>
                  </div>
                  <div class="card-body text-start pt-0">
                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto">done</i>
                      <span class="ps-3">Very Detailed Eyes Physics</span>
                    </div>

                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto">done</i>
                      <span class="ps-3">4*4 Detailed Mouth Form</span>
                    </div>

                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto">done</i>
                      <span class="ps-3">Head Turns XYZ (40°/15°/20°)</span>
                    </div>

                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto">done</i>
                      <span class="ps-3">Smooth Body Movement </span>
                    </div>

                    

                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto">done</i>
                      <span class="ps-3">Arm/Hand/Finger Movements </span>
                    </div>
                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto">done</i>
                      <span class="ps-3">Leg Movement XYZ</span>
                    </div>

                    {{-- <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto">done</i>
                      <span class="ps-3">Simple Finger Movement </span>
                    </div> --}}

                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto">done</i>
                      <span class="ps-3">Detailed Full Body Physics</span>
                    </div>
                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto  ">done</i>
                      <span class="ps-3 ">Shadow/Body Interactions</span>
                    </div>

                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto">done</i>
                      <span class="ps-3 ">5 Free Toggles</span>
                    </div>

                    {{-- <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto">done</i>
                      <span class="ps-3">Complex Clothing Physics</span>
                    </div> --}}

                    {{-- <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto  ">done</i>
                      <span class="ps-3 ">Additional Hand/Arm Rigging</span>
                    </div> --}}

                    <div class="btn-group dropup p-2">
                      <div class="dropdown-toggle text-dark cursor-pointer" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-plus-circle"></i>
                        <span class="text-sm ps-3">Add-on Options</span>
                      </div>
                      <ul class="dropdown-menu px-2 py-3" aria-labelledby="dropdownMenuButton">
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Toggle Expression $50 +</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Simple Animation $70 +</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Extra Asset (Tails, Ears, Horns) $70 +</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Extra Arm Pose $100+</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Extra Hair Style $200+</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Extra Outfit $300+</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Complex design: $300 +</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Tongue out (IOS Only) $100+</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Vbridger (IOS Only) $200+</li>
                        
                        
                        <li class="dropdown-item border-radius-md text-bolder">* Some options require corresponding model art</li>
                      </ul>
                    </div>
                    <a href="{{url('/form/rig')}}" class="btn btn-icon bg-gradient-dark d-lg-block mt-3 mb-0 w-100">
                      REQUEST NOW
                      <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                  </div>
                </div>
              </div>
              
              <div class="col-lg-4 mb-lg-0 mb-4">
                <div class="card bg-gradient-dark shadow-lg">
                  <span class="badge bg-gradient-info  w-lg-30 w-50 mt-n2 mx-auto">Premium (LV5)</span>
                  <div class="card-header text-center pt-4 pb-3 bg-transparent">
                    <h1 class="font-weight-bold mt-2 text-white">
                      <small class="text-lg align-top me-1">$</small>1599 - 1799<small class="text-lg">+</small>
                    </h1>
                  </div>
                  <div class="card-body text-lg-start pt-0">
                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto text-white">done</i>
                      <span class="ps-3 text-white">Very Detailed Eyes Physics</span>
                    </div>
                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto text-white">done</i>
                      <span class="ps-3 text-white">5*5 Detailed Mouth Form</span>
                    </div>
                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto text-white">done</i>
                      <span class="ps-3 text-white">Head Turns XYZ (45°/20°/25°)</span>
                    </div>
                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto text-white">done</i>
                      <span class="ps-3 text-white">Fluid Body Movement</span>
                    </div>
                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto text-white">done</i>
                      <span class="ps-3 text-white">Hand/Arm/Finger Movement </span>
                    </div>
                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto text-white">done</i>
                      <span class="ps-3 text-white">Wider Range Leg Movement</span>
                    </div>
                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto text-white">done</i>
                      <span class="ps-3 text-white">Fluid Full Body Physics</span>
                    </div>
                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto text-white">done</i>
                      <span class="ps-3 text-white ">Enhanced Shadow/Body Interactions</span>
                    </div>
                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto text-white">done</i>
                      <span class="ps-3 text-white ">5 Free Toggles</span>
                    </div>
                    <div class="btn-group dropup p-2">
                      <div class="dropdown-toggle text-white cursor-pointer" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-plus-circle"></i>
                        <span class="text-sm ps-3">Add-on Options</span>
                      </div>
                      <ul class="dropdown-menu px-2 py-3" aria-labelledby="dropdownMenuButton">
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Toggle Expression $50 +</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Simple Animation $70 +</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Extra Asset (Tails, Ears, Horns) $70 +</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Extra Arm Pose $100+</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Extra Hair Style $200+</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Extra Outfit $300+</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Complex design: $300 +</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Tongue out (IOS Only) $100+</li>
                        <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Vbridger (IOS Only) $200+</li>
                        <li class="dropdown-item border-radius-md text-bolder">* Some options require corresponding model art</li>
                        {{-- <li><i class="dropdown-item border-radius-md" href="javascript:;">Add finger movements</i></li>
                        <li><i class="dropdown-item border-radius-md" href="javascript:;">Additional leg movements</i></li>
                        <li><i class="dropdown-item border-radius-md" href="javascript:;">Additional body movements</i></li>
                        <li><i class="dropdown-item border-radius-md" href="javascript:;">Complex hair physics</i></li>
                        <li><i class="dropdown-item border-radius-md" href="javascript:;">Additional accessories</i></li>
                        <li><i class="dropdown-item border-radius-md" href="javascript:;">Additional clothing physics</i></li>
                        <li><i class="dropdown-item border-radius-md" href="javascript:;">Additional expression toggles</i></li>
                        <li><i class="dropdown-item border-radius-md" href="javascript:;">Additional and/arm rigging</i></li>
                        <li><i class="dropdown-item border-radius-md" href="javascript:;">Additional shadow details</i></li>
                        <li><i class="dropdown-item border-radius-md" href="javascript:;">Add hip sway</i></li>
                        <li>*Some options require corresponding model art</li> --}}
                      </ul>
                    </div>
                    


                    
                    {{-- <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto text-white">done</i>
                      <span class="ps-3 text-white">Everything in EXPERT Tier</span>
                    </div>

                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto text-white">done</i>
                      <span class="ps-3 text-white">Full Range Finger Movement </span>
                    </div>

                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto text-white">done</i>
                      <span class="ps-3 text-white">Full Range Hip and Leg Movement XYZ Axis </span>
                    </div>

                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto text-white">done</i>
                      <span class="ps-3 text-white">Fully Fluid Hair Physics, Complete Hair and Shadow Breakdown</span>
                    </div>

                    

                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto text-white">done</i>
                      <span class="ps-3 text-white">Fully Fluid Clothing Physics, Complete Cloth/Body Interaction</span>
                    </div>

                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto text-white  ">done</i>
                      <span class="ps-3 text-white ">Flowy Accessoriers/Tail/Ear (All)</span>
                    </div>

                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto text-white ">done</i>
                      <span class="ps-3 text-white ">+2 Additional Expression Toggles (5 with additional cost extra)</span>
                    </div>

                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto text-white ">done</i>
                      <span class="ps-3 text-white ">+1 Additional Hand/Arm Rigging</span>
                    </div>

                    <div class="d-flex justify-content-start p-2">
                      <i class="material-icons my-auto text-white">done</i>
                      <span class="ps-3 text-white ">Fully Inhanced Shadow/Body Interactions</span>
                    </div> --}}
  
                    <a href="{{url('/form/rig')}}" class="btn btn-icon bg-gradient-info d-lg-block mt-3 mb-0 w-100">
                      Request Now
                      <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                  </div>
                </div>
              </div>
            </div>
            {{-- <div class="col-lg-4 mb-lg-0 mb-4">
              <div class="card bg-gradient-dark shadow-lg">
                <span class="badge bg-primary  w-lg-30 w-50 mt-n2 mx-auto">Premium</span>
                <div class="card-header text-center pt-4 pb-3 bg-transparent">
                  <h1 class="font-weight-bold mt-2 text-white">
                    <small class="text-lg align-top me-1">$</small>1599<small class="text-lg">+</small>
                  </h1>
                </div>
                <div class="card-body text-lg-start text-center pt-0">
                  <div class="d-flex justify-content-start p-2">
                    <i class="material-icons my-auto text-white">done</i>
                    <span class="ps-3 text-white">10 team members</span>
                  </div>

                  <div class="d-flex justify-content-start p-2">
                    <i class="material-icons my-auto text-white">done</i>
                    <span class="ps-3 text-white">40GB Cloud storage </span>
                  </div>

                  <div class="d-flex justify-content-start p-2">
                    <i class="material-icons my-auto text-white">done</i>
                    <span class="ps-3 text-white">Integration help </span>
                  </div>

                  <div class="d-flex justify-content-start p-2">
                    <i class="material-icons my-auto text-white">done</i>
                    <span class="ps-3 text-white">Sketch Files </span>
                  </div>

                  <div class="d-flex justify-content-start p-2">
                    <i class="material-icons my-auto text-white">remove</i>
                    <span class="ps-3 text-white">API Access </span>
                  </div>

                  <div class="d-flex justify-content-start p-2">
                    <i class="material-icons my-auto text-white">remove</i>
                    <span class="ps-3 text-white">Complete documentation </span>
                  </div>

                  <a href="javascript:;" class="btn btn-icon bg-gradient-primary d-lg-block mt-3 mb-0">
                    Try Premium
                    <i class="fas fa-arrow-right ms-1"></i>
                  </a>
                </div>
              </div>
            </div> --}}     
          </div>
          <div class="tab-pane fade pb-7" id="tabs-pricing-tab-3" role="tabpanel" aria-labelledby="v-pills-profile-tab">
            <div class="row mt-5">
              <div class="card">
                <div class="row">
                  <div class="col-lg-8">
                    <div class="card-body">
                      <h3 class="text-dark">Character Design</h3>
                      {{-- <p class="font-weight-normal">You have Free Unlimited Updates and Premium Support on each package. You also have 30 days to request a refund.</p> --}}
                      <div class="row mt-5 mb-2">
                        <div class="col-lg-3 col-12">
                          <h6 class="text-dark tet-uppercase">What is included</h6>
                        </div>
                        <div class="col-6">
                          <hr class="horizontal dark">
                        </div>
                      </div>
                      <div class="row">
                        <div class="col-lg-6 col-md-6 col-12 ps-0">
                          <div class="d-flex align-items-center p-2">
                            <i class="material-icons text-dark font-weight-bold">done</i>
                            <span class="ps-2">High resolution PNG </span>
                          </div>
                          <div class="d-flex align-items-center p-2">
                            <i class="material-icons text-dark font-weight-bold">done</i>
                            <span class="ps-2">Commercial use rights</span>
                          </div>
                        </div>
                        <div class="col-lg-6 col-md-6 col-12 ps-sm-2 ps-0">
                          <div class="d-flex align-items-center p-2">
                            <i class="material-icons text-dark font-weight-bold">done</i>
                            <span class="ps-2">Simple or/and transparent background</span>
                          </div>
                          {{-- <div class="d-flex align-items-center p-2">
                            <i class="material-icons text-dark font-weight-bold">done</i>
                            <span class="ps-2">Support team full assist</span>
                          </div> --}}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-4 my-auto">
                    <div class="card-body text-center">
                      <h6 class="mt-sm-4 mt-0 mb-0">Starting From</h6>
                      <h1 class="mt-0">
                        <small>$</small>399
                      </h1>
                      <a href="{{url('/form/design')}}" type="button" class="btn bg-gradient-danger btn-lg mt-2">Request Now</a>
                      <div>
                        <div class="btn-group dropdown p-2">
                          <div class="dropdown-toggle text-dark cursor-pointer" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-plus-circle"></i>
                            <span class="text-sm ps-3">Add-on Options</span>
                          </div>
                          <ul class="dropdown-menu px-2 py-3" aria-labelledby="dropdownMenuButton">
                            <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Refrence Sheet $200 +</li>
                          </ul>
                        </div>

                      </div>
                      
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
          </div>
          <div class="tab-pane fade pb-7" id="tabs-pricing-tab-4" role="tabpanel" aria-labelledby="v-pills-profile-tab">
            <div class="row mt-5">
              <div class="col-lg-4 col-md-6 ms-lg-auto mb-sm-0 mb-5">
                <div class="card text-center">
                  <div class="card-header bg-transparent">
                    <h5 class="mt-4 mb-2">BUST UP</h5>
                    <h2>
                      $200 - 300 <small class="text-sm text-secondary font-weight-bold">+</small>
                    </h2>
                  </div>
                  {{-- <div class="card-body">
                    <ul class="list-unstyled max-width-200 mx-auto">
                      <li>
                        <b>10</b> Projects
                      </li>
                      <hr class="horizontal dark">
                      <li>
                        <b>1</b> Team Members
                      </li>
                      <hr class="horizontal dark">
                      <li>
                        <b>5</b> Personal Contacts
                      </li>
                      <hr class="horizontal dark">
                      <li>
                        <b>500</b> Messages
                      </li>
                    </ul>
                    <button type="button" class="btn bg-gradient-warning mt-4">Get Access</button>
                  </div> --}}
                </div>
              </div>
              <div class="col-lg-4 col-md-6 me-lg-auto">
                <div class="card text-center">
                  <div class="card-header bg-transparent">
                    <h5 class="mt-4 mb-2">HALF BODY</h5>
                    <h2>
                      $250 - 400 <small class="text-sm text-secondary font-weight-bold">+</small>
                    </h2>
                  </div>
                  {{-- <div class="card-body">
                    <ul class="list-unstyled max-width-200 mx-auto">
                      <li>
                        <b>10</b> Projects
                      </li>
                      <hr class="horizontal dark">
                      <li>
                        <b>1</b> Team Members
                      </li>
                      <hr class="horizontal dark">
                      <li>
                        <b>5</b> Personal Contacts
                      </li>
                      <hr class="horizontal dark">
                      <li>
                        <b>500</b> Messages
                      </li>
                    </ul>
                    <button type="button" class="btn bg-gradient-warning mt-4">Get Access</button>
                  </div> --}}
                </div>
              </div>
              <div class="col-lg-4 col-md-6 me-lg-auto">
                <div class="card text-center bg-gradient-info">
                  <div class="card-header bg-transparent">
                    <h5 class="mt-4 mb-2 text-white">FULL BODY</h5>
                    <h2 class="text-white">
                      $300 - 500 <small class="text-sm">+</small>
                    </h2>
                  </div>
                  {{-- <div class="card-body">
                    <ul class="list-unstyled max-width-200 mx-auto text-white">
                      <li>
                        <b>100</b> Projects
                      </li>
                      <hr class="horizontal light">
                      <li>
                        <b>25</b> Team Members
                      </li>
                      <hr class="horizontal light">
                      <li>
                        <b>15</b> Personal Contacts
                      </li>
                      <hr class="horizontal light">
                      <li>
                        <b>5K</b> Messages
                      </li>
                    </ul>
                    <button type="button" class="btn btn-white mt-4">Get Access</button>
                  </div> --}}
                </div>
              </div>
            </div>

            <div class="card mt-5">
              <div class="row">
                <div class="col-lg-12">
                  <div class="card-body">
                    <h5 class="mb-2">Additional Cost</h5>
                    {{-- <p class="font-weight-normal">You have Free Unlimited Updates and Premium Support on each package. You also have 30 days to request a refund.</p> --}}
                    {{-- <div class="row mt-5 mb-2">
                      <div class="col-lg-3 col-12">
                        <h6 class="text-dark tet-uppercase">What is included</h6>
                      </div>
                      <div class="col-6">
                        <hr class="horizontal dark">
                      </div>
                    </div> --}}
                    <div class="row">
                      <div class="col-lg-4 col-md-4 col-6 ps-0">
                        <div class="d-flex align-items-center p-2">
                          <i class="material-icons text-dark font-weight-bold">done</i>
                          <span class="ps-2">Complex Background 100 - 500 +</span>
                        </div>
                        <div class="d-flex align-items-center p-2">
                          <i class="material-icons text-dark font-weight-bold">done</i>
                          <span class="ps-2">Animation 300 +</span>
                        </div>
                      </div>
                      {{--  --}}
                    </div>
                  </div>
                </div>
                {{-- <div class="col-lg-4 my-auto">
                  <div class="card-body text-center">
                    <h6 class="mt-sm-4 mt-0 mb-0">Starting From</h6>
                    <h1 class="mt-0">
                      <small>$</small>399
                    </h1>
                    <button type="button" class="btn bg-gradient-danger btn-lg mt-2">Request Now</button>
                    <div>
                      <div class="btn-group dropdown p-2">
                        <div class="dropdown-toggle text-dark cursor-pointer" data-bs-toggle="dropdown" aria-expanded="false">
                          <i class="fas fa-plus-circle"></i>
                          <span class="text-sm ps-3">Add-on Options</span>
                        </div>
                        <ul class="dropdown-menu px-2 py-3" aria-labelledby="dropdownMenuButton">
                          <li class="dropdown-item border-radius-md"><i class="fas fa-user-plus"></i> Refrence Sheet $200 +</li>
                        </ul>
                      </div>

                    </div>
                    
                  </div>
                </div> --}}
              </div>
            </div>
            </div>
          
          <div class="tab-pane fade pb-7" id="tabs-pricing-tab-5" role="tabpanel" aria-labelledby="v-pills-profile-tab">
            <div class="mt-5">
              <div class="container">
                <div class="row">
                  <div class="col-lg-3 mb-lg-auto mb-4 my-auto p-md-0 ms-auto">
                    <div class="card border-radius-top-end-lg-0 border-radius-bottom-end-lg-0">
                      <div class="card-header text-center pt-4 pb-3">
                        <h6 class="text-dark opacity-8 mb-0">Static Emote</h6>
                        <h1 class="font-weight-bolder">
                          <small>$</small>25<small class="text-sm">+ / each</small>
                        </h1>
                      </div>
                      <div class="card-body mx-auto pt-0">
                        <div class="justify-content-start d-flex px-2 py-1">
                          <div>
                            <i class="fas fa-check text-dark opacity-6 text-sm"></i>
                          </div>
                          <div class="ps-2">
                            <span class="text-sm">Custom Emote</span>
                          </div>
                        </div>
                        <div class="justify-content-start d-flex px-2 py-1">
                          <div>
                            <i class="fas fa-check text-dark opacity-6 text-sm"></i>
                          </div>
                          <div class="ps-2">
                            <span class="text-sm">Custom text/font</span>
                          </div>
                        </div>
                        <div class="justify-content-start d-flex px-2 py-1">
                          <div>
                            <i class="fas fa-check text-dark opacity-6 text-sm"></i>
                          </div>
                          <div class="ps-2">
                            <span class="text-sm">PNG file</span>
                          </div>
                        </div>
                        <div class="justify-content-start d-flex px-2 py-1">
                          <div>
                            <i class="fas fa-check text-dark opacity-6 text-sm"></i>
                          </div>
                          <div class="ps-2">
                            <span class="text-sm">Different Resolutions</span>
                          </div>
                        </div>
          
                        {{-- <div class="justify-content-start d-flex px-2 py-1">
                          <div>
                            <i class="fas fa-check text-dark opacity-6 text-sm"></i>
                          </div>
                          <div class="ps-2">
                            <span class="text-sm">Min Order 5</span>
                          </div>
                        </div> --}}
                      </div>
                      <div class="card-footer pt-0">
                        <a href="{{url('/form/emote')}}" class="btn w-100 bg-gradient-dark mb-0">
                          Request now
                        </a>
                      </div>
                    </div>
                  </div>
          
                  <div class="col-lg-3 p-md-0 mb-lg-auto mb-4 z-index-2">
                    <div class="card">
                      <div class="card-header text-center pt-4 pb-3">
                        <h6 class="text-dark opacity-8 text mb-0 mt-2">Animated Emote</h6>
                        <h1 class="font-weight-bolder">
                          <small>$</small>70<small class="text-sm">+ / each</small>
                        </h1>
                      </div>
                      <div class="card-body mx-auto pt-0">
                        <div class="justify-content-start d-flex px-2 py-1">
                          <div>
                            <i class="fas fa-check text-dark opacity-6 text-sm"></i>
                          </div>
                          <div class="ps-2">
                            <span class="text-sm">Custom Animation</span>
                          </div>
                        </div>
                        <div class="justify-content-start d-flex px-2 py-1">
                          <div>
                            <i class="fas fa-check text-dark opacity-6 text-sm"></i>
                          </div>
                          <div class="ps-2">
                            <span class="text-sm">PNG File</span>
                          </div>
                        </div>
                        <div class="justify-content-start d-flex px-2 py-1">
                          <div>
                            <i class="fas fa-check text-dark opacity-6 text-sm"></i>
                          </div>
                          <div class="ps-2">
                            <span class="text-sm">Gif File</span>
                          </div>
                        </div>
          
                        <div class="justify-content-start d-flex px-2 py-1">
                          <div>
                            <i class="fas fa-check text-dark opacity-6 text-sm"></i>
                          </div>
                          <div class="ps-2">
                            <span class="text-sm">Different Resolutions</span>
                          </div>
                        </div>
          
                        <div class="justify-content-start d-flex px-2 py-1">
                          <div>
                            <i class="fas fa-check text-dark opacity-6 text-sm"></i>
                          </div>
                          <div class="ps-2">
                            <span class="text-sm">Different FPS</span>
                          </div>
                        </div>
                      </div>
                      <div class="card-footer pt-0">
                        <a href="{{url('form/emote')}}" class="btn btn-icon bg-gradient-info d-block mb-0 icon-move-right">Request Now
                          <i class="fas fa-arrow-right ms-2" aria-hidden="true"></i>
                        </a>
                      </div>
                    </div>
                  </div>
          
                  <div class="col-lg-3 mb-lg-auto mb-4 my-auto p-md-0 me-auto">
                    <div class="card border-radius-top-start-lg-0 border-radius-bottom-start-lg-0">
                      <div class="card-header text-center pt-4 pb-2">
                        <h6 class="text-dark opacity-8 text mb-0">Template Based</h6>
                        <h1 class="font-weight-bolder">
                          <small>$</small>100<small class="text-sm">+ / package</small>
                        </h1>
                      </div>
                      <div class="card-body mx-auto pt-0">
                        <div class="justify-content-start d-flex px-2 py-1">
                          <div>
                            <i class="fas fa-check text-dark opacity-6 text-sm"></i>
                          </div>
                          <div class="ps-2">
                            <span class="text-sm">30 + emotes</span>
                          </div>
                        </div>
                        
                        <div class="justify-content-start d-flex px-2 py-1">
                          <div>
                            <i class="fas fa-check text-dark opacity-6 text-sm"></i>
                          </div>
                          <div class="ps-2">
                            <span class="text-sm">Custom text/font</span>
                          </div>
                        </div>
                        <div class="justify-content-start d-flex px-2 py-1">
                          <div>
                            <i class="fas fa-check text-dark opacity-6 text-sm"></i>
                          </div>
                          <div class="ps-2">
                            <span class="text-sm">PNG file</span>
                          </div>
                        </div>
                        <div class="justify-content-start d-flex px-2 py-1">
                          <div>
                            <i class="fas fa-check text-dark opacity-6 text-sm"></i>
                          </div>
                          <div class="ps-2">
                            <span class="text-sm">Different Resolutions</span>
                          </div>
                        </div>
                      </div>
                      <div class="card-footer pt-0">
                        <a href="{{url('/form/emote')}}" class="btn w-100 bg-gradient-dark mb-0">Request Now</a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- START Testimonials w/ 3 cards & dark background -->
    {{-- <section class="py-3 position-relative overflow-hidden">
      <img src="../../assets/img/shapes/pattern-lines.svg" class="position-absolute opacity-2 h-100 top-0 d-md-block d-none" alt="">
      <div class="container pt-6 pb-5 position-relative z-index-3">
        <div class="row">
          <div class="col-md-6 mx-auto text-center">
            <span class="badge badge-white text-dark mb-2">Testimonials</span>
            <h2 class=" mb-3">Some thoughts from our clients</h2>
            <h5 class=" font-weight-light">
              If you’re selected for them you’ll also get three tickets, opportunity to access Investor Office Hours and Mentor Hours and much more all for free.
            </h5>
          </div>
        </div>
        <div class="row mt-8">
          <div class="col-md-4 mb-md-0 mb-7">
            <div class="card">
              <div class="text-center mt-n5 z-index-1">
                <div class="position-relative">
                  <div class="blur-shadow-avatar rounded-circle">
                    <img class="avatar avatar-xxl shadow-lg" src="../../assets/img/team-2.jpg" alt="avatar">
                  </div>
                </div>
              </div>
              <div class="card-body text-center pb-0">
                <h4 class="mb-0">Olivia Harper</h4>
                <p>@oliviaharper</p>
                <p class="mt-2">
                  The connections you make at Web Summit are unparalleled, we met users all over the world.
                </p>
              </div>
              <div class="card-footer text-center pt-2">
                <div class="mx-auto">
                  <svg class="opacity-2" width="60px" height="60px" viewBox="0 0 270 270" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                      <title>quote-down</title>
                      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                          <path d="M107.000381,49.033238 C111.792099,48.01429 115.761022,48.6892564 116.625294,50.9407629 C117.72393,53.8028077 113.174473,58.3219079 107.017635,60.982801 C107.011653,60.9853863 107.00567,60.9879683 106.999688,60.990547 C106.939902,61.0219589 106.879913,61.0439426 106.820031,61.0659514 C106.355389,61.2618887 105.888177,61.4371549 105.421944,61.5929594 C88.3985192,68.1467602 80.3242628,76.9161885 70.3525495,90.6906738 C60.0774843,104.884196 54.9399518,119.643717 54.9399518,134.969238 C54.9399518,138.278158 55.4624127,140.716309 56.5073346,142.283691 C57.2039492,143.328613 57.9876406,143.851074 58.8584088,143.851074 C59.7291771,143.851074 61.0353294,143.241536 62.7768659,142.022461 C68.3497825,138.016927 75.4030052,136.01416 83.9365338,136.01416 C93.8632916,136.01416 102.658051,140.063232 110.320811,148.161377 C117.983572,156.259521 121.814952,165.88151 121.814952,177.027344 C121.814952,188.695638 117.417572,198.970703 108.622813,207.852539 C99.828054,216.734375 89.1611432,221.175293 76.6220807,221.175293 C61.9931745,221.175293 49.3670351,215.166992 38.7436627,203.150391 C28.1202903,191.133789 22.8086041,175.024577 22.8086041,154.822754 C22.8086041,131.312012 30.0359804,110.239421 44.490733,91.6049805 C58.2196377,73.906272 74.3541002,59.8074126 102.443135,50.4450748 C102.615406,50.3748509 102.790055,50.3058192 102.966282,50.2381719 C104.199241,49.7648833 105.420135,49.3936487 106.596148,49.1227802 L107,49 Z M233.000381,49.033238 C237.792099,48.01429 241.761022,48.6892564 242.625294,50.9407629 C243.72393,53.8028077 239.174473,58.3219079 233.017635,60.982801 C233.011653,60.9853863 233.00567,60.9879683 232.999688,60.990547 C232.939902,61.0219589 232.879913,61.0439426 232.820031,61.0659514 C232.355389,61.2618887 231.888177,61.4371549 231.421944,61.5929594 C214.398519,68.1467602 206.324263,76.9161885 196.352549,90.6906738 C186.077484,104.884196 180.939952,119.643717 180.939952,134.969238 C180.939952,138.278158 181.462413,140.716309 182.507335,142.283691 C183.203949,143.328613 183.987641,143.851074 184.858409,143.851074 C185.729177,143.851074 187.035329,143.241536 188.776866,142.022461 C194.349783,138.016927 201.403005,136.01416 209.936534,136.01416 C219.863292,136.01416 228.658051,140.063232 236.320811,148.161377 C243.983572,156.259521 247.814952,165.88151 247.814952,177.027344 C247.814952,188.695638 243.417572,198.970703 234.622813,207.852539 C225.828054,216.734375 215.161143,221.175293 202.622081,221.175293 C187.993174,221.175293 175.367035,215.166992 164.743663,203.150391 C154.12029,191.133789 148.808604,175.024577 148.808604,154.822754 C148.808604,131.312012 156.03598,110.239421 170.490733,91.6049805 C184.219638,73.906272 200.3541,59.8074126 228.443135,50.4450748 C228.615406,50.3748509 228.790055,50.3058192 228.966282,50.2381719 C230.199241,49.7648833 231.420135,49.3936487 232.596148,49.1227802 L233,49 Z" fill="#48484A" fill-rule="nonzero" transform="translate(135.311778, 134.872794) scale(-1, -1) translate(-135.311778, -134.872794) "></path>
                      </g>
                  </svg>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-4 mb-md-0 mb-7">
            <div class="card">
              <div class="text-center mt-n5 z-index-1">
                <div class="position-relative">
                  <div class="blur-shadow-avatar rounded-circle">
                    <img class="avatar avatar-xxl shadow-lg" src="../../assets/img/team-3.jpg" alt="avatar">
                  </div>
                </div>
              </div>
              <div class="card-body text-center pb-0">
                <h4 class="mb-0">Simon Lauren</h4>
                <p>@simonlaurent</p>
                  <p class="mt-2">
                    The networking at Web Summit is like no other European tech conference. Everything is amazing.
                  </p>
              </div>
              <div class="card-footer text-center pt-2">
                <div class="mx-auto">
                  <svg class="opacity-2" width="60px" height="60px" viewBox="0 0 270 270" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                      <title>quote-down</title>
                      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                          <path d="M107.000381,49.033238 C111.792099,48.01429 115.761022,48.6892564 116.625294,50.9407629 C117.72393,53.8028077 113.174473,58.3219079 107.017635,60.982801 C107.011653,60.9853863 107.00567,60.9879683 106.999688,60.990547 C106.939902,61.0219589 106.879913,61.0439426 106.820031,61.0659514 C106.355389,61.2618887 105.888177,61.4371549 105.421944,61.5929594 C88.3985192,68.1467602 80.3242628,76.9161885 70.3525495,90.6906738 C60.0774843,104.884196 54.9399518,119.643717 54.9399518,134.969238 C54.9399518,138.278158 55.4624127,140.716309 56.5073346,142.283691 C57.2039492,143.328613 57.9876406,143.851074 58.8584088,143.851074 C59.7291771,143.851074 61.0353294,143.241536 62.7768659,142.022461 C68.3497825,138.016927 75.4030052,136.01416 83.9365338,136.01416 C93.8632916,136.01416 102.658051,140.063232 110.320811,148.161377 C117.983572,156.259521 121.814952,165.88151 121.814952,177.027344 C121.814952,188.695638 117.417572,198.970703 108.622813,207.852539 C99.828054,216.734375 89.1611432,221.175293 76.6220807,221.175293 C61.9931745,221.175293 49.3670351,215.166992 38.7436627,203.150391 C28.1202903,191.133789 22.8086041,175.024577 22.8086041,154.822754 C22.8086041,131.312012 30.0359804,110.239421 44.490733,91.6049805 C58.2196377,73.906272 74.3541002,59.8074126 102.443135,50.4450748 C102.615406,50.3748509 102.790055,50.3058192 102.966282,50.2381719 C104.199241,49.7648833 105.420135,49.3936487 106.596148,49.1227802 L107,49 Z M233.000381,49.033238 C237.792099,48.01429 241.761022,48.6892564 242.625294,50.9407629 C243.72393,53.8028077 239.174473,58.3219079 233.017635,60.982801 C233.011653,60.9853863 233.00567,60.9879683 232.999688,60.990547 C232.939902,61.0219589 232.879913,61.0439426 232.820031,61.0659514 C232.355389,61.2618887 231.888177,61.4371549 231.421944,61.5929594 C214.398519,68.1467602 206.324263,76.9161885 196.352549,90.6906738 C186.077484,104.884196 180.939952,119.643717 180.939952,134.969238 C180.939952,138.278158 181.462413,140.716309 182.507335,142.283691 C183.203949,143.328613 183.987641,143.851074 184.858409,143.851074 C185.729177,143.851074 187.035329,143.241536 188.776866,142.022461 C194.349783,138.016927 201.403005,136.01416 209.936534,136.01416 C219.863292,136.01416 228.658051,140.063232 236.320811,148.161377 C243.983572,156.259521 247.814952,165.88151 247.814952,177.027344 C247.814952,188.695638 243.417572,198.970703 234.622813,207.852539 C225.828054,216.734375 215.161143,221.175293 202.622081,221.175293 C187.993174,221.175293 175.367035,215.166992 164.743663,203.150391 C154.12029,191.133789 148.808604,175.024577 148.808604,154.822754 C148.808604,131.312012 156.03598,110.239421 170.490733,91.6049805 C184.219638,73.906272 200.3541,59.8074126 228.443135,50.4450748 C228.615406,50.3748509 228.790055,50.3058192 228.966282,50.2381719 C230.199241,49.7648833 231.420135,49.3936487 232.596148,49.1227802 L233,49 Z" fill="#48484A" fill-rule="nonzero" transform="translate(135.311778, 134.872794) scale(-1, -1) translate(-135.311778, -134.872794) "></path>
                      </g>
                  </svg>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-4 mb-md-0 mb-7">
            <div class="card">
              <div class="text-center mt-n5 z-index-1">
                <div class="position-relative">
                  <div class="blur-shadow-avatar rounded-circle">
                    <img class="avatar avatar-xxl shadow-lg" src="../../assets/img/team-4.jpg" alt="avatar">
                  </div>
                </div>
              </div>
              <div class="card-body text-center pb-0">
                <h4 class="mb-0">Lucian Eurel</h4>
                <p>@luciaeurel</p>
                <p class="mt-2">
                  Web Summit will increase your appetite, your inspiration, your motivation and your network.
                </p>
              </div>
              <div class="card-footer text-center pt-2">
                <div class="mx-auto">
                  <svg class="opacity-2" width="60px" height="60px" viewBox="0 0 270 270" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                      <title>quote-down</title>
                      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                          <path d="M107.000381,49.033238 C111.792099,48.01429 115.761022,48.6892564 116.625294,50.9407629 C117.72393,53.8028077 113.174473,58.3219079 107.017635,60.982801 C107.011653,60.9853863 107.00567,60.9879683 106.999688,60.990547 C106.939902,61.0219589 106.879913,61.0439426 106.820031,61.0659514 C106.355389,61.2618887 105.888177,61.4371549 105.421944,61.5929594 C88.3985192,68.1467602 80.3242628,76.9161885 70.3525495,90.6906738 C60.0774843,104.884196 54.9399518,119.643717 54.9399518,134.969238 C54.9399518,138.278158 55.4624127,140.716309 56.5073346,142.283691 C57.2039492,143.328613 57.9876406,143.851074 58.8584088,143.851074 C59.7291771,143.851074 61.0353294,143.241536 62.7768659,142.022461 C68.3497825,138.016927 75.4030052,136.01416 83.9365338,136.01416 C93.8632916,136.01416 102.658051,140.063232 110.320811,148.161377 C117.983572,156.259521 121.814952,165.88151 121.814952,177.027344 C121.814952,188.695638 117.417572,198.970703 108.622813,207.852539 C99.828054,216.734375 89.1611432,221.175293 76.6220807,221.175293 C61.9931745,221.175293 49.3670351,215.166992 38.7436627,203.150391 C28.1202903,191.133789 22.8086041,175.024577 22.8086041,154.822754 C22.8086041,131.312012 30.0359804,110.239421 44.490733,91.6049805 C58.2196377,73.906272 74.3541002,59.8074126 102.443135,50.4450748 C102.615406,50.3748509 102.790055,50.3058192 102.966282,50.2381719 C104.199241,49.7648833 105.420135,49.3936487 106.596148,49.1227802 L107,49 Z M233.000381,49.033238 C237.792099,48.01429 241.761022,48.6892564 242.625294,50.9407629 C243.72393,53.8028077 239.174473,58.3219079 233.017635,60.982801 C233.011653,60.9853863 233.00567,60.9879683 232.999688,60.990547 C232.939902,61.0219589 232.879913,61.0439426 232.820031,61.0659514 C232.355389,61.2618887 231.888177,61.4371549 231.421944,61.5929594 C214.398519,68.1467602 206.324263,76.9161885 196.352549,90.6906738 C186.077484,104.884196 180.939952,119.643717 180.939952,134.969238 C180.939952,138.278158 181.462413,140.716309 182.507335,142.283691 C183.203949,143.328613 183.987641,143.851074 184.858409,143.851074 C185.729177,143.851074 187.035329,143.241536 188.776866,142.022461 C194.349783,138.016927 201.403005,136.01416 209.936534,136.01416 C219.863292,136.01416 228.658051,140.063232 236.320811,148.161377 C243.983572,156.259521 247.814952,165.88151 247.814952,177.027344 C247.814952,188.695638 243.417572,198.970703 234.622813,207.852539 C225.828054,216.734375 215.161143,221.175293 202.622081,221.175293 C187.993174,221.175293 175.367035,215.166992 164.743663,203.150391 C154.12029,191.133789 148.808604,175.024577 148.808604,154.822754 C148.808604,131.312012 156.03598,110.239421 170.490733,91.6049805 C184.219638,73.906272 200.3541,59.8074126 228.443135,50.4450748 C228.615406,50.3748509 228.790055,50.3058192 228.966282,50.2381719 C230.199241,49.7648833 231.420135,49.3936487 232.596148,49.1227802 L233,49 Z" fill="#48484A" fill-rule="nonzero" transform="translate(135.311778, 134.872794) scale(-1, -1) translate(-135.311778, -134.872794) "></path>
                      </g>
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section> --}}

    <section class="py-4">
      <div class="container">
        <div class="row my-5">
          <div class="col-md-6 mx-auto text-center">
            <h2>Frequently Asked Questions</h2>
            <p>Some of your questions and concern may be found here, or you can see our <a href="{{url('/faq')}}">FAQ </a>page. </p>
          </div>
        </div>
        <div class="row">
          <div class="col-md-10 mx-auto">
            <div class="accordion" id="accordionRental">
              <div class="accordion-item mb-3">
                <h5 class="accordion-header" id="headingOne">
                  <button class="accordion-button border-bottom font-weight-bold" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                    How do I order?
                    <i class="collapse-close fa fa-plus text-xs pt-1 position-absolute end-0"></i>
                    <i class="collapse-open fa fa-minus text-xs pt-1 position-absolute end-0"></i>
                  </button>
                </h5>
                <div id="collapseOne" class="accordion-collapse collapse show" aria-labelledby="headingOne" data-bs-parent="#accordionRental">
                  <div class="accordion-body text-sm opacity-8">
                    You may use the link <a href="{{url('/request')}}">REQUEST NOW</a>to submit a commission request. You will also need an account to submit a request, please go to the <a href="{{url('signup')}}">SIGNUP</a> PAGE to register for an account.
                  </div>
                </div>
              </div>
              <div class="accordion-item mb-3">
                <h5 class="accordion-header" id="headingTwo">
                  <button class="accordion-button border-bottom font-weight-bold" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                    How can i make the payment?
                    <i class="collapse-close fa fa-plus text-xs pt-1 position-absolute end-0"></i>
                    <i class="collapse-open fa fa-minus text-xs pt-1 position-absolute end-0"></i>
                  </button>
                </h5>
                <div id="collapseTwo" class="accordion-collapse collapse" aria-labelledby="headingTwo" data-bs-parent="#accordionRental">
                  <div class="accordion-body text-sm opacity-8">
                    We have integrated payment solutions that you can make payment with, such as <a href='https://www.paypal.com/' target="_blank">Paypal</a> or <a href='https://stripe.com/' target="_blank">Stripe</a>, but we also offer other alternatives if the offered options are not available.
                    <br>Please talk to us after you make a request and we will try to help you with the payment process.
                  </div>
                </div>
              </div>
              <div class="accordion-item mb-3">
                <h5 class="accordion-header" id="headingThree">
                  <button class="accordion-button border-bottom font-weight-bold" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                    How much time does it take to receive the order?
                    <i class="collapse-close fa fa-plus text-xs pt-1 position-absolute end-0"></i>
                    <i class="collapse-open fa fa-minus text-xs pt-1 position-absolute end-0"></i>
                  </button>
                </h5>
                <div id="collapseThree" class="accordion-collapse collapse" aria-labelledby="headingThree" data-bs-parent="#accordionRental">
                  <div class="accordion-body text-sm opacity-8">
                    Each order will usually take around 1 month from start to finish, though time do vary based on amount of content created for the order or the time communication takes.
                    We usually encourage client give us feedback within 1-3 days to prevent delay on the project, and we have multiple deliverable stages to ensure each order is on track to the best of our abilities.
                  </div>
                </div>
              </div>
              <div class="accordion-item mb-3">
                <h5 class="accordion-header" id="headingFour">
                  <button class="accordion-button border-bottom font-weight-bold" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFour" aria-expanded="false" aria-controls="collapseFour">
                    Can I resell the products?
                    <i class="collapse-close fa fa-plus text-xs pt-1 position-absolute end-0"></i>
                    <i class="collapse-open fa fa-minus text-xs pt-1 position-absolute end-0"></i>
                  </button>
                </h5>
                <div id="collapseFour" class="accordion-collapse collapse" aria-labelledby="headingFour" data-bs-parent="#accordionRental">
                  <div class="accordion-body text-sm opacity-8">
                    We do allow for resell on some of our Licences, please check our <a href="{{url('/terms')}}">Terms and Conditions</a> for more details or talk to us about it, we are happy to explain to you.
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>




    <!-- -------- END PRE-FOOTER 8 w/ TEXT, BG IMAGE AND 2 BUTTONS ------- -->
  </div>
  @include('frontend/footer')
  @include('frontend/js')
  <script>
    window.dispatchEvent(new Event('resize'));
  </script>
</body>

</html>
