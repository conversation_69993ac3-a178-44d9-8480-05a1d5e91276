
<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" itemscope itemtype="http://schema.org/WebPage">

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <title>
    ESS Product
  </title>
  @include('frontend/css')
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@8/swiper-bundle.min.css"/>
</head>

<body class="coworking bg-gray-200">
  <!-- Navbar -->
  @include('frontend/topnav')
  <!-- -------- START HEADER 1 w/ text and image on right ------- -->
  <header>
    <div class="page-header min-vh-60" style="background-image: url('{{asset('/image/raingirl_2.jpg')}}');background-position:top" loading="lazy">
      <span class="mask bg-gradient-dark opacity-7"></span>
      <div class="container">
        
      </div>
    </div>
  </header>
  <!-- -------- END HEADER 1 w/ text and image on right ------- -->
  <div class="card card-body blur shadow-blur mx-2 mx-lg-3 mt-n6 mb-4">
    <a href="{{url('cart')}}" class="btn btn-outline-dark ms-auto position-sticky top-3" type="button" name="button">
      <i class="material-icons me-2">shopping_cart</i><span id="cart_count">{{count(session()->get('cart')??[])}}</span>
    </a>
    <div class="container">
      <div class="row flex-row">
        <div class="col-lg-6 text-center">
          <div class="text-start w-100 mx-auto">
            <a href="{{url('/shop')}}" class="btn btn-outline-info btn-sm">Back To Shop</a>
          </div>
        </div>
      </div>    
    </div>
    
    <div class="section my-lg-5">
      <div class="container">
        <div class="row flex-row">
          <div class="col-lg-6">
            <div class="swiper top-swiper">
              <div class="swiper-wrapper">
                <!-- Slides -->
                @foreach($product->images as $image)
                <div class="swiper-slide">
                  <div class="text-start w-90 border-radius-lg border cursor-pointer" data-fancybox="product-{{$product->id}}" href="{{asset('storage/'.$image)}}">
                    <img class="w-100 aspect-ratio-1 object-fit-contain p-2" src="{{asset('storage/'.$image)}}" alt="ladydady" loading="lazy">
                  </div>
                </div>
                @endforeach     
              </div>
            </div>
            <div class="position-relative w-90 mt-2">
              <div class="swiper bottom-swiper ">
                <div class="swiper-wrapper">
                  <!-- Slides -->
                  @foreach($product->images as $image)
                  <div class="swiper-slide">
                    <div class="text-center w-100 mx-auto border-radius-lg border">
                      <img class="w-100 aspect-ratio-1 object-fit-contain p-2" src="{{asset('storage/'.$image)}}" alt="ladydady" loading="lazy">
                    </div>
                  </div>
                  @endforeach     
                </div>
                
              </div>
              <div class="swiper-button-prev opacity-5 text-dark"></div>
              <div class="swiper-button-next opacity-5 text-dark"></div>
            </div>
          </div>
          <div class="col-lg-6">
            <div>
              <h3 class="mt-lg-0 mt-4">{{$product->name}}</h3>
             
              <h6 class="mb-0 mt-2">Price</h6>
              
              <h5 class="badge badge-success" id="sku_price">${{$product->price}}</h5>

              <div class="row mt-3">
                @foreach($attributes as $attr)
                  <div class="col-lg-7 mt-lg-0 mt-2">
                  <label class="ms-0 mb-0 text-xs">{{$attr->attribute_name}}</label>
                  <select class="form-control choice sku-select border px-2" name="choices-material" attr-name="{{$attr->attribute_name}}" id="sku-select-{{$attr->attribute_name}}">
                    @foreach($attr->options as $option)
                      <option value="{{$option->option_name}}">{{$option->option_name}}</option>
                    @endforeach
                  </select>
                </div>
                @endforeach
                {{-- <div class="col-lg-7 mt-lg-0 mt-2">
                  <label class="ms-0 mb-0 text-xs">Options</label>
                  <select class="form-control" name="choices-material" id="choices-material">
                    <option value="Choice 1" selected>Black</option>
                    <option value="Choice 2">Blue</option>
                    <option value="Choice 3">Gray</option>
                    <option value="Choice 4">Brown</option>
                  </select>
                </div> --}}
                
                {{-- <div class="col-lg-7 mt-lg-0 mt-2">
                  <label class="ms-0 mb-0 text-xs">Select Size</label>
                  <select class="form-control" name="choices-colors" id="choices-colors">
                    <option value="Choice 1" selected>Small</option>
                    <option value="Choice 2">Extra Small</option>
                    <option value="Choice 3">Medium</option>
                    <option value="Choice 4">Large</option>
                    <option value="Choice 5">XXL</option>
                  </select>
                </div> --}}
              </div>

              <div class="text-end mt-2">
                <button class="btn bg-gradient-info mb-0 mt-lg-auto btn-add-cart" type="button" name="button">Add To Cart</button>
              </div>
             
              <div class="accordion mt-5" id="accordionProduct">
                <div class="accordion-item mb-1">
                  <h6 class="accordion-header" id="headingOne">
                    <button class="accordion-button border-bottom font-weight-bold text-start" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="false" aria-controls="collapseOne">
                      Description
                      <i class="collapse-close fa fa-plus text-xs pt-1 position-absolute end-0"></i>
                      <i class="collapse-open fa fa-minus text-xs pt-1 position-absolute end-0"></i>
                    </button>
                  </h6>
                  <div id="collapseOne" class="accordion-collapse collapse" aria-labelledby="headingOne" >
                    <div class="accordion-body text-sm opacity-8">
                      {!!$product->description!!}
                    </div>
                  </div>
                </div>
                <div class="accordion-item mb-1">
                  <h6 class="accordion-header" id="headingThree">
                    <button class="accordion-button border-bottom font-weight-bold text-start" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="true" aria-controls="collapseThree">
                      Detail
                      <i class="collapse-close fa fa-plus text-xs pt-1 position-absolute end-0"></i>
                      <i class="collapse-open fa fa-minus text-xs pt-1 position-absolute end-0"></i>
                    </button>
                  </h6>
                  <div id="collapseThree" class="accordion-collapse collapse show" aria-labelledby="headingThree">
                    <div class="accordion-body text-sm opacity-8">
                      <ul>

                        <li>L2D Model Estimate Time : 14 days</li>
                        <li>Rigging Estimate Time : 14 days</li>
                      </ul>
                      
                    </div>
                  </div>
                </div>
                <div class="accordion-item mb-1">
                  <h6 class="accordion-header" id="headingTwo">
                    <button class="accordion-button border-bottom font-weight-bold text-start" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="true" aria-controls="collapseTwo">
                      Artist(s) Information
                      <i class="collapse-close fa fa-plus text-xs pt-1 position-absolute end-0"></i>
                      <i class="collapse-open fa fa-minus text-xs pt-1 position-absolute end-0"></i>
                    </button>
                  </h6>
                  <div id="collapseTwo" class="accordion-collapse collapse show" aria-labelledby="headingTwo" >
                    <div class="accordion-body text-sm opacity-8 text-left">
                      @foreach($product->artists as $artist)
                        <div class="text-center d-inline-block mx-3" style="">
                          <a href="{{url('/artist').'/'.$artist->id}}" class="avatar avatar-xl rounded-circle border border-info">
                            <img alt="Image placeholder" class="p-1" src="{{asset('storage/'.$artist->avatar)}}">
                          </a>
                          <p class="mb-0 text-md text-info bold">{{$artist->name}}</p>
                          {{-- <p class="mb-0 text-sm">Art & Rig</p> --}}
                        </div>
                      @endforeach 
                    </div>
                  </div>
                </div>
                
              </div>

              
              
              
             
              
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
    <!-- END Teams w/ 4 cards w/ image on left & descriptio/social link on right -->
    <!-- -------- END PRE-FOOTER 8 w/ TEXT, BG IMAGE AND 2 BUTTONS ------- -->
  </div>
  @include('frontend/footer')
  @include('frontend/js')
</body>
<script src="https://cdn.jsdelivr.net/npm/swiper@8/swiper-bundle.min.js"></script>


<script type="module">
  // import { getPrime, PathFinder, descartes } from "{{asset('frontend/js/plugins/sku.js')}}";


  const skus = {!! json_encode($skus) !!};
  let selected_sku = skus[0];
  // matchSku()
  if(skus.length > 0){
    $("#sku_price").html('$ ' + selected_sku['price'])
    let selected_atr = JSON.parse(selected_sku.sku_attribute)
    Object.keys(selected_atr).forEach(function(key) {
      $('.sku-select[attr-name="'+key+'"]').val(selected_atr[key]).change();
      // $('#sku-select-'+key).val(selected_atr[key]).change();
    });

    // skus.forEach(function(sku,i){
    //   let atr =JSON.parse(sku.sku_attribute);
    //   Object.keys(atr).forEach(function(key) {
    //     console.log(atr[key]);
    //     $('#sku-select-'+key+' option[value='+atr[key]+']').prop('disabled', false);
    //     // $('#sku-select-'+key+'option[value='+atr[key]+']').prop('disabled', false);
    //   });
    //   // console.log(atr_data[attribute])
    //   // console.log(atr[attribute]);
    //   // $('#sku-select-Size option[value="Large').attr('disabled','disabled');
    // })
  }
  else{
    $(".btn-add-cart").prop( "disabled", true );
    $(".btn-add-cart").text('Not Available')
  }
  

  
  var swiper = new Swiper(".bottom-swiper", {
    spaceBetween: 10,
    slidesPerView: 3,
    freeMode: true,
    watchSlidesProgress: true,
    navigation: {
      nextEl: ".swiper-button-next",
      prevEl: ".swiper-button-prev",
    },
  });

  var swiper2 = new Swiper(".top-swiper", {
    spaceBetween: 10,
    // navigation: {
    //   nextEl: ".swiper-button-next-1",
    //   prevEl: ".swiper-button-prev-1",
    // },
    thumbs: {
      swiper: swiper,
    },
  });


  $( ".btn-add-cart" ).click(function() {
      let product_id = "{{$product_id}}";
      let product_data = {
        "_token": "{{ csrf_token() }}",
        "id":product_id,
        "sku_id":selected_sku.id,
        // "sku_id":"",
        // "attribute":"",
      }
      
      $.ajax({
        method: "POST",
        url: "{{url('cart/add')}}",
        data: product_data,
        success: function(response) {
          if(response.code == 200 ){
            toastr.success(response.message, 'Success',{"positionClass": "toast-top-center",})
            $('#cart_count').html(Object.keys(response.cart).length);
          }
          else{
            toastr.error(response.message, 'Error',{"positionClass": "toast-top-center",})
          }

        }
      });
  });

  $(".sku-select").change(function() {
   matchSku()
    
  });


  function matchSku(){

    let selected_options = {};
    $('.sku-select').each(function(i, obj) {
      let attribute = $(this).attr("attr-name");
      let select = $(this)
      let select_option = $(this).val();
      selected_options[attribute] = select_option;
    });
    let selected_options_encode = JSON.stringify(selected_options)
    skus.forEach(function(sku, i){
      if(sku.sku_attribute == selected_options_encode){
        selected_sku = sku;
        $("#sku_price").html('$ ' + selected_sku['price'])
      }
    })
    // let select_atr_data = {}
    // $('.sku-select').each(function(i, obj) {
    //   let attribute = $(this).attr("attr-name");
    //   let select = $(this)
    //   let select_option = $(this).val();
    //   skus.forEach(function(sku,i){
    //     let atr =JSON.parse(sku.sku_attribute);
    //     if(atr_data[attribute] == atr[attribute]){
    //       console.log(atr[attribute]+"available!!");
    //     }
    //     else{
    //       console.log(atr[attribute]+"not available!!");
    //     }
    //     console.log(atr_data[attribute])
    //     console.log(atr[attribute]);
    //     $('#sku-select-Size option[value="Large').attr('disabled','disabled');
    //   })
    //   // console.log(atr_data[attribute])
      
    // })
    // console.log(atr_data);

  }

    // matchSku();

    

   
  
</script>

</html>
