
<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" itemscope itemtype="http://schema.org/WebPage">

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <title>
    Elda Solar's Vtuber Creator
  </title>
  @include('frontend/css')
</head>

<body class="coworking bg-gray-200">
  <!-- Navbar -->
  @include('frontend/topnav')
  <!-- -------- START HEADER 1 w/ text and image on right ------- -->
  <!-- -------- START HEADER 9 w/ floating img and bg  ------- -->
  <header class="position-relative">
    <div class="vtuber page-header min-vh-100 position-relative">
      <video class="w-100" playsinline="playsinline" autoplay="autoplay" muted="muted" loop="loop" loading="lazy">
        <source src="{{asset('storage/background-2.mp4')}}" type="video/mp4">
      </video>
      <span class="mask bg-gradient-dark opacity-3"></span>
      <div class="container">
        <div class="row justify-content-center">
          <div class="col-lg-6 text-center mx-auto mt-n7">
            <h4 class="text-white fadeIn1 fadeInBottom">Check Out Our Commissions</h4>
            <h1 class="text-white fadeIn2 fadeInBottom">Work with the topguns in the industry</h1>
            <p class="lead mb-5 fadeIn3 fadeInBottom text-white opacity-8">We’re constantly trying to help our client to express themselves and actualize your dream. Let us also assist you in elevating your Vtuber Game!</p>
            <a href="{{url('/request')}}" type="submit" class="btn bg-white me-2 fadeIn1 fadeInBottom">Get started</a>
          </div>
        </div>
      </div>
    </div>
  </header>
  <div class="card card-body blur shadow-blur mx-2 mx-lg-3 mx-md-4 mt-n6 mb-4">
    <!-- -------- START Features w/ title, device and stats -------- -->
  <section class="py-5">
    <div class="container">
      <div class="text-center mb-4">
        <h2>Illustration Commission</h2>
      </div>
    </div>
  </section>
  <section class="py-4">
    <div class="container">
      <div class="row justify-space-between py-2">
        <div class="col-lg-4 col-md-10 mx-auto first-col">
          <div class="card shadow-lg">
            <div class="card-header p-0 mx-3 mt-3 position-relative z-index-1">
              <a href="javascript:;" class="d-block">
                <img src="{{asset('storage/images/October2022/pV7CpYnukj6bmkBRyYO2.jpg')}}" class="img-fluid border-radius-lg">
              </a>
            </div>
  
            <div class="card-body pt-3">
              <span class="text-gradient text-info text-uppercase text-xs font-weight-bold my-2">TYPE</span>
              <a href="javascript:;" class="text-dark h5 d-block">
                Headshot
              </a>
              <p class="card-description mb-4">
                Use border utilities to quickly style the border and border-radius of an element. Great for images, buttons.
              </p>
              <div class="author align-items-center">
                <img src="{{asset('storage/artists/October2022/VZFkQnEuzsiF1SkXcshd.jpg')}}" alt="..." class="avatar shadow border-radius-lg">
                <div class="name ps-3">
                  <span>SiBuGe</span>
                  <div class="stats">
                    {{-- <small>Posted on 28 February</small> --}}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="card shadow-lg mt-3">
            <div class="card-header p-0 mx-3 mt-3 position-relative z-index-1">
              <a href="javascript:;" class="d-block">
                <img src="{{asset('storage/images/October2022/Q9rrSOld1BbpspiSxk4j.jpg')}}" class="img-fluid border-radius-lg">
              </a>
            </div>
  
            <div class="card-body pt-3">
              <span class="text-gradient text-info text-uppercase text-xs font-weight-bold my-2">TYPE</span>
              <a href="javascript:;" class="text-dark h5 d-block">
                Bust Up
              </a>
              <p class="card-description mb-4">
                Use border utilities to quickly style the border and border-radius of an element. Great for images, buttons.
              </p>
              <div class="author align-items-center">
                <img src="{{asset('storage/artists/October2022/VZFkQnEuzsiF1SkXcshd.jpg')}}" alt="..." class="avatar shadow border-radius-lg">
                <div class="name ps-3">
                  <span>SiBuGe</span>
                  <div class="stats">
                    {{-- <small>Posted on 28 February</small> --}}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-lg-4 col-md-10 mx-auto mt-0 mt-md-3 second-col">
          <div class="card shadow-lg">
            <div class="card-header p-0 mx-3 mt-3 position-relative z-index-1">
              <a href="javascript:;" class="d-block">
                <img src="{{asset('storage/images/October2022/v0bazocydtz4un7cqgnzn8dnwp123kcw.jpg')}}" class="img-fluid border-radius-lg">
              </a>
            </div>
  
            <div class="card-body pt-3">
              <span class="text-gradient text-info text-uppercase text-xs font-weight-bold my-2">TYPE</span>
              <a href="javascript:;" class="text-dark h5 d-block">
                Half Body
              </a>
              <p class="card-description mb-4">
                Use border utilities to quickly style the border and border-radius of an element. Great for images, buttons.
              </p>
              <div class="author align-items-center">
                <img src="{{asset('storage/artists/October2022/11QyMEJn9OTUGN2IUGwU.jpg')}}" alt="..." class="avatar shadow border-radius-lg">
                <div class="name ps-2">
                  <span>OPV</span>
                  <div class="stats">
                    {{-- <small>Posted on 28 February</small> --}}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="card shadow-lg mt-3">
            <div class="card-header p-0 mx-3 mt-3 position-relative z-index-1">
              <a href="javascript:;" class="d-block">
                <img src="{{asset('storage/images/October2022/PRhkStXp6P5ApF1KxQvy.jpg')}}" class="img-fluid border-radius-lg">
              </a>
            </div>
  
            <div class="card-body pt-3">
              <span class="text-gradient text-info text-uppercase text-xs font-weight-bold my-2">Type</span>
              <a href="javascript:;" class="text-dark h5 d-block">
                Full Body
              </a>
              <p class="card-description mb-4">
                Use border utilities to quickly style the border and border-radius of an element. Great for images, buttons.
              </p>
              <div class="author align-items-center">
                <img src="{{asset('storage/artists/October2022/11QyMEJn9OTUGN2IUGwU.jpg')}}" alt="..." class="avatar shadow border-radius-lg">
                <div class="name ps-2">
                  <span>OPV</span>
                  <div class="stats">
                    {{-- <small>Posted on 28 February</small> --}}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-lg-4 col-md-10 mx-auto mt-0 mt-md-3 third-col">
          <div class="card shadow-lg">
            <div class="card-header p-0 mx-3 mt-3 position-relative z-index-1">
              <a href="javascript:;" class="d-block">
                <img src="{{asset('storage/images/October2022/QPwA75Y11gsfDD5YNVi6.jpg')}}" class="img-fluid border-radius-lg">
              </a>
            </div>
  
            <div class="card-body pt-3">
              <span class="text-gradient text-info text-uppercase text-xs font-weight-bold my-2">Type</span>
              <a href="javascript:;" class="text-dark h5 d-block">
                Full Body & Complex BG
              </a>
              <p class="card-description mb-4">
                Use border utilities to quickly style the border
              </p>
              <div class="author align-items-center">
                <img src="{{asset('storage/artists/October2022/11QyMEJn9OTUGN2IUGwU.jpg')}}" alt="..." class="avatar shadow border-radius-lg">
                <div class="name ps-2">
                  <span>OPV</span>
                  <div class="stats">
                    {{-- <small>Posted on 28 February</small> --}}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="card shadow-lg mt-3">
            <div class="card-header p-0 mx-3 mt-3 position-relative z-index-1">
              <a href="javascript:;" class="d-block">
                <video class="w-100 border-radius-lg" playsinline="playsinline" autoplay="autoplay" muted="muted" loop="loop" loading="lazy">
                  <source src="{{asset('storage/background-5_x264.mp4')}}" type="video/mp4">
                </video>
              </a>
            </div>
  
            <div class="card-body pt-3">
              <span class="text-gradient text-info text-uppercase text-xs font-weight-bold my-2">Type</span>
              <a href="javascript:;" class="text-dark h5 d-block">
                Animated 
              </a>
              <p class="card-description mb-4">
                Use border utilities to quickly style the border
              </p>
              <div class="author align-items-center">
                <img src="{{asset('storage/artists/October2022/Uva0VxLmVyBPNqXmenGr.jpg')}}" alt="..." class="avatar shadow border-radius-lg">
                <div class="name ps-2">
                  <span>MingZhou</span>
                  <div class="stats">
                    {{-- <small>Posted on 28 February</small> --}}
                  </div>
                </div>
                <img src="{{asset('storage/artists/December2022/De5UiczxRnq5SaOwBxkx.webp')}}" alt="..." class="avatar shadow border-radius-lg ms-5">
                <div class="name ps-2">
                  <span>DWS</span>
                  <div class="stats">
                    {{-- <small>Posted on 28 February</small> --}}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="card shadow-lg mt-3">
            <div class="card-header p-0 mx-3 mt-3 position-relative z-index-1">
              <a href="javascript:;" class="d-block">
                <video class="w-100 border-radius-lg" playsinline="playsinline" autoplay="autoplay" muted="muted" loop="loop" loading="lazy">
                  <source src="{{asset('storage/background-6.mp4')}}" type="video/mp4">
                </video>
              </a>
            </div>
  
            <div class="card-body pt-3">
              <span class="text-gradient text-info text-uppercase text-xs font-weight-bold my-2">TYPE</span>
              <a href="javascript:;" class="text-dark h5 d-block">
                Long Animation
              </a>
              <p class="card-description mb-4">
                Use border utilities to quickly style the border and border-radius of an element. Great for images, buttons.
              </p>
              <div class="author align-items-center">
                <img src="{{asset('storage/artists/December2022/De5UiczxRnq5SaOwBxkx.webp')}}" alt="..." class="avatar shadow border-radius-lg">
                <div class="name ps-2">
                  <span>DWS</span>
                  <div class="stats">
                    {{-- <small>Posted on 28 February</small> --}}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
  {{-- <hr class="horizontal dark">
  <section class="mt-5">
    <div class="container py-5">
      <div class="row align-items-center">
        <div class="col-md-6 mb-md-0 mb-4">
          <h3>Very nice subtitle</h3>
          <p class="lead mb-md-5 mb-4">
            Change the color to match your brand or vision, add your logo, choose the perfect thumbnail, remove the playbar, add controls and more.
          </p>
          <p><span class="me-2">&#9679;</span> Showcase and embed your work with</p>
          <p><span class="me-2">&#9679;</span> Publish across social channels in a click</p>
          <p><span class="me-2">&#9679;</span> Sell your videos worldwide</p>
          <p><span class="me-2">&#9679;</span> Make more profit</p>
        </div>
        <div class="col-md-6">
          <div class="blur-shadow-image text-center">
            <img src="../../assets/img/examples/blog2.jpg" alt="img-shadow" class="img-fluid shadow-xl border-radius-lg max-height-500">
          </div>
        </div>
      </div>
    </div>
  </section> --}}
  <!-- -------- END Features w/ title, device and stats -------- -->
  </div>


  @include('frontend/footer')
  @include('frontend/js')
  <script>
    const urlParams = new URLSearchParams(window.location.search);
    let nav_tab = urlParams.get('tab');
    if(nav_tab == "design"){
      document.getElementById("v-pills-design-tab").click();
      console.log('design trigger');
    }
    else if(nav_tab == "model"){
      document.getElementById("v-pills-model-tab").click();
    }
    else if(nav_tab == "rig"){
      document.getElementById("v-pills-rig-tab").click();
    }
  </script>
</body>

</html>
