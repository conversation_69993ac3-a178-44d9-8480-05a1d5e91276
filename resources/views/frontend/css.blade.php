<!--     Fonts and icons     -->
<link rel="stylesheet" type="text/css" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700,900|Roboto+Slab:400,700" />
<!-- Nucleo Icons -->
<link href="{{asset('/frontend/css/nucleo-icons.css')}}" rel="stylesheet" />
<link href="{{asset('frontend/css/nucleo-svg.css')}}" rel="stylesheet" />
<!-- Font Awesome Icons -->
<script src="https://kit.fontawesome.com/efc75e7082.js" crossorigin="anonymous"></script>
<!-- Material Icons -->
<link href="https://fonts.googleapis.com/icon?family=Material+Icons+Round" rel="stylesheet">
<!-- CSS Files -->
<link id="pagestyle" href="{{asset('dashboard/css/material-dashboard.css?v=3.0.5')}}" rel="stylesheet" />
<link id="pagestyle" href="{{asset('frontend/fancybox/jquery.fancybox.css')}}" rel="stylesheet" />
<link href="{{asset('frontend/css/toggle-switch.css')}}" rel="stylesheet" />

<!-- toastr -->
<link href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" rel="stylesheet" />
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css" crossorigin="anonymous" referrerpolicy="no-referrer" />

<link rel="apple-touch-icon" sizes="48x48" href="{{asset('image/favicon.ico')}}">
<link rel="icon" type="image/png" href="{{asset('image/favicon-32x32.png')}}">

<script async src="https://www.googletagmanager.com/gtag/js?id=G-H77W0HKZ5P"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-H77W0HKZ5P');
</script>


<style>
  .toast-top-center {
    top: 24px;
  }
  .gallery-item {
    width: 100%;
    overflow: hidden;
  }
  .img-responsive{
    border-radius: 10px;
    width: 100%;
    -webkit-box-shadow: 5px 5px 25px -5px rgba(166,166,166,0.8);
    -moz-box-shadow: 5px 5px 25px -5px rgba(166,166,166,0.8);
    box-shadow: 5px 5px 25px -5px rgba(166,166,166,0.8);
    margin-top:15px;
  }

  .shadow-8{
    -webkit-box-shadow: 5px 5px 25px -5px rgba(166,166,166,0.8);
    -moz-box-shadow: 5px 5px 25px -5px rgba(166,166,166,0.8);
    box-shadow: 5px 5px 25px -5px rgba(166,166,166,0.8);
  }

  .square{
    display: inline-block;
    position: relative;
    /* width:24%;
    margin: 0.1rem; */
  }
  .square::after {
    content: "";
    display: block;
    padding-bottom: 100%;
  }
  .square-content{
    position:absolute;
    width: 97%;
    height: 97%;
    object-fit: cover;
    object-position: top;
    /* border-radius: 10px; */
  }
  .w-24{
    width: 24% !important;
  }

  /* hr{
    border: 0;
    height: 3px;
    width: 20%;
    position: relative;
    margin: 30px auto;
    background: #a43f6f
  } */
  .nav-link{
    cursor: pointer;
    /* transform:translate3d(0px, 0px, 0px); */
    transition :.5s ease;
  }
  .nav-link:hover{
    color: #1A73E8 !important;
  }
  .nav.nav-pills-css .nav-link.active{
    background:white;
    -webkit-box-shadow: 5px 5px 5px -5px rgba(166,166,166,0.5);
    -moz-box-shadow: 5px 5px 5px -5px rgba(166,166,166,0.5);
    box-shadow: 5px 5px 5px -5px rgba(166,166,166,0.5);
  }
  .hidden{
    visibility: hidden;
  }
  .bg-dark-blur{
    background: rgba( 0, 0, 0, 0.1 );
    backdrop-filter: blur( 2.5px );
    -webkit-backdrop-filter: blur( 2.5px );
    border: 1px solid rgba( 0, 0, 0, 0.18 );
  }
  .bg-dark-op5{
    background: rgba( 0, 0, 0, 0.5 );
  }
  /* .bg-dark-blur-xl{
    background: rgba( 0, 0, 0, 0.7 );
    box-shadow: 0 8px 32px 0 rgba( 31, 38, 135, 0.37 );
    backdrop-filter: blur( 15px );
    -webkit-backdrop-filter: blur( 15px );
    border: 1px solid rgba( 255, 255, 255, 0.18 );
    transition-duration: 1s;
  } */
  .d-none-ni{
    display: none;
  }
  .bg-position-top{
    background-position: top!important;
  }

  .footer .footer-logo{
    max-width: 6rem !important;
  }
  .phone-video{
    height: 96%;
    left: 3%;
    top: 2%;
    width: 68%;
    border-radius: 2.2rem;
  }
  .absolute-center{
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    margin:0 auto;
    transition: ease-out 1s;
  }
  .left-200{
    left:200%;
  }
  .chat-unread-number{
    display: flex;
    justify-content:center;
    align-items:center;
    width: 20px;
    height: 20px;
    font-size:0.75rem;
  }
  .unread-dot{
    position: absolute;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    top: 9px;
    left: 19px;
    font-size: 1px;
  }
  .product-title{
    font-size: 1rem;
    line-height: 1.2;
    height: 2.4rem;
    font-weight: 600;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .product-image-pv{
    aspect-ratio: 16 / 9;
    object-fit: cover;
    object-position: top;
  }
  .aspect-ratio-16-9{
    aspect-ratio: 16 / 9;
  }
  .aspect-ratio-9-16{
    aspect-ratio: 9 / 16;
  }
  .aspect-ratio-1{
    aspect-ratio: 1/1;
    justify-content: center;
    align-items: center;
    object-fit: cover;
    object-position: top;
    overflow: hidden;
  }
  .object-fit-contain{
    object-fit: contain;
  }
  .object-fit-cover{
    object-fit: cover;
    object-position: top 0 left 0;
  }


  
  
  /* .request-card .inner-border{
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
    left: 15px;
    right: 15px;
    top: 15px;
    bottom: 15px;
    margin: 0 auto;
    transition: ease-out 1s;
  } */
  .out-border{
    padding: 0.5rem;
    transition: all 0.8s ease-in;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid rgba(107,114,128,0.8);
  }
  .request-card{
    transition: all 0.8s ease-in;
  }

  .request-layer{
    position: absolute;
    display: flex;
    align-items: center;
    width: 100%;
    height: 100%;
    background: rgba( 0, 0, 0, 0.8 );
    backdrop-filter: blur( 1px );
    -webkit-backdrop-filter: blur( 1px );
    border: 1px solid rgba( 255, 255, 255, 0.18 );
    opacity: 1;
    transition: 0.8s ease-in;
  }
  
  .request-title{
    transition-delay:0.5s;
    transition: 1.2s ease-in-out 0.2s
   
  }
  .request-txt{
    line-height: 1.2rem;
    height: 3.6rem;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    transition: 1.2s ease-in-out 0.2s
    
  }
  
  .width{
    position:absolute;
    height:calc(100% + 10px);
    width:calc(100% - 60px); 
    background: white;
    margin:0 auto;
    transition: all 0.8s ease-in-out 
  }
  .height{
    position:absolute;
    height:calc(100% - 60px);
    width:calc(100% + 10px); 
    background: white;
    margin:0 auto;
    transition: all 0.8s ease-in;
  }
  .out-border:hover{
    transform: scale(1.05);
    box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.14), 0 7px 10px -5px rgba(64, 64, 64, 0.4);
    /* z-index: 9999; */
    /* padding: 0.25rem; */
    /* padding: 0!important; */
  }
  .out-border:hover .width{
    width: 0;
  }

  .out-border:hover .height{
    height: 0;
  }
  .out-border:hover .request-layer{
    opacity: 0.2;
  }
  
  .out-border:hover .request-title{
    transform: translateX(-50%)
  }
  .out-border:hover .request-txt{
    transform: translateY(10rem)
  }


  /* .request-card{
    border: 1px solid #333;
    font-family: 'Cinzel', serif;
    font-size: 20px;
    cursor: pointer;
    -webkit-tap-highlight-color: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 0;
    transition: 1s;
  }

  .request-card::before, .request-card::after {
  position: absolute;
  background: #eee;
  z-index: -1;
  transition: 1s;
  content: '';
}

  .request-card::before {
    height: 50px;
    width: 130px;
  }

  .request-card::after {
    width: 150px;
    height: 30px;
  }

  

  .request-card:hover::before {
    width: 0px;
    background: #fff;
  }

  .request-card:hover::after {
    height: 0px;
    background: #fff;
  }

  .request-card:hover {
    background: #fff;
  } */
  
  /* .center-text{
    width: 90%;
    height: 50%;
    border:1px solid white;
  } */

  /* .white-btn{
    border: 0px;
    background-color: white;
  }
  .white-btn i{
    position: absolute;
    left: 0px;
  }
  .white-btn span{
    color: #3b3b41;
    margin-left: 12px;
  }

  .dark-btn{
    border: 0px;
    background-color: transparent;
  }
  .dark-btn i{
    position: absolute;
    left: 0;
  }
  .dark-btn span{
    color: white;
    margin-left: 12px;
  } */

  /* .dropzone{
    min-height: 220px;
  } */
  .dropdown.nav-item .dropdown-menu-animation.show {
      height: auto !important;
      opacity: 1;
  }

  /* small desktop */
  @media only screen and (max-width: 1500px) {
    .page-header video {
      position: absolute;
      top: 50%;
      left: 50%;
      min-width: 140%;
      min-height: 100%;
      width: auto;
      height: auto;
      z-index: 0;
      transform: translateX(-50%) translateY(-50%);
    }
    .vtuber.min-vh-100 {
      min-height: 90vh !important;
    }
    object{
      width: 540px;
      height:900px;
    }
  }

  @media only screen and (max-width: 1400px) {
    object{
      width: 500px;
      height:900px;
    }
  }

  @media only screen and (max-width: 1300px) {
    object{
      width: 450px;
      height:900px;
    }

    .page-header video {
      min-width: 155%;
      min-height: 100%;
    }
  }

  /* Pad */
  @media only screen and (max-width: 1000px) {
    .page-header video {
      min-width: 230%;
      min-height: 100%;
    }
    .vtuber.min-vh-100 {
      min-height: 90vh !important;
    }
    /* .icon.icon-shape{
      display: none;
    } */
    .design-img{
      width: 100% !important;
    }
    .design-img-b{
      margin-bottom: 45vh;
    }
  }

    @media only screen and (max-width: 800px) {
    .artist-avatar a img{
      float: left;
      height: 60px;
      width: 60px !important;
    }
    .artist-avatar a div{
      font-size: 0.5rem;
    }

    .page-header video {
      min-width: 255%;
      min-height: 100%;
    }
  }

    /* Mobile Phone 401-600 */
    @media only screen and (max-width: 600px) {
    .page-header video {
      min-width: 300%;
      min-height: 100%;
    }
    .vtuber.min-vh-100 {
      min-height: 80vh !important;
    }
    /* .icon.icon-shape{
      display: none;
    } */
    .design-img{
      width: 100% !important;
    }
    .design-img-b{
      margin-bottom: 75%;
    }
    object{
      width: 400px;
      height:800px;
    }
  }

  @media only screen and (max-width: 500px) {
    object{
      width: 320px;
      height:650px;
    }
    .vtuber.min-vh-100 {
      min-height: 79vh !important;
    }
  }

  /* Mobile Phone Tiny <400 */
    @media only screen and (max-width: 400px) {
    .page-header video {
      min-width: 385%;
      min-height: 100%;
    }
    .vtuber.min-vh-100 {
      min-height: 90vh !important;
    }
    .icon.icon-shape{
      /* display: none; */
    }
    object{
      width: 250px;
      height:500px;
    }
  }
</style>

<style>
  .lds-spinner {
    color: official;
    display: inline-block;
    position: relative;
    width: 80px;
    height: 80px;
  }
  .lds-spinner div {
    transform-origin: 40px 40px;
    animation: lds-spinner 1.2s linear infinite;
  }
  .lds-spinner div:after {
    content: " ";
    display: block;
    position: absolute;
    top: 3px;
    left: 37px;
    width: 6px;
    height: 18px;
    border-radius: 20%;
    background: #fff;
  }
  .lds-spinner div:nth-child(1) {
    transform: rotate(0deg);
    animation-delay: -1.1s;
  }
  .lds-spinner div:nth-child(2) {
    transform: rotate(30deg);
    animation-delay: -1s;
  }
  .lds-spinner div:nth-child(3) {
    transform: rotate(60deg);
    animation-delay: -0.9s;
  }
  .lds-spinner div:nth-child(4) {
    transform: rotate(90deg);
    animation-delay: -0.8s;
  }
  .lds-spinner div:nth-child(5) {
    transform: rotate(120deg);
    animation-delay: -0.7s;
  }
  .lds-spinner div:nth-child(6) {
    transform: rotate(150deg);
    animation-delay: -0.6s;
  }
  .lds-spinner div:nth-child(7) {
    transform: rotate(180deg);
    animation-delay: -0.5s;
  }
  .lds-spinner div:nth-child(8) {
    transform: rotate(210deg);
    animation-delay: -0.4s;
  }
  .lds-spinner div:nth-child(9) {
    transform: rotate(240deg);
    animation-delay: -0.3s;
  }
  .lds-spinner div:nth-child(10) {
    transform: rotate(270deg);
    animation-delay: -0.2s;
  }
  .lds-spinner div:nth-child(11) {
    transform: rotate(300deg);
    animation-delay: -0.1s;
  }
  .lds-spinner div:nth-child(12) {
    transform: rotate(330deg);
    animation-delay: 0s;
  }
  @keyframes lds-spinner {
    0% {
      opacity: 1;
    }
    100% {
      opacity: 0;
    }
  }



  $bg: #FEF5DF;

body {
  background-color: $bg;
}

.container-1 {
  
  
  background: url("https://i.pinimg.com/564x/6f/5a/b1/6f5ab1b470beeeeaf285bb451c63ac8f.jpg");
  background-color: black;
  background-size: cover;
  cursor: pointer;
  
  -webkit-box-shadow: 0 0 5px #000;
        box-shadow: 0 0 5px #000;
}

.overlay {
  width: 100%;
  height: 100%;
  
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: 1fr 2fr 2fr 1fr;
  
  background: rgba(77, 77, 77, .9);
  color: $bg;
  opacity: 0;
  transition: all 0.5s;
  
  font-family: 'Playfair Display', serif;
}


.items {
  padding-left: 20px;
  letter-spacing: 3px;
}

.head {
  font-size: 30px;
  line-height: 40px;
  
  transform: translateY(40px);
  transition: all 0.7s;
  hr {
    display: block;
    width: 0;
    
    border: none;
    border-bottom: solid 2px $bg;
    
    position: absolute;
    bottom: 0; left:20px;
    
    transition: all .5s;
  }
}

.price {
  font-size: 22px;
  line-height: 10px;
  font-weight: bold;  
  opacity: 0;
  transform: translateY(40px);
  transition: all 0.7s;
  .old {
    text-decoration: line-through;
    color: lighten(rgb(77, 77, 77),40%);
  }
}

.cart {
  font-size: 12px;
  opacity: 0;
  letter-spacing: 1px;
  font-family: 'Lato', sans-serif;
  transform: translateY(40px);
  transition: all 0.7s;
  i {
    font-size: 16px;
  }
  span {
    margin-left: 10px;
  }
}

.container-1:hover .overlay {
  opacity: 1;
  & .head {
    transform: translateY(0px);
  }
  
  & hr {
    width: 75px;
    transition-delay: 0.4s;
  }
  
  & .price {
    transform: translateY(0px);
    transition-delay: 0.3s;
    opacity: 1;
  }
  
  & .cart {
    transform: translateY(0px);
    transition-delay: 0.6s;
    opacity: 1;
  }
}


</style>

