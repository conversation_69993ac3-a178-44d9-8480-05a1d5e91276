
<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" itemscope itemtype="http://schema.org/WebPage">

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <title>
    PIPIPEN - Artist detail
  </title>
  @include('content/css')
</head>

<body class="coworking bg-gray-200">
  <!-- Navbar -->
  @include('content/topnav')
  <!-- -------- START HEADER 1 w/ text and image on right ------- -->

  <header>
    <div class="page-header min-height-500" style="background-image: url('');" loading="lazy">
      <span class="mask bg-gradient-dark opacity-2"></span>
    </div>
  </header>
  <!-- -------- END HEADER 1 w/ text and image on right ------- -->
  <div class="card card-body blur shadow-blur mx-2 mx-lg-3 mt-n6 mb-4">
    <section class="py-sm-5 py-3 position-relative">
      <div class="container">
        <div class="row">
          <div class="col-12 mx-auto">
            <div class="mt-n7 mt-md-n8 text-center">
              <img class="avatar avatar-xxl shadow-xl position-relative z-index-2 blur" src="{{$artist->avatar}}" alt="artist avatar" loading="lazy">
            </div>
            <div class="row py-5">
              <div class="col-lg-7 col-md-7 z-index-2 position-relative px-md-2 px-sm-5 mx-auto">
                <div class="d-flex justify-content-between align-items-center mb-2">
                  <h3 class="mb-0">{{$artist->name}}</h3>
                  <div class="d-block">
                    {{-- <div type="button" class="btn btn-sm btn-outline-info text-nowrap mb-0">Add To Commission</div> --}}
                  </div>
                </div>
                <div class="row">
                  @if($artist->link_twitter)
                  <div class="col-auto">
                    <a href="{{$artist->link_twitter}}" class="h6 text-info" target="_blank">
                      <i class="fa-brands fa-twitter"></i>
                    </a>
                  </div>
                  @endif
                  @if($artist->link_pixiv)
                  <div class="col-auto">
                    <a href="{{$artist->link_pixiv}}" class="h6 text-info" target="_blank">
                      <i class="fa-solid fa-p"></i>
                    </a>
                  </div>
                  @endif
                  @if($artist->link_weibo)
                  <div class="col-auto">
                    <a href="{{$artist->link_weibo}}" class="h6 text-info" target="_blank">
                      <i class="fa-brands fa-weibo"></i>
                    </a>
                  </div>
                  @endif
                  @if($artist->link_bilibili)
                  <div class="col-auto">
                    <a href="{{$artist->link_bilibili}}" class="h6 text-info" target="_blank">
                      <i class="fa-brands fa-bilibili"></i>
                    </a>
                  </div>
                  @endif
                  @if($artist->link_mihuashi)
                  <div class="col-auto">
                    <a href="#" class="h6 text-info">
                      <i class="fa-brands fa-octopus-deploy"></i>
                    </a>
                  </div>
                  @endif
                </div>
                <div class="row my-3">
                  <div class="col-12">
                    @foreach($tags as $tag)
                    <span class="badge bg-gradient-info">{{$tag->name_en}}</span>
                    @endforeach
                  </div>
                </div>
                <p class="text-lg mb-0">
                  {!!$artist->intro!!}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section class="position-relative">
      <div class="container">
        <div class="row">
          <div class="col-7">
            <div class="nav-wrapper border border-radius-xl">
              <ul class="nav nav-pills nav-pills-css nav-fill p-1" role="tablist">
                <li class="nav-item">
                  <div class="nav-link active" id="pills-works" data-bs-toggle="pill" href="#works" role="tab" aria-controls="works" aria-selected="true">Portfolio (15)</div>
                </li>
                <li class="nav-item">
                  <div class="nav-link" id="pills-commissions" data-bs-toggle="pill" href="#commissions" role="tab" aria-controls="commissions" aria-selected="false">Commissions (3)</div>
                </li>
                <li class="nav-item">
                  <div class="nav-link" id="pills-commissions" data-bs-toggle="pill" href="#commissions" role="tab" aria-controls="reviews" aria-selected="false">Reviews (275)</div>
                </li>
               
              </ul>
            </div>
          </div>
        </div>
        <div class="mt-5 mb-5">
          <div class="tab-content">
            <div class="tab-pane fade show active" id="works" role="tabpanel" aria-labelledby="pills-works">
              <div class="row">
                @foreach($artist->works as $key=>$image)
                <div class="col-4 col-lg-3">
                  <div class="shadow border-radius-lg mt-4 cursor-pointer work-preview border" image-key="{{$key}}" work-id="{{$image->id}}">
                    @if($image->url_sm)
                      <img class="shadow border-radius-lg w-100 aspect-ratio-1" src="{{$image->url_sm}}" />
                    @else
                      <img class="shadow border-radius-lg w-100 aspect-ratio-1" src="{{$image->url_md}}" />
                    @endif
                  </div>
                </div>
                @endforeach
              </div>
            </div>
            <div class="tab-pane fade" id="commissions" role="tabpanel" aria-labelledby="pills-commissions">
              <div class="row">
                @foreach($services as $service)
                <div class="col-4">
                  <a href="{{url("commission")."/".$service->id}}" target="_blank" class="card shadow cursor-pointer service-preview">
                    <div class="card-header p-0 mx-3 mt-3 position-relative z-index-1">
                      <div  class="d-block">
                        <img src="{{$service->firstshowcase->url_sm}}" class="img-fluid border-radius-lg aspect-ratio-16-9 border">
                      </div>
                    </div>
          
                    <div class="card-body pt-3">
                      <span class="text-gradient text-success text-uppercase text-xs font-weight-bold my-2">OPEN</span>
                      <div class="text-dark h4 d-block">
                       {{$service->enContent->title}}
                      </div>
                      <div class="mb-3 opacity-7">
                      
                       <span class="text-info">${{$service->price}}</span>
                       <span class="text-info text-xs">+</span> 
                      </div>
                    </div>
                  </div>
                </a>
                
                @endforeach
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- END Teams w/ 4 cards w/ image on left & descriptio/social link on right -->
    <!-- -------- END PRE-FOOTER 8 w/ TEXT, BG IMAGE AND 2 BUTTONS ------- -->
  </div>
  <div class="modal fade modal-xl" id="work-detail" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="work-title"></h5>
          <button type="button" class="btn-close text-dark" data-bs-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body text-center show">
          <div class="row">
            <div class="col-7">
              <div class="border p-2 border-radius-lg">
                <img class="w-100 border-radius-lg" src="" id="work-img">
              </div>
            </div>
            
          </div>
          
        </div>
        <div class="modal-footer">
          {{-- <button type="button" class="btn bg-gradient-dark btn-sm" data-bs-dismiss="modal">取消</button> --}}
          <button type="button" class="btn bg-gradient-info btn-sm" id="confirm">report</button>
        </div>
      </div>
    </div>
  </div>
  <div class="modal fade modal-xl" id="comm-detail" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="work-title"></h5>
          <button type="button" class="btn-close text-dark" data-bs-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body text-center show">
          <div class="row">
            <div class="col-7">
              
            </div>
            <div class="col-5">

            </div>
          </div>
        </div>
        <div class="modal-footer">
          {{-- <button type="button" class="btn bg-gradient-dark btn-sm" data-bs-dismiss="modal">取消</button> --}}
          <button type="button" class="btn bg-gradient-info btn-sm" id="confirm">report</button>
        </div>
      </div>
    </div>
  </div>
  @include('content/footer')
  @include('content/js')
</body>

<script>
  const images = {!! json_encode($artist->works) !!}
  const services = {!!json_encode($artist->services)!!}

  $( ".work-preview" ).click(function() {
     let key = $(this).attr('image-key');
     $("#work-img").attr('src',"{{asset('storage')}}/" + images[key]['url_md']);
     $('#work-detail').modal('show')
  })

  // $( ".work-preview" ).click(function() {
  //   let data = {
  //     'id': $(this).attr('work-id')
  //   }
   
  //   $.ajax({
  //     method: "GET",
  //     url: "{{url('content/worklio_detail')}}",
  //     data: data,
  //     success: function(response) {
  //       if(response.code == 200 ){
  //         $("#work-img").attr('src',"{{asset('storage')}}/" + response.data.url_md);
  //         $('#work-detail').modal('show')
  //       }
  //       else{
          
  //       }
  //     }
  //   });    
  // })


  
</script>

</html>
