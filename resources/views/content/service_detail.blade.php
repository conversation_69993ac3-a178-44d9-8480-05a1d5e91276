
<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" itemscope itemtype="http://schema.org/WebPage">

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <title>
    {{$service->enContent()->first()->title}}
  </title>
  @include('content/css')
</head>

<body class="coworking bg-gray-200">
  <!-- Navbar -->
  @include('content/topnav')
  <!-- -------- START HEADER 1 w/ text and image on right ------- -->

  <header>
    <div class="page-header min-height-500" style="background-image: url('{{$artist->cover ? Voyager::image($artist->cover) : asset('/image/raingirl_1_lg.jpg')}}');" loading="lazy">
      <span class="mask bg-gradient-dark opacity-2"></span>
    </div>
  </header>
  <!-- -------- END HEADER 1 w/ text and image on right ------- -->
  <div class="card card-body blur shadow-blur mx-2 mx-lg-3 mt-n6 mb-4">
    <div class="section my-lg-5">
      <div class="container">
        <div class="row flex-row">
          <div class="col-lg-7">
            <div class="w-100" data-fancybox="product-" href="{{$service->firstShowcase->url_lg}}">
              <img class="card w-100" src="{{$service->firstShowcase->url_md}}" alt="ladydady" loading="lazy">
            </div>
            <div class="nav-wrapper border border-radius-xl mt-5 position-sticky top-10 z-index-3">
              <ul class="nav nav-pills nav-pills-css nav-fill p-1" role="tablist">
                <li class="nav-item">
                  <div class="nav-link active" id="pills-about" data-bs-toggle="pill" href="#about" role="tab" aria-controls="about" aria-selected="true">About</div>
                </li>
                <li class="nav-item">
                  <div class="nav-link" id="pills-showcases" data-bs-toggle="pill" href="#showcases" role="tab" aria-controls="showcases" aria-selected="false">Showcases</div>
                </li>
                <li class="nav-item">
                  <div class="nav-link" id="v-pills-reviews" data-bs-toggle="pill" href="#reviews" role="tab" aria-controls="reviews" aria-selected="false">Reviews</div>
                </li>
              </ul>
            </div>
            <div class="tab-content">
              <div class="tab-pane fade show active" id="about" role="tabpanel" aria-labelledby="pills-works">
                <div class="accordion mt-5" id="accordionProduct">
                  <div class="accordion-item mb-1">
                    <h6 class="accordion-header" id="headingOne">
                      <button class="accordion-button border-bottom font-weight-bold text-start" type="button" data-bs-toggle="collapse" data-bs-target="#detail" aria-expanded="true" aria-controls="detail">
                        Detail
                        <i class="collapse-close fa fa-plus text-xs pt-1 position-absolute end-0"></i>
                        <i class="collapse-open fa fa-minus text-xs pt-1 position-absolute end-0"></i>
                      </button>
                    </h6>
                    <div id="detail" class="accordion-collapse collapse show" aria-labelledby="headingOne" >                 
                      <div class="accordion-body text-md opacity-8">
                        <div class="white-space-pre-wrap">{!!$service->enContent()->first()->detail!!}</div>
                        
                      </div>
                    </div>
                  </div>
                  <div class="accordion-item mb-1">
                    <h6 class="accordion-header" id="headingOne">
                      <button class="accordion-button border-bottom font-weight-bold text-start" type="button" data-bs-toggle="collapse" data-bs-target="#delivery" aria-expanded="true" aria-controls="delivery">
                        Delivery Time
                        <i class="collapse-close fa fa-plus text-xs pt-1 position-absolute end-0"></i>
                        <i class="collapse-open fa fa-minus text-xs pt-1 position-absolute end-0"></i>
                      </button>
                    </h6>
                    <div id="delivery" class="accordion-collapse collapse show" aria-labelledby="headingOne" >                 
                      <div class="accordion-body text-md opacity-8">
                        <div class="">
                          <span class="badge badge-info">{{$service->days_need}} Day(s)</span> 
                        </div>
                        
                      </div>
                    </div>
                  </div>
                  <div class="accordion-item mb-1">
                    <h6 class="accordion-header" id="headingOne">
                      <button class="accordion-button border-bottom font-weight-bold text-start" type="button" data-bs-toggle="collapse" data-bs-target="#prefer" aria-expanded="true" aria-controls="prefer">
                        Prefer & Can't Do
                        <i class="collapse-close fa fa-plus text-xs pt-1 position-absolute end-0"></i>
                        <i class="collapse-open fa fa-minus text-xs pt-1 position-absolute end-0"></i>
                      </button>
                    </h6>
                    <div id="prefer" class="accordion-collapse collapse show" aria-labelledby="headingOne" >                 
                      <div class="accordion-body text-md opacity-8">
                        <div class="prefer">
                          <div class="text-md text-success">Prefer</div>
                          <div>
                            @foreach($prefer_tags as $prefer_tag)
                              <span class="badge badge-success">{{$prefer_tag->name_en}}</span>
                            @endforeach
                          </div>
                          <div class="text-md mt-3 text-danger">Can't Do</div>
                          <div>
                            @foreach($cantdo_tags as $cantdo_tag)
                              <span class="badge badge-danger">{{$cantdo_tag->name_en}}</span>
                            @endforeach
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div class="accordion-item mb-1">
                    <h6 class="accordion-header" id="headingOne">
                      <button class="accordion-button border-bottom font-weight-bold text-start" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                        Creation & Payment stages
                        <i class="collapse-close fa fa-plus text-xs pt-1 position-absolute end-0"></i>
                        <i class="collapse-open fa fa-minus text-xs pt-1 position-absolute end-0"></i>
                      </button>
                    </h6>
                    <div id="collapseOne" class="accordion-collapse collapse show" aria-labelledby="headingOne" >
                      <div class="accordion-body text-sm opacity-8">
                        
                          @foreach($service_stages as $stage)
                            <div class="d-flex align-items-center mb-2">
                              <div class="text-info font-weight-bold mb-0 w-10 border border-info border-radius-lg text-center py-1">
                                {{$stage->percent}}%
                              </div>
                              <div class="mx-2"> - </div>
                              <div class="text-info">{{$stage->name_en}}</div>
                            </div>
                          @endforeach
                        
                        {{-- @foreach($service_stages as $stage)
                          <div>{{$stage->name_en}} - {{$stage->percent}}%</div>
                        @endforeach --}}
                      </div>
                    </div>
                  </div>
                  <div class="accordion-item mb-1">
                    <h6 class="accordion-header" id="headingThree">
                      <button class="accordion-button border-bottom font-weight-bold text-start" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="true" aria-controls="collapseThree">
                        Usage & Commercial rights
                        <i class="collapse-close fa fa-plus text-xs pt-1 position-absolute end-0"></i>
                        <i class="collapse-open fa fa-minus text-xs pt-1 position-absolute end-0"></i>
                      </button>
                    </h6>
                    <div id="collapseThree" class="accordion-collapse collapse show" aria-labelledby="headingThree">
                      <div class="accordion-body text-sm opacity-8">
                        
                      </div>
                    </div>
                  </div>
                  
                  
                </div>
              </div>
              <div class="tab-pane fade" id="showcases" role="tabpanel" aria-labelledby="pills-serviceissions">
                @foreach($service->showcases()->get() as $showcase)
                  <div class="mt-3 mx-auto" data-fancybox="product-" href="{{$showcase->url_lg}}">
                    <img class="card w-100" src="{{$showcase->url_md}}" alt="ladydady" loading="lazy">
                  </div>
                @endforeach
              </div>
            </div>
          </div>
          <div class="col-lg-5">
            <div class="ms-lg-5 position-sticky top-10">
              <h3 class="border-bottom pb-3 mb-0">{{$service->enContent()->first()->title}}</h3>
              <div class="border-bottom py-3">
                <div class="d-flex" style="">
                  <a href="{{url('/artist').'/'.$artist->id}}" class="avatar avatar-xxl rounded-circle border">
                    <img alt="Image placeholder" class="p-1" src="{{$artist->avatar}}">
                  </a>
                  <div class="d-flex align-items-center ms-4">
                    <div class="justify-content-center">
                      <div class="">
                        <span class="text-xl text-info">{{$artist->name}}</span>
                        <span class="text-sm ms-2">
                          <i class="fa-solid fa-award me-1 text-danger opacity-8"></i>
                          <i class="fa-solid fa-medal me-1 text-warning opacity-8"></i>
                        </span>
                       
                      </div>
                      <div class="opacity-8 d-flex text-md">
                        <span class="text-success me-2"><i class="fa-solid fa-star me-1"></i>5.0</span>
                        <span class>(253 reviews)</span>
                      </div>
                    </div>
                  </div>
                  {{-- <p class="mb-0 text-sm">Art & Rig</p> --}}
                </div>
              </div>
              <div class="border-bottom py-3">
                <div class="d-flex justify-content-between mt-2">
                  <h6 class="opacity-8">STATUS</h6>
                  <h6 class="category text-success">OPEN</h6>
                </div>
                <div class="d-flex justify-content-between mt-2">
                  <h6 class="opacity-8">PRICE</h6>
                  <h6 class="category"><span class="text-xs opacity-8">From</span> ${{$service->price}}</h6>
                </div>
              </div>
              <div class="text-end mt-3">
                {{-- <button class="btn btn-outline-info mb-0 mt-lg-auto btn-add-cart" type="button" name="button">SAVE</button> --}}
                <button class="btn bg-gradient-info mb-0 mt-lg-auto" type="button" name="button" id="request-btn">REQUEST</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    

    
    <!-- END Teams w/ 4 cards w/ image on left & descriptio/social link on right -->
    <!-- -------- END PRE-FOOTER 8 w/ TEXT, BG IMAGE AND 2 BUTTONS ------- -->
  </div>
  <div class="modal fade modal-xl" id="request-modal" tabindex="-2" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="modal-title">{{$service->enContent()->first()->title}}</h5>
          <button type="button" class="btn-close text-dark" data-bs-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
         
          <div class="row request-body">
            <div class="col-6 border-end">
              <div class="px-3" data-fancybox="product-" href="{{$service->firstShowcase->url_lg}}">
                <img class="card border w-100" src="{{$service->firstShowcase->url_md}}" alt="ladydady" loading="lazy">
              </div>
            </div>
            <div class="col-6 position-relative">
              <form id="request-form">
                <div class="px-3">
                  
                    <h5>Commission Request</h5>

                    <div class="row mt-3">
                      <div class="col-12">
                        <p class="mb-2">Deadline <span class="text-danger">*</span></p>
                        <input type="text" class="form-control flatpickr border border-radius-lg px-2" id="deadline">
                      </div>
                    </div>

                    {{-- <div class="row mt-3">
                      <div class="col-12">
                        <p class="mb-2">Budget</p>
                        <input type="text" class="form-control border border-radius-lg px-2" id="deadline">
                      </div>
                    </div> --}}


                    <div class="row mt-3">
                      <div class="col-12">
                        <p class="mb-2">Request Detail <span class="text-danger">*</span></p>
                        <textarea class="form-control border border-radius-lg px-2 resize-none" id="request-detail" autocomplete="off" rows="7" id="detail"></textarea>
                      </div>
                    </div>

                    

                    <div class="row mt-3">
                      <div class="col-12">
                        <p class="mb-0">Reference</p>
                        <div class="text-sm opacity-8 mb-2 text-info">Tips: The correct file names will help the artist understand you better.</div>
                        <div class="form-control border dropzone dz-clickable" id="cd_ref" file-type="design">
                          <div class="dz-default dz-message"><button class="dz-button" type="button">Upload Reference Files</button>
                          </div>
                        </div>
                      </div>
                    </div>              
                </div>
              </form>

              <div class="success-message position-absolute h-100 top-0 d-none-ni">
                <div class="d-flex align-items-center h-100">
                  <div class="text-center ">
                    <img src="{{asset('image/pia_emote/love.gif')}}" class="w-50">
                    <h2 class="text mt-3">Request Sent</h2>
                    <p class="lead">You can go to <a href="{{url('dashboard/commissions')}}">Dashboard</a> to view your submited request</p>
                  </div>
                </div>
              </div>
            </div>
            
          </div>
        </div>
        <div class="modal-footer">
          {{-- <button type="button" class="btn bg-gradient-dark btn-sm" data-bs-dismiss="modal">取消</button> --}}
          <button type="button" class="btn bg-gradient-info" id="send-request">Send Request</button>
        </div>
      </div>
    </div>
  </div>

  <div class="modal fade" id="rename-modal" tabindex="-1" role="dialog" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">>
    <div class="modal-dialog modal-dialog-centered" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <div>
            <h5 class="modal-title" id="">Rename File</h5>
            <p class="text-sm opacity-8">The correct file names will help the artist understand you better.</p>
            <div class="text-sm mt-1 opacity-8">The correct file names will help the artist understand you better.</div>
          </div>
          
          
          <button type="button" class="btn-close text-dark" data-bs-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body text-start">
          <div class="">
            <img src="" class="img-fluid border-radius-lg aspect-ratio-16-9 border" id="rename_preview">
          </div>
          <div class="mt-3">
            <div class="text-sm">Original File Name</div>
            <input class="form-control border border-radius-lg px-2"  id="file_old_name" disabled>
          </div>
          <div class="mt-3">
            <div class="text-sm">Rename to: </div>
            <input type="text" class="form-control border border-radius-lg px-2" id="file_new_name">
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn bg-gradient-info btn-sm" id="save-name">Save</button>
        </div>
      </div>
    </div>
  </div>
  @include('content/footer')
  @include('content/js')
  <script src="{{asset('dashboard/js/plugins/dropzone.min.js')}}"></script>
  <script src="{{asset('dashboard/js/plugins/flatpickr.min.js')}}"></script>
  <script>
    jQuery(function($){
      // $('#rename-modal').modal('show')
    });

    flatpickr(".flatpickr");

    

    function requestSuccess(){
      $("#request-form").addClass("hidden");
      $("#send-request").hide();
      $(".success-message").fadeIn(800);
    }
  
    function renameFiles(files){
      let i = 0;
    }
  
    $( "#request-btn" ).click(function() {
      //  let key = $(this).attr('image-key');
      //  $("#work-img").attr('src',"{{asset('storage')}}/" + images[key]['url_md']);
      $('#request-modal').modal('show')
    })
  
    Dropzone.autoDiscover = false;
    const dropzone = $('.dropzone').dropzone({
      url: "{{url('user/send_request')}}",
      acceptedFiles: 'image/*',
      autoProcessQueue:false,
      method: 'post',
      addRemoveLinks: true,
      uploadMultiple:true,
      parallelUploads: 100,
      dictRemoveFile: "REMOVE",
      headers: {
          'X-CSRF-TOKEN': "{{ csrf_token() }}"
      },
      init: function() {
        const dropzone = this;
        let files = [];
        var new_files;
        var new_files_names = [];
        var new_files_index = 0;
        var new_files_length = 1;
        let file_index = 0;


        this.on('sendingmultiple', function(files, xhr, formData){
          formData.append('service_id', "{{$service->id}}");
          formData.append('artist_id', "{{$service->artist_id}}");
          formData.append('detail', $("#request-detail").val());
          formData.append('deadline', $("#deadline").val());

        });
  
        this.on("addedfile", file => {
          // file.remove_index = file_index;
          // console.log(file_index)
          // file_index ++;
        });
  
        this.on("addedfiles", files => {
          // $('#rename-modal').modal('show')
          // current_file_index = 0;
          // new_files = files;
          // new_files_length = files.length;

          // let old_file_name = new_files[current_file_index].name.replace(/\.[^/.]+$/, "")

          // let file_src = URL.createObjectURL(new_files[current_file_index]);
          // $('#rename_preview').attr("src",file_src);
          // $('#file_old_name').val(old_file_name);
          // $('#file_new_name').val(old_file_name);

          // let queued_files = dropzone.getQueuedFiles();
          // // for(let i=0; i<queued_files.length; i ++){
          // //   queued_files[i].removed_index = i ; 
          // // }

          // console.log(queued_files);
        });
  
        this.on("removedfile", file => {
            // let removed_index = file.remove_index;
            // //reset index of current files

            // let queued_files = dropzone.getQueuedFiles();
            // for(let i=0; i<queued_files.length; i ++){
            //   queued_files[i].removed_index = i ; 
            // }
            
            // new_files_names.splice(removed_index, 1);
            // file_index = file_index - 1;
            // console.log(new_files_names);
        });
  
        this.on("success", function(file, response){
          // requestSuccess()
        });
  
        $('#send-request').click(function() {
          // if($("#request-detail").val() == ""){
          //   toastr.error("Please fill in all the required fileds", 'Fill in required fileds',{"positionClass": "toast-top-center",})
          //   return
          // }
  
  
          files =  dropzone.getQueuedFiles();
          console.log(files)
          // $('#send-request').attr("disabled",true)
          // $('#send-request').html("Sending...")
          if(files.length > 0){
            dropzone.processQueue();
          }
          else{
            let request_data = {
              "_token": "{{ csrf_token() }}",
              "artist_id":"{{$service->artist_id}}",
              "service_id":"{{$service->id}}",
              "detail":$("#request-detail").val(),
              "deadline":$("#deadline").val(),
            }
  
            $.ajax({
              method: "POST",
              url: "{{url('user/send_request')}}",
              data: request_data,
              success: function(response) {
                if(response.code == 200 ){
                  requestSuccess()
                  // toastr.success(response.message, 'Success',{"positionClass": "toast-top-center",});
                  console.log(response)
                  // location.reload();
                }
                else{
                  toastr.error(response.message, 'Error',{"positionClass": "toast-top-center",})
                  $('#send-request').attr("disabled",false)
                  $('#send-request').html("Send Request")
                }
              }
            });
          }
        })

        // $('#save-name').click(function(){

        //   if(current_file_index < new_files_length){
        //     let new_file_name = $('#file_new_name').val();
        //     new_files_names.push(new_file_name)
            
        //     current_file_index ++
        //     if(current_file_index < new_files_length){
        //       let old_file_name = new_files[current_file_index].name.replace(/\.[^/.]+$/, "")
        //       let file_src = URL.createObjectURL(new_files[current_file_index]);
        //       $('#rename_preview').attr("src",file_src);
        //       $('#file_old_name').val(old_file_name);
        //       $('#file_new_name').val(old_file_name);
        //     }
        //     else{
        //       $('#rename-modal').modal('hide')
        //     }
        //   }
        //   // console.log(new_files_names)
        // })
  
        // const startButton = document.querySelector("#cancel_upload")
        // const confirmButton = document.querySelector("#confirm_upload")
        // var i = 0;
  
  
        // $('#upload-modal').on('hidden.bs.modal', function () {
        //   let files = dorpzone.getQueuedFiles();
        //   dorpzone.removeFile(files[0])
        // })
  
        // $('.cancel_upload').on('click', function () {
          
        // })
  
        // cancel_upload.addEventListener("click", function() {
        //   let files = dorpzone.getQueuedFiles();
        //   dorpzone.removeFile(files[0])
        //   // dorpzone.processQueue()
          
        //   // const imageSrc = URL.createObjectURL(files[i]);
          
        //   // const imagePreviewElement = document.querySelector("#upload-preview");
          
        //   // imagePreviewElement.src = imageSrc;
  
        //   // $('#upload-modal').modal('show')
  
        // });
  
        // confirmButton.addEventListener("click", function() {
        //   dorpzone.processQueue()
        //   $('#upload-modal').modal('hide')
        //   // let files = dorpzone.getQueuedFiles();
        //   // let current_file = files[i];
        //   // dorpzone.processFile(current_file);
          
          
        // });
  
  
  
  
      }
    });
  </script>
</body>



</html>
