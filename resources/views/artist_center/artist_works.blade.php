<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <link rel="apple-touch-icon" sizes="76x76" href="../../../assets/img/apple-icon.png">
  <link rel="icon" type="image/png" href="../../../assets/img/favicon.png">
  <title>
    Pipipen Dashboard
  </title>
  <!--     Fonts and icons     -->
  @include('artist_center/css')
  <style>
    .aspect-ratio-1{
      aspect-ratio: 1/1;
      object-fit: cover;
      object-position: top;
      overflow: hidden;
    }
    .object-fit-contain{
      object-fit: contain;
    }
    .object-fit-cover{
      object-fit: cover;
      object-position: top 0 left 0;
    }
  </style>
</head>

<body class="g-sidenav-show bg-gray-200">
  @include('artist_center/sidebar')
  <main class="main-content position-relative h-100 border-radius-lg ">
    <!-- Navbar -->
    @include('artist_center/topnav')
    <div class="container-fluid py-5" id="app">
      <div class="row mt-5 mb-5">
        <div class="col-lg-3">
          <div class="card position-sticky top-9">
            <ul class="nav flex-column bg-white border-radius-lg p-3">
              <li class="nav-item">
                <a class="nav-link text-dark d-flex" data-scroll="" href="#works-card">
                  <i class="fa-solid fa-palette text-lg me-2"></i>
                  <span class="text-sm">作品合集</span>
                </a>
              </li>
              <li class="nav-item pt-2">
                <a class="nav-link text-dark d-flex" data-scroll="" href="#upload-work">
                  <i class="fa-solid fa-upload text-lg me-2"></i>
                  <span class="text-sm">上传图片</span>
                </a>
              </li>
              <li class="nav-item pt-2">
                <a class="nav-link text-dark d-flex" data-scroll="" href="#video-work">
                  <i class="fa-brands fa-youtube text-lg me-2"></i>
                  <span class="text-sm">视频链接</span>
                </a>
              </li>
              {{-- <li class="nav-item pt-2">
                <a class="nav-link text-dark d-flex" data-scroll="" href="">
                  <i class="material-icons text-lg me-2">receipt_long</i>
                  <span class="text-sm">Change Password</span>
                </a>
              </li> --}}
              {{-- <li class="nav-item pt-2">
                <a class="nav-link text-dark d-flex" data-scroll="" href="#password">
                  <i class="material-icons text-lg me-2">lock</i>
                  <span class="text-sm">Change Password</span>
                </a>
              </li>
              <li class="nav-item pt-2">
                <a class="nav-link text-dark d-flex" data-scroll="" href="#2fa">
                  <i class="material-icons text-lg me-2">security</i>
                  <span class="text-sm">2FA</span>
                </a>
              </li>
              <li class="nav-item pt-2">
                <a class="nav-link text-dark d-flex" data-scroll="" href="#accounts">
                  <i class="material-icons text-lg me-2">badge</i>
                  <span class="text-sm">Accounts</span>
                </a>
              </li>
              <li class="nav-item pt-2">
                <a class="nav-link text-dark d-flex" data-scroll="" href="#notifications">
                  <i class="material-icons text-lg me-2">campaign</i>
                  <span class="text-sm">Notifications</span>
                </a>
              </li>
              <li class="nav-item pt-2">
                <a class="nav-link text-dark d-flex" data-scroll="" href="#sessions">
                  <i class="material-icons text-lg me-2">settings_applications</i>
                  <span class="text-sm">Sessions</span>
                </a>
              </li>
              <li class="nav-item pt-2">
                <a class="nav-link text-dark d-flex" data-scroll="" href="#delete">
                  <i class="material-icons text-lg me-2">delete</i>
                  <span class="text-sm">Delete Account</span>
                </a>
              </li> --}}
            </ul>
          </div>
        </div>
        <div class="col-lg-9 mt-lg-0 mt-4">
          <!-- Card Basic Info -->
          <div class="card" id="works-card">
            <div class="card-body mt-2">
              <div class="d-flex">
                <h6>作品合集</h6>
                <button type="button" class="btn btn-info btn-sm  m-0 ms-auto" data-mdb-toggle="tooltip" title="Remove item" id="sort-btn">
                  调整顺序
                </button>
                <button type="button" class="btn btn-success btn-sm m-0 ms-auto d-none-ni" data-mdb-toggle="tooltip" title="Remove item" id="sort-save">
                  保存顺序
                </button>
              </div>
              <hr class="horizontal dark">
              <div class="row" id="works">
                @foreach($artist_works as $key=>$work)
                <div class="col-xl-3 col-lg-4 col-md-6 col-6 work-card" work-id="{{$work->id}}">
                  <div class="card shadow-lg mb-3">
                    <div class="card-header p-3 position-relative z-index-1">
                      <div class="position-relative border-radius-lg overflow-hidden">
                        <span class="mask bg-gradient-dark center-flex cursor-grab sort-mask">
                          <div class="text-center">
                            <i class="fa-solid fa-up-down-left-right text-white" style="font-size:3rem"></i>
                            <div class="text-white">拖动调整顺序</div>
                          </div> 
                        </span>
                        <img class="img-fluid border-radius-lg aspect-ratio-1 border work-image" src="{{$work->url_sm}}" />      
                      </div>
                    </div>
                    <div class="card-body p-3 pt-0">
                      <div class="text-end mt-2">
                        <button type="button" class="btn btn-outline-info btn-sm m-0 me-2 btn-edit"  work-id="{{$work->id}}" url-md="{{$work->url_md}}" onclick="edit_click(event)">
                          <i class="fa-solid fa-gear"></i>
                        </button>
                        <button type="button" class="btn btn-outline-danger btn-sm m-0 btn-delete"  work-id="{{$work->id}}" url-md="{{$work->url_md}}" onclick="delete_click(event)">
                          <i class="fas fa-trash"></i>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
                
                @endforeach
              </div>
            </div>
          </div>
          <div class="card mt-4" id="upload-work">
            <div class="card-header">
              <h5>上传图片</h5>
            </div>
            <div class="card-body pt-0">
              <div class="form-control border dropzone dz-clickable" id="cd_ref" file-type="design">
                <div class="dz-default dz-message"><button class="dz-button" type="button">文件拖到此处上传</button>
                </div>
              </div>
              <button class="btn bg-gradient-info btn-sm float-end mt-6 mb-0" id="start_upload">开始上传</button>
            </div>
          </div>
          <div class="card mt-4" id="video-work">
            <div class="card-header">
              <h5>视频链接</h5>
            </div>
            <div class="card-body pt-0 text-center">
              <button class="btn bg-gradient-info btn-lg my-3">上传视频链接作品</button>
            </div>
          </div>
          
          
        </div>
      </div>
     
    </div>
  </main>
  <div class="modal fade modal-lg" id="edit-modal" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="exampleModalLabel">编辑作品</h5>
          <button type="button" class="btn-close text-dark" data-bs-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body text-center show">
          <div class="row">
            <div class="col-6">
              <div class="border p-2">
                <img class="w-100" src="" id="edit-image">
              </div>
            </div>
            <div class="col-6">
              <div class="form-group text-start">
                <label class="text-start"> 标题 </label>
                <input type="email" class="form-control border border-radius-lg px-2" id="edit-title">
              </div>
              <div class="form-group text-start mt-4">
                <label class="text-start"> 分类 </label>
                <input type="email" class="form-control border border-radius-lg px-2" id="eidt-categories">
                
              </div>
              <div class="text-start mt-4">
                <p class="mb-2">标签</p>
                <select class="selectize" multiple="multiple" id="edit-tags">
                  @foreach ($tags as $tag)
                    <option value="{{$tag->id}}">{{$tag->name_zh}}</option>
                  @endforeach
                </select>
              </div>
              <div class="form-group text-start mt-4">
                <label class="text-start"> 作品介绍 </label>
                <textarea class="form-control border border-radius-lg px-2" autocomplete="off" rows="3" maxlength="30" id="edit-detail"></textarea>
              </div>

              <div class="mt-4">
                <div class="form-check form-switch d-flex align-items-center mb-4">
                  <input class="form-check-input" type="checkbox" id="rememberMe" checked>
                  <label class="form-check-label mb-0 ms-3 text-gray" for="rememberMe">展示</label>
                </div>
              </div>
            </div>
          </div>
          
        </div>
        <div class="modal-footer">
          <button type="button" class="btn bg-gradient-dark btn-sm" data-bs-dismiss="modal">取消</button>
          <button type="button" class="btn bg-gradient-info btn-sm" id="edit-save" work-id="">保存</button>
        </div>
      </div>
    </div>
  </div>

  <div class="modal fade modal-lg" id="upload-modal" tabindex="-1" role="dialog" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="">上传作品</h5>
          <button type="button" class="btn-close text-dark cancel_upload" data-bs-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body text-center show">
          <div class="row">
            <div class="col-6">
              <div class="border p-2">
                <img class="w-100" src="" id="upload-preview">
              </div>
            </div>
            <div class="col-6">
              <div class="form-group text-start">
                <label class="text-start"> 标题 </label>
                <input type="email" class="form-control border border-radius-lg px-2" id="upload-title" placeholder="">
              </div>
              <div class="form-group text-start mt-4">
                <label class="text-start"> 分类 </label>
                <input type="email" class="form-control border border-radius-lg px-2" v-model="user_email" placeholder="" id="categories">
                
              </div>
              <div class="form-group text-start mt-4">
                <label class="text-start"> 标签 </label>
                <select class="selectize" multiple="multiple" id="tags">
                  @foreach ($tags as $tag)
                    <option value="{{$tag->id}}">{{$tag->name_en}}</option>
                  @endforeach
                </select>
                
              </div>
              <div class="form-group text-start mt-4">
                <label class="text-start"> 作品介绍 </label>
                <textarea class="form-control border border-radius-lg px-2" autocomplete="off" rows="3" maxlength="30"></textarea>
              </div>

              <div class="mt-4">
                <div class="form-check form-switch d-flex align-items-center mb-4">
                  <input class="form-check-input" type="checkbox" id="rememberMe" checked>
                  <label class="form-check-label mb-0 ms-3 text-gray" for="rememberMe">展示</label>
                </div>
              </div>

              {{-- <div class="mt-4">
                <div class="form-check form-switch d-flex align-items-center mb-4">
                  <input class="form-check-input" type="checkbox" id="rememberMe" checked>
                  <label class="form-check-label mb-0 ms-3 text-gray" for="rememberMe">水印</label>
                </div>
              </div> --}}
            </div>
          </div>
          
        </div>
        <div class="modal-footer">
          <button type="button" class="btn bg-gradient-dark btn-sm cancel_upload" data-bs-dismiss="modal">取消</button>
          <button type="button" class="btn bg-gradient-info btn-sm" id="confirm_upload">确认上传</button>
        </div>
      </div>
    </div>
  </div>

  <div class="modal fade" tabindex="-1" id="delete-modal" role="dialog" aria-hidden="true">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">删除作品</h5>
          <button type="button" class="btn-close text-dark" data-bs-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-12" id="delete-img">
              <img class="img-fluid border-radius-lg aspect-ratio-1 border w-100" src="" id="delete_image">
            </div>
            <div class="col-12 text-center">
              <h3 class="text-danger">你确定要删除该作品吗</h3>
            </div>
          </div>
          
        </div>
        <div class="modal-footer">
          <button type="button" class="btn bg-gradient-dark btn-sm" data-bs-dismiss="modal">取消</button>
          <button type="button" class="btn bg-gradient-danger btn-sm" id="confirm_delete">确认删除</button>
        </div>
      </div>
    </div>
  </div>

  {{-- <div class="modal fade" tabindex="-1" id="delete-modal" role="dialog" aria-hidden="true">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">删除作品</h5>
          <button type="button" class="btn-close text-dark" data-bs-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-12" id="delete-img">
              <img class="img-fluid border-radius-lg aspect-ratio-1 border w-100" src="" id="delete_image">
            </div>
            <div class="col-12 text-center">
              <h3 class="text-danger">你确定要删除该作品吗</h3>
            </div>
          </div>
          
        </div>
        <div class="modal-footer">
          
          <button type="button" class="btn bg-gradient-danger btn-sm" id="confirm_delete">确认删除</button>
          <button type="button" class="btn bg-gradient-dark btn-sm" data-bs-dismiss="modal">取消</button>
        </div>
      </div>
    </div>
  </div> --}}


  
    
 
  
  <!--   Core JS Files   -->
  @include('artist_center/js')
  <script src="{{asset('dashboard/js/plugins/dropzone.min.js')}}"></script>
  <script>
    var artist_works = {!! json_encode($artist_works) !!}

    // const edit_tags = document.querySelector('#edit-tags');
    // const edit_tags_choices = new Choices(edit_tags,{
    //   removeItemButton: true,
    //   maxItemCount: 5,
    //   maxItemText: (maxItemCount) => {
    //     return "只能添加 "+ maxItemCount +" 个标签";
    //   },
    // });
    $(".selectize").selectize({
      plugins: ["remove_button"],
      create: function (input) {
        return {
          value: input,
          text: input,
        };
      }
    })

    const tags_select = $("#edit-tags")[0].selectize;

    const sort = document.getElementById('works');
    var pl = Sortable.create(sort, {
      swapThreshold: 1,
      handle: '.sort-mask',
      animation: 150,
      onEnd: function (e) {

      },
    });


    $( "#sort-btn" ).click(function() {
      $('.sort-mask').css("display","flex");
      $(this).hide();
      $('#sort-save').show();
    });

    $( "#sort-save" ).click(function() {
      let sorted_images_ids = [];
      $('.work-card').each(function(i, obj) {
        sorted_images_ids[i] = $(this).attr('work-id');
      });

      let list_data = {
        "_token": "{{ csrf_token() }}",
        'sorted_images_ids':sorted_images_ids
      }
      console.log(list_data);

      $.ajax({
        method: "POST",
        url: "{{url('artist_center/sortworks')}}",
        data: list_data,
        success: function(response) {
          if(response.code == 200 ){
            $( "#sort-save").hide();
            $('#sort-btn').show();
            $('.sort-mask').css("display","none");
            toastr.success(response.message, 'Success',{"positionClass": "toast-top-center",})
          }
          else{
            toastr.error(response.message, 'Error',{"positionClass": "toast-top-center",})
          }
        }
      });
    });

    // $( ".btn-edit" ).click(function() {
    //   let url_md = $(this).attr('url-md');
    //   $("#edit-image").attr('src', url_md);
    //   $('#edit-modal').modal('show')
    // })
    // $( ".btn-delete" ).click(function() {
    //   let url_md = $(this).attr('url-md');
    //   let work_id = $(this).attr('work-id');
    //   $("#delete_image").attr('src', url_md);
    //   $("#confirm_delete").attr('work-id',work_id)
    //   $('#delete-modal').modal('show')
    // })
    function edit_click(event) {
      let element = $(event.currentTarget)
      let url_md = element.attr('url-md');
      let work_id = element.attr('work-id');
      let edit_work = artist_works.find(work=>work.id == work_id)

      $("#edit-image").attr('src', url_md);
      $('#edit-modal').modal('show')
      $("#edit-save").attr('work-id', work_id);

      $("#edit-title").val(edit_work.title);
      $("#edit-detail").val(edit_work.detail)
                                                      
      tags_select.setValue(edit_work.tag_ids);
    }

    function delete_click(event){
      let element = $(event.currentTarget)
      let url_md = element.attr('url-md');
      let work_id = element.attr('work-id');
      $("#delete_image").attr('src', url_md);
      $("#confirm_delete").attr('work-id',work_id)
      $('#delete-modal').modal('show')
    }

    $( "#edit-save" ).click(function() {
      let work_id = $(this).attr('work-id');
      let edit_work_data = {
        "_token": "{{ csrf_token() }}",
        'id':work_id,
        'title': $("#edit-title").val(),
        'detail':$("#edit-detail").val(),
        'tags': tags_select.getValue(),
      }
      console.log(edit_work_data)
      $.ajax({
        method: "POST",
        url: "{{url('artist_center/edit_work')}}",
        data: edit_work_data,
        success: function(response) {
          if(response.code == 200 ){
            toastr.success(response.message, 'Success',{"positionClass": "toast-top-center",});

            $('#edit-modal').modal('hide')
            let index = artist_works.findIndex(work=>work.id == work_id)
            artist_works[index] = response.data
          }
          else{
            toastr.error(response.message, 'Error',{"positionClass": "toast-top-center",})
          }
        }
      });
      
    });
    

    $( "#confirm_delete" ).click(function() {
      let work_id = $(this).attr('work-id');

      let delete_data = {
        "_token": "{{ csrf_token() }}",
        'artist_id':"{{$artist->id}}",
        'work_id': work_id
      }
      

      
      $.ajax({
        method: "POST",
        url: "{{url('artist_center/delete_work')}}",
        data: delete_data,
        success: function(response) {
          if(response.code == 200 ){
            toastr.success(response.message, 'Success',{"positionClass": "toast-top-center",});

            $('#delete-modal').modal('hide')
            $(".work-card[work-id='"+ work_id +"']").remove();
            // location.reload();
          }
          else{
            toastr.error(response.message, 'Error',{"positionClass": "toast-top-center",})
          }
        }
      });
      // let key = $(this).attr('image-key');
      // $("#edit-image").attr('src',"{{asset('storage')}}/" + images[key]['url_md']);
    })

    Dropzone.autoDiscover = false;
    const dropzone = $('.dropzone').dropzone({
      url: "{{url('artist_center/upload_work')}}",
      autoProcessQueue:false,
      method: 'post',
      
      addRemoveLinks: true,
      dictRemoveFile: "移除",
      headers: {
          'X-CSRF-TOKEN': "{{ csrf_token() }}"
      },
      init: function() {
        const dropzone = this;
        let files;
        let i = 0;
        this.on('sending', function(file, xhr, formData){
          
        });

        this.on("addedfile", file => {
          setTimeout(function () {
            //直接写的话拿不到最后一个file
            files =  dropzone.getQueuedFiles();
            i = 0; 
          }, 10);
        });

        this.on("removedfile", file => {
            
        });

        this.on("success", function(file, response){

          toastr.success(response.message, 'Success',{"positionClass": "toast-top-center",})
          
          $('#confirm_upload').attr("disabled",false)
          $('#confirm_upload').html("确认上传")
          
          // let success_file_src = URL.createObjectURL(file);
          let new_work = response.data;
          appendNewWork(new_work);
          artist_works.push(new_work);


          if(i+1 < files.length){
            i++
            let next_file_src = URL.createObjectURL(files[i]);
            $('#upload-preview').attr("src",next_file_src);
          }
          else{
            files = [];
            $('#upload-modal').modal('hide')
          }

          
        });

        this.on("error", file => {
          $('#confirm_upload').attr("disabled",false)
          $('#confirm_upload').html("确认上传")
        });

        
        $("#start_upload").click(function() {
          console.log(files)
          if(files && files.length > 0){
            let imageSrc = URL.createObjectURL(files[i]);
            $('#upload-preview').attr("src",imageSrc)
            $('#upload-modal').modal('show')
          }
          else{
            toastr.error("没有上传的图片", 'Error',{"positionClass": "toast-top-center",})
          }
        })

        $('#confirm_upload').click(function() {

          $('#confirm_upload').attr("disabled",true)
          $('#confirm_upload').html("上传中...")


          dropzone.processFile(files[i]);

        })

        // const startButton = document.querySelector("#cancel_upload")
        // const confirmButton = document.querySelector("#confirm_upload")
        // var i = 0;


        // $('#upload-modal').on('hidden.bs.modal', function () {
        //   let files = dorpzone.getQueuedFiles();
        //   dorpzone.removeFile(files[0])
        // })

        // $('.cancel_upload').on('click', function () {
          
        // })

        // cancel_upload.addEventListener("click", function() {
        //   let files = dorpzone.getQueuedFiles();
        //   dorpzone.removeFile(files[0])
        //   // dorpzone.processQueue()
          
        //   // const imageSrc = URL.createObjectURL(files[i]);
          
        //   // const imagePreviewElement = document.querySelector("#upload-preview");
          
        //   // imagePreviewElement.src = imageSrc;

        //   // $('#upload-modal').modal('show')

        // });

        // confirmButton.addEventListener("click", function() {
        //   dorpzone.processQueue()
        //   $('#upload-modal').modal('hide')
        //   // let files = dorpzone.getQueuedFiles();
        //   // let current_file = files[i];
        //   // dorpzone.processFile(current_file);
          
          
        // });




      }
    });

    function appendNewWork(work){
      let new_html = '<div class="col-xl-3 col-lg-4 col-md-6 col-6 work-card" work-id="'+work.id+'">'+
                        '<div class="card shadow-lg mb-3">'+
                          '<div class="card-header p-3 position-relative z-index-1">'+
                            '<div class="position-relative border-radius-lg overflow-hidden">'+
                              '<span class="mask bg-gradient-dark center-flex cursor-grab sort-mask">'+
                                '<div class="text-center">'+
                                  '<i class="fa-solid fa-up-down-left-right text-white" style="font-size:3rem"></i>'+
                                  '<div class="text-white">拖动调整顺序</div>'+
                                '</div>'+
                              '</span>'+
                              '<img class="img-fluid border-radius-lg aspect-ratio-1 border work-image" src="'+ work.url_sm +'"/>'+      
                            '</div>'+
                          '</div>'+
                          '<div class="card-body p-3 pt-0">'+
                            '<div class="text-end mt-2">'+
                              '<button type="button" class="btn btn-outline-info btn-sm m-0 me-2 btn-edit" work-id="'+work.id+'" url-md="'+ work.url_md +'" onclick="edit_click(event)">'+
                                '<i class="fa-solid fa-gear"></i>'+
                              '</button>'+
                              '<button type="button" class="btn btn-outline-danger btn-sm m-0 btn-delete" work-id="'+work.id+'" url-md="'+ work.url_md +'" onclick="delete_click(event)">'+
                                '<i class="fas fa-trash"></i>'+
                              '</button>'+
                            '</div>'+
                          '</div>'+
                        '</div>'+
                      '</div>'
      $('#works').append(new_html);
    }

    function removeWorkCard(){




    }

    function disablebutton(button,text){


    }

    // $("#confirm_upload").click(function() {
    //   dropzone.processQueue()
    // })

    







    
    


  </script>
  
 
</body>

</html>
