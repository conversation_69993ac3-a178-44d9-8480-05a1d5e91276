<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <link rel="apple-touch-icon" sizes="76x76" href="../../../assets/img/apple-icon.png">
  <link rel="icon" type="image/png" href="../../../assets/img/favicon.png">
  <title>
    Pipipen
  </title>
  <!--     Fonts and icons     -->
  @include('artist_center/css')
</head>

<body class="g-sidenav-show bg-gray-200">
  @include('artist_center/sidebar')
  <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg ">
    <!-- Navbar -->
    @include('artist_center/topnav')
    <!-- End Navbar -->
    <div class="container-fluid py-5" id="app">
      <div class="row mt-5 mb-5">
        <div class="col-lg-6 col-12 position-relative">
          <div class="card">
            <div class="card-header p-3 pt-2">
              <div class="icon icon-lg icon-shape bg-gradient-info shadow text-center border-radius-xl mt-n4 me-3 float-start">
                <i class="material-icons opacity-10">description</i>
              </div>
              <h6 class="mb-0">内容</h6>
              <div class="w-35 ms-auto">
                <select class="form-select border px-2">
                  <option>原文 - 简体中文</option>
                  <option>译文 - English（正在等待系统翻译）</option>
                </select>
              </div>
            </div>
            <div class="card-body pt-2">
              <div class="mt-4">
                <p class="mb-2">委托标题</p>
                <input type="email" class="form-control border border-radius-lg px-2" placeholder="" value="{{$service->originalContent->title}}" id="title">
              </div>
              <div class="mt-4">
                <p class="mb-2">简介</p>
                <textarea class="form-control border border-radius-lg px-2 resize-none" autocomplete="off" rows="7" id="detail">{!!$service->originalContent->detail!!}</textarea>
              </div>
              
              <div class="d-flex justify-content-end mt-4">
                {{-- <button type="button" name="button" class="btn btn-light m-0">返回</button> --}}
                <button type="button" name="button" class="btn bg-gradient-success m-0 ms-2" id="translate-content">生成译文</button>
                <button type="button" name="button" class="btn bg-gradient-info m-0 ms-2" id="save-content">保存原文</button>
                
              </div>
            </div>
          </div>
          <div class="card mt-4">
            <div class="card-header p-3 pt-2">
              <div class="icon icon-lg icon-shape bg-gradient-info shadow text-center border-radius-xl mt-n4 me-3 float-start">
                <i class="material-icons opacity-10">image</i>
              </div>

              <h6 class="mb-0">例图</h6>
              <div class="ms-auto text-end">
                <div class="ms-3 btn btn-sm btn-outline-info" id="sort-btn">调整顺序</div>
                <div class="ms-3 btn btn-sm btn-outline-success d-none-ni" id="sort-save">保存顺序</div>
                <div class="ms-3 btn btn-sm btn-outline-info" id="add-showcase-btn">添加例图</div>
              </div>
              <p class="text-sm mb-0"></p>
            </div>
            <div class="card-body pt-2">
              {{-- <p class="mb-2 opacity-5 text-sm">例图的添加与删除会立即生效，无需再点击保存</p> --}}
              <div class="row" id="showcases">
                @foreach($service_showcases as $showcase)
                <div class="col-4 sort-card" showcase-id="{{$showcase->id}}">
                  <div class="card shadow-lg mb-3">
                    <div class="card-header p-3 position-relative z-index-1">
                      <div class="position-relative border-radius-lg overflow-hidden">
                        <span class="mask bg-gradient-dark center-flex cursor-grab sort-mask">
                          <div class="text-center">
                            <i class="fa-solid fa-up-down-left-right text-white" style="font-size:3rem"></i>
                            <div class="text-white">拖动调整顺序</div>
                          </div> 
                        </span>
                        <img class="img-fluid border-radius-lg aspect-ratio-1 border work-image w-100" src="{{$showcase->url_sm}}" />      
                      </div>
                    </div>
                    <div class="card-body p-3 pt-0 d-flex">
                      <div class="">
                        <span class="badge badge-info">来自作品</span>
                      </div>
                      <div class="ms-auto text-danger justify-content-center">
                          <i class="fas fa-trash cursor-pointer delete-showcase" url-md="{{$showcase->url_md}}" showcase-id="{{$showcase->id}}"></i>
                      </div>
                    </div>
                  </div>
                </div>
                @endforeach
              </div>
            </div>
          </div>
        </div>
        <div class="col-lg-6 col-12 position-relative">
          <div class="card">
            <div class="card-header p-3 pt-2">
              <div class="icon icon-lg icon-shape bg-gradient-info shadow text-center border-radius-xl mt-n4 me-3 float-start">
                <i class="material-icons opacity-10">settings</i>
              </div>
              <h6 class="mb-0">设置</h6>
              <p class="text-sm mb-0"></p>
            </div>
            <form id="setting_form">
              <div class="card-body pt-2">
                  <div class="row mt-4">
                    <div class="col-6">
                      <p class="mb-2">价格（￥）*</p>
                      <input type="text" class="form-control border border-radius-lg px-2" value="{{$service->price}}" id="price" required>
                    </div>
                    <div class="col-6">
                      <p class="mb-2">状态 *</p>
                      <select class="form-control border border-radius-lg px-2" id="status">
                        <option value="1"  {{$service->status==1 ? "selected":""}}>可接</option>
                        <option value="2"  {{$service->status==2 ? "selected":""}}>排队</option>
                        <option value="3"  {{$service->status==3 ? "selected":""}}>关闭</option>
                        <option value="0"  {{$service->status==0 ? "selected":""}}>隐藏</option>
                      </select>  
    
                    </div>
                  </div>
                  <div class="row mt-4">
                    <div class="col-6">
                      <p class="mb-2">分类 *</p>
                      <select class="selectize" multiple="multiple" id="categories" required>
                        @foreach ($service_categories as $service_c)
                          <option value="{{$service_c->id}}">{{$service_c->name_zh}}</option>
                        @endforeach
                      </select>  
                    </div>
                    <div class="col-6">
                      <p class="mb-2">标签 *</p>
                      <select class="selectize" multiple="multiple" id="tags" required>
                        @foreach ($tags_categorized as $category_name=>$tags)
                          <optgroup class="" label="{{$category_name}} : ">
                            @foreach($tags as $tag)
                            <option value="{{$tag->id}}">{{$tag->name_zh}}</option>
                            @endforeach
                          </optgroup>
                        @endforeach
                      </select>
    
                    </div>
                  </div>

                  

                  <div class="row mt-4">
                    <div class="col-6">
                      <p class="mb-2">偏好类型</p>
                      <select class="selectize" multiple="multiple" id="prefer">
                        @foreach ($tags as $tag)
                          <option value="{{$tag->id}}">{{$tag->name_zh}}</option>
                        @endforeach
                      </select>
                    </div>
                    <div class="col-6">
                      <p class="mb-2">不接类型</p>
                      <select class="selectize" multiple="multiple" id="cantdo">
                        @foreach ($tags as $tag)
                          <option value="{{$tag->id}}">{{$tag->name_zh}}</option>
                        @endforeach
                      </select>
    
                    </div>
                  </div>

                  <div class="row mt-4">
                    <div class="col-6">
                      <p class="mb-2">工期（天）*</p>
                      <input type="number" id="days_need"class="form-control border border-radius-lg px-2" value="{{$service->days_need}}" min="0" step="1"  oninput="validity.valid||(value = this.previousValue);" required>
                    </div>
                    <div class="col-6">
                      <p class="mb-2">剩余稿位 *</p>
                      <input type="number" id="stock" class="form-control border border-radius-lg px-2" value="{{$service->stock}}" min="0" step="1"  oninput="validity.valid||(value = this.previousValue);" required>
                    </div>
                  </div>
                  
                  
    
                  <div class="mt-4">
                    <p class="mb-2">使用范围</p>
                    <select class="form-control border border-radius-lg px-2" id="usage">
                      <option value="1" {{$service->usage==1 ? "selected":""}}>个人用</option>
                      <option value="2" {{$service->usage==2 ? "selected":""}}>正常商用</option>
                      <option value="3" {{$service->usage==3 ? "selected":""}}>需双方协商</option>
                    </select>  
                  </div>
                
                

              
                {{-- <div class="mt-4">
                  <p class="mb-1">例图 <span class="ms-3 badge bg-gradient-success cursor-pointer" id="add-showcase-btn">+</span></p>
                  <p class="mb-2 opacity-5 text-sm">例图的添加与删除会立即生效，无需再点击保存</p>
                  <div class="row">
                    <div class="col-xl-3 col-lg-4 sort-card" image-id="">
                      <div class="card shadow-lg mb-3">
                        <div class="card-header p-3 position-relative z-index-1">
                          <div class="position-relative border-radius-lg overflow-hidden">
                            <span class="mask bg-gradient-dark center-flex cursor-grab sort-mask">
                              <div class="text-center">
                                <i class="fa-solid fa-up-down-left-right text-white" style="font-size:3rem"></i>
                                <div class="text-white">拖动调整顺序</div>
                              </div> 
                            </span>
                            <img class="img-fluid border-radius-lg aspect-ratio-1 border work-image" src="{{asset('storage/images\October2022\VDRaRN8JbOwq10awle1Y.jpg')}}" />      
                          </div>
                        </div>
                        <div class="card-body p-3 pt-0">
                          <div class="text-end mt-2 text-danger">
                              <i class="fas fa-trash cursor-pointer"></i>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                  </div>
                </div> --}}




                
                <div class="d-flex justify-content-end mt-4">
                  <button type="submit" name="button" class="btn bg-gradient-info m-0 ms-2" id="save-setting">保存设置</button>
                </div>
              </div>
            <form>
          </div>
          
          <div class="card mt-4">
            <div class="card-header p-3 pt-2">
              <div class="icon icon-lg icon-shape bg-gradient-info shadow text-center border-radius-xl mt-n4 me-3 float-start">
                <i class="material-icons opacity-10">arrow_forward</i>
              </div>
              <h6 class="mb-0">创作节点</h6>
              <p class="text-sm mb-0"></p>
            </div>
            <div class="card-body pt-2">
              <div class="mt-4">
                <div class="d-flex">
                  <div class="form-control border border-radius-lg px-2" id="checkpoints">
                    L2D 验收 （30% - 80% - 100%）
                  </div>
                  <div class="ms-3 w-25 btn bg-gradient-info m-0 ms-2" id="edit-stages">编辑创作节点</div>
                </div>
              </div>
            </div>
          
          </div>
          
        </div>
    </div>
  </main>

  <div class="modal fade modal-xl" id="stages" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
      <div class="modal-content max-height-vh-80">
        <div class="modal-header">
          <h5 class="modal-title" id="exampleModalLabel">验收节点</h5>
          <button type="button" class="btn-close text-dark" data-bs-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body text-center show overflow-y-auto">
          <div class="row">
            <div class="col-4 text-start border-end">
              <h6>预设模板</h6>
              <select class="form-control border border-radius-lg px-2" >
                <option>立绘 （30% - 80% - 100%）</option>
                <option>Skeb（100%）</option>
              </select>
              <button type="button" class="btn btn-outline-info btn-sm mt-3" id="confirm">套用模板</button>
            </div>
            <div class="col-8 pb-5 ps-5">
              <h6 class="hidden">占位</h6>
              <div class="timeline timeline-one-side text-start" data-timeline-axis-style="dotted">
                <div class="stages">
                  @foreach($service_stages as $stage)
                  <div class="timeline-block mb-5">
                    <span class="timeline-step bg-dark p-3">
                      <i class="material-icons text-white text-sm opacity-10">
                        notifications
                      </i>
                    </span>
                    <div class="timeline-content pt-1 ">
                      <div class="d-flex align-items-center">
                        <div class="input-group input-group-outline me-3 w-50 is-focused">
                          <label class="form-label">节点名称</label>
                          <select type="text" class="form-control">
                            @foreach($service_stage_selections as $selection)
                              <option value="{{$selection->id}}" {{$stage->service_stage_selection_id == $selection->id ? "selected":""}}>
                                {{$selection->name_zh}}
                              </option>
                            @endforeach
                          </select>
                        </div>
                        <div>--</div>
                        <div class="input-group input-group-outline mx-3 w-25 is-focused">
                          <label class="form-label">金额 % </label>
                          <input type="number" class="form-control" value="{{$stage->percent}}"/>
                        </div>
                        <div class="cursor-pointer delete-stage text-danger">x</div>
                      </div>
                    </div>
                  </div>
                  @endforeach
                </div>
                

                <div class="d-flex align-items-center cursor-pointer add-stage" >
                  <span class="timeline-step bg-success p-3">
                    <i class="material-icons text-white text-sm opacity-10">
                      add
                    </i>
                  </span>
                  <div class="timeline-content top-0 p-0" >
                    <h6 class="text-dark text-sm font-weight-bold mb-0">添加新的节点</h6>
                  </div>
                </div>
                

                
                
                
              </div>
            </div>
          </div>
          
        </div>
        <div class="modal-footer">
          <button type="button" class="btn bg-gradient-dark btn-sm" data-bs-dismiss="modal">取消</button>
          <button type="button" class="btn bg-gradient-info btn-sm" id="save-nodes">确认</button>
        </div>
      </div>
    </div>
  </div>

  <div class="modal fade modal-xl" id="add-showcase-modal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
      <div class="modal-content max-height-vh-80">
        <div class="modal-header">
          <h5 class="modal-title" id="exampleModalLabel">添加例图</h5>
          <button type="button" class="btn-close text-dark" data-bs-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body text-center show overflow-y-auto">
          <div class="row">
            <div class="col-4 text-start border-end">
              <h6>例图来源</h6>
              <select class="form-control border border-radius-lg px-2" >
                <option>从作品选择</option>
                <option>上传图片</option>
                <option>视频链接</option>
              </select>
            </div>
            <div class="col-8 pb-5 ps-5 text-start">
              <div class="worklios">
                <h6>作品</h6>
                <div class="row" id="worklios">
                  @foreach($addable_works as $key=>$work)
                  <div class="col-xl-4" image-id="{{$work->id}}">
                    <div class="card shadow-lg mb-3 cursor-pointer select-work" work-id="{{$work->id}}">
                      <div class="card-body p-3">
                        <div class="position-relative border-radius-lg overflow-hidden">
                          <img class="img-fluid border-radius-lg aspect-ratio-1 border work-image" src="{{$work->url_sm}}" />      
                        </div>
                      </div>
                    </div>
                  </div>
                  @endforeach
                </div>
              </div>
              <div class="upload d-none">
                <h6>上传图片</h6>
              </div>

              <div class="iframe d-none">
                <h6>外部链接</h6>
              </div>
              
              
            </div>
          </div>
          
        </div>
        <div class="modal-footer">
          <button class="btn bg-gradient-dark btn-sm" data-bs-dismiss="modal">取消</button>
          <button class="btn bg-gradient-info btn-sm" id="add_showcase" add-type="work">添加</button>
        </div>
      </div>
    </div>
  </div>

  <div class="modal fade" tabindex="-1" id="delete-showcase-modal" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">删除例图</h5>
          <button type="button" class="btn-close text-dark" data-bs-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-10 mx-auto" id="delete-img">
              <img class="img-fluid border-radius-lg border w-100" src="" id="delete_showcase_img">
              <input type="hidden" id="delete-showcase-id">
            </div>
            <div class="col-12 text-center mt-3">
              <h6 class="text-danger">确定要删除该例图吗</h6>
              <div class="text-dark opacity-6 text-sm">来自作品的例图不会删除作品本身</div>
            </div>
          </div>
          
        </div>
        <div class="modal-footer">
          <button type="button" class="btn bg-gradient-dark btn-sm" data-bs-dismiss="modal">取消</button>
          <button type="button" class="btn bg-gradient-danger btn-sm" id="comfirm-delete-showcase">确认删除</button>
        </div>
      </div>
    </div>
  </div>

  
  
  <!--   Core JS Files   -->
  @include('artist_center/js')
  <script src="{{asset('dashboard/js/plugins/flatpickr.min.js')}}"></script>
  <script src="https://npmcdn.com/flatpickr/dist/l10n/zh.js"></script>

  <script>

    

    // flatpickr(".flatpickr",{
    //   "locale": "zh"  
    // });

    

    $(".selectize").selectize({
      plugins: ["remove_button"],
      // create: function (input) {
      //   return {
      //     value: input,
      //     text: input,
      //   };
      // }
    })

    const selected_data = {!! json_encode($selected_data) !!}
    const categories = $("#categories")[0].selectize;
    const general_tags = $("#tags")[0].selectize;
    const prefer_tags = $("#prefer")[0].selectize;
    const cantdo_tags = $("#cantdo")[0].selectize;

    categories.setValue(selected_data.categories)
    general_tags.setValue(selected_data.general_tag_ids)
    prefer_tags.setValue(selected_data.prefer_tag_ids)
    cantdo_tags.setValue(selected_data.cantdo_tag_ids)

    
    $('#add-showcase-btn').click(function() {
      $('#add-showcase-modal').modal('show')
    })

    $( "#save-content" ).click(function() {
      let content_data = {
        "_token": "{{ csrf_token() }}",
        "service_id":"{{$service->id}}",
        "title": $("#title").val(),
        "detail": $("#detail").val(),
      }
      
      $.ajax({
        method: "POST",
        url: "{{url('artist_center/save_content')}}",
        data: content_data,
        success: function(response) {
          console.log(response)
          if(response.code == 200 ){
            toastr.success(response.message, 'Success',{"positionClass": "toast-top-right",})
          }
          else{
            toastr.error(response.message, 'Error',{"positionClass": "toast-top-center",})
          }
        }
      });
    });

    $("#save-setting").click(function() {
      
      // console.log(setting_data);

    })

    $("#setting_form").submit(function(e) {
      e.preventDefault();
      let setting_data = {
        "_token": "{{ csrf_token() }}",
        "service_id":"{{$service->id}}",
        "price": $("#price").val(),
        "status": $("#status").val(),
        "categories":categories.getValue(),
        "general_tags":general_tags.getValue(),
        "prefer_tags":prefer_tags.getValue(),
        "cantdo_tags":cantdo_tags.getValue(),
        "days_need":$("#days_need").val(),
        "stock":$("#stock").val(),
        "usage":$('#usage :selected').val()
      }
      // console.log(setting_data);

      $.ajax({
        method: "POST",
        url: "{{url('artist_center/save_setting')}}",
        data: setting_data,
        success: function(response) {
          console.log(response)
          if(response.code == 200 ){
            toastr.success(response.message, 'Success',{"positionClass": "toast-top-right",})
          }
          else{
            toastr.error(response.message, 'Error',{"positionClass": "toast-top-center",})
          }
        }
      });

    });



    $('.select-work').click(function() {
      if($(this).hasClass('selected')){
        $(this).removeClass('selected');
      }
      else{
        $(this).addClass('selected');
      }    
    })

    $('#add_showcase').click(function() {
      let type = $(this).attr('add-type')
      let showcase_data = {}
      if(type == 'work'){
        let selected_works = []
        $('.selected').each(function(i, obj) {
          let work_id = $(this).attr('work-id')
          selected_works.push(work_id)
        });

        showcase_data = {
          "_token": "{{ csrf_token() }}",
          "service_id":"{{$service->id}}",
          "type":"work",
          "selected_works":selected_works
        }
      }
      $.ajax({
        method: "POST",
        url: "{{url('artist_center/add_service_showcase')}}",
        data: showcase_data,
        success: function(response) {
          // console.log(response)
          if(response.code == 200 ){
            toastr.success(response.message, 'Success',{"positionClass": "toast-top-center",})
            setTimeout(function() {
              window.location.reload();
            }, 800);
          }
          else{
            toastr.error(response.message, 'Error',{"positionClass": "toast-top-center",})
          }
        }
      });
    })

    
    $('.delete-showcase').click(function() {
      let url_md = $(this).attr('url-md')
      let showcase_id = $(this).attr('showcase-id')
      $('#delete_showcase_img').attr('src',url_md)
      $('#delete-showcase-id').val(showcase_id)
      $('#delete-showcase-modal').modal('show')
    })

    $('#comfirm-delete-showcase').click(function(){
      let delete_showcase_data = {
        "_token": "{{ csrf_token() }}",
        "delete_id":$('#delete-showcase-id').val(),
        // "service_id":"{{$service->id}}",
      }
      $.ajax({
        method: "POST",
        url: "{{url('artist_center/delete_service_showcase')}}",
        data: delete_showcase_data,
        success: function(response) {
          if(response.code == 200 ){
            toastr.success(response.message, 'Success',{"positionClass": "toast-top-center",})
            setTimeout(function() {
              window.location.reload();
            }, 800);
          }
          else{
            toastr.error(response.message, 'Error',{"positionClass": "toast-top-center",})
          }
        }
      });
    });

    const sort = document.getElementById('showcases');
    var pl = Sortable.create(sort, {
      swapThreshold: 1,
      handle: '.sort-mask',
      animation: 150,
      onEnd: function (e) {

      },
    });


    $( "#sort-btn" ).click(function() {
      $('.sort-mask').css("display","flex");
      $(this).hide();
      $('#sort-save').css("display","inline-block");
    });

    $( "#sort-save" ).click(function() {
      let sorted_showcase_ids = [];
      $('.sort-card').each(function(i, obj) {
        sorted_showcase_ids[i] = $(this).attr('showcase-id');
      });

      let list_data = {
        "_token": "{{ csrf_token() }}",
        "service_id":"{{$service->id}}",
        'sorted_showcase_ids':sorted_showcase_ids,
      }
      
      $.ajax({
        method: "POST",
        url: "{{url('artist_center/sort_service_showcases')}}",
        data: list_data,
        success: function(response) {
          if(response.code == 200 ){
            $( "#sort-save").hide();
            $('#sort-btn').show();
            $('.sort-mask').css("display","none");
            toastr.success(response.message, 'Success',{"positionClass": "toast-top-center",})
          }
          else{
            toastr.error(response.message, 'Error',{"positionClass": "toast-top-center",})
          }
        }
      });
    });

    
    $("#edit-stages" ).click(function() {
      $('#stages').modal('show')
    })

    $(".add-stage" ).click(function() {
      let new_point_html = '<div class="timeline-block mb-5">'+
                              '<span class="timeline-step bg-dark p-3">'+
                                '<i class="material-icons text-white text-sm opacity-10">'+
                                'notifications'+
                                '</i>'+
                              '</span>'+
                              '<div class="timeline-content pt-1 ">'+
                                '<div class="d-flex align-items-center">'+
                                  '<div class="input-group input-group-outline me-3 w-50 is-focused">'+
                                    '<label class="form-label">节点名称</label>'+
                                    '<select type="text" class="form-control">'+
                                      @foreach($service_stage_selections as $selection)
                                      '<option value="{{$selection->id}}">{{$selection->name_zh}}</option>'+
                                      @endforeach
                                    '</select>'+
                                  '</div>'+
                                  '<div>--</div>'+
                                  '<div class="input-group input-group-outline mx-3 w-25 is-focused">'+
                                    '<label class="form-label">金额 % </label>'+
                                    '<input type="number" class="form-control"/>'+
                                  '</div>'+
                                  '<div class="cursor-pointer delete-stage text-danger">x</div>'+
                                '</div>'+
                              '</div>'+
                            '</div>'
                            
                          
      $('.stages').append(new_point_html);
    })

    $().click(function(){

      
    })

    $("#save-stages").click(function(){
      let nodes = [
        {
          "name":{
            "en":"Sketch",
            "zh":"草稿",
          },
          "percent": 30,
        },
        {

        },
      ]
      console.log(nodes)
    })


    


    
    






    // if (document.getElementById('edit-deschiption-edit')) {
    //   var quill = new Quill('#edit-deschiption-edit', {
    //     theme: 'snow' // Specify theme in configuration
        
    //   });
    // };
  </script>
</body>

</html>
