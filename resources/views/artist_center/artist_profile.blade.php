<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <link rel="apple-touch-icon" sizes="76x76" href="../../../assets/img/apple-icon.png">
  <link rel="icon" type="image/png" href="../../../assets/img/favicon.png">
  <title>
    ESS Dashboard
  </title>
  <!--     Fonts and icons     -->
  @include('artist_center/css')
</head>

<body class="g-sidenav-show bg-gray-200">
  @include('artist_center/sidebar')
  <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg ">
    <!-- Navbar -->
    @include('artist_center/topnav')

    <div class="container-fluid py-5" id="app">
      <div class="row mt-5 mb-5">
        <div class="col-lg-3">
          <div class="card position-sticky top-9">
            <ul class="nav flex-column bg-white border-radius-lg p-3">
              <li class="nav-item">
                <a class="nav-link text-dark d-flex" data-scroll="" href="">
                  <i class="material-icons text-lg me-2">person</i>
                  <span class="text-sm">基本信息</span>
                </a>
              </li>
              {{-- <li class="nav-item pt-2">
                <a class="nav-link text-dark d-flex" data-scroll="" href="">
                  <i class="material-icons text-lg me-2">receipt_long</i>
                  <span class="text-sm">Change Password</span>
                </a>
              </li> --}}
              {{-- <li class="nav-item pt-2">
                <a class="nav-link text-dark d-flex" data-scroll="" href="#password">
                  <i class="material-icons text-lg me-2">lock</i>
                  <span class="text-sm">Change Password</span>
                </a>
              </li>
              <li class="nav-item pt-2">
                <a class="nav-link text-dark d-flex" data-scroll="" href="#2fa">
                  <i class="material-icons text-lg me-2">security</i>
                  <span class="text-sm">2FA</span>
                </a>
              </li>
              <li class="nav-item pt-2">
                <a class="nav-link text-dark d-flex" data-scroll="" href="#accounts">
                  <i class="material-icons text-lg me-2">badge</i>
                  <span class="text-sm">Accounts</span>
                </a>
              </li>
              <li class="nav-item pt-2">
                <a class="nav-link text-dark d-flex" data-scroll="" href="#notifications">
                  <i class="material-icons text-lg me-2">campaign</i>
                  <span class="text-sm">Notifications</span>
                </a>
              </li>
              <li class="nav-item pt-2">
                <a class="nav-link text-dark d-flex" data-scroll="" href="#sessions">
                  <i class="material-icons text-lg me-2">settings_applications</i>
                  <span class="text-sm">Sessions</span>
                </a>
              </li>
              <li class="nav-item pt-2">
                <a class="nav-link text-dark d-flex" data-scroll="" href="#delete">
                  <i class="material-icons text-lg me-2">delete</i>
                  <span class="text-sm">Delete Account</span>
                </a>
              </li> --}}
            </ul>
          </div>
        </div>
        <div class="col-lg-9 mt-lg-0 mt-4">
          <!-- Card Basic Info -->
          <div class="card" id="basic-info">
            <div class="card-header">
              <div class="page-header min-height-300 border-radius-xl" style="background-image: url('');">
                <i class="fa-solid fa-pen-to-square text-info edit-cover-icon cursor-pointer mt-3 me-3"></i>
              </div>
              <div class="mt-n6 text-center">
                <div class="position-relative mx-auto" style="width:fit-content">
                  <img class="avatar avatar-xxl shadow-xl border" src="{{asset('storage/'.$artist->avatar)}}" alt="artist avatar" loading="lazy">
                  <i class="fa-solid fa-pen-to-square text-info edit-avatar-icon cursor-pointer"></i>
                </div>
              </div> 
            </div>
            <div class="card-body pt-0">
              <h6>基本信息</h6>
              <div class="row">
                <div class="col-6">
                  <div class="input-group input-group-static">
                    <label>画师名</label>
                    <input type="text" class="form-control" id="name" value="{{$artist->name}}">
                  </div>
                </div>
                {{-- <div class="col-6">
                  <div class="input-group input-group-static">
                    <label>Name</label>
                    <input type="text" class="form-control" v-model="user_name">
                  </div>
                </div> --}}
              </div>
              <label class="mt-4 ms-0">个人简介</label>
              
              <div class="">
                <div id="edit-deschiption-edit" style="min-height:200px">
                  {!!$artist->intro!!}
                </div>
              </div>
    
             
              <h6 class="mt-4">相关链接</h6>
              <div class="row">
                <div class="col-6 mt-2">
                  <div class="input-group input-group-static">
                    <label>Twitter</label>
                    <input type="text" class="form-control" id="link_twitter" value="{{$artist->link_twitter}}">
                  </div>
                </div>
                <div class="col-6 mt-2">
                  <div class="input-group input-group-static">
                    <label>Pixiv</label>
                    <input type="text" class="form-control" id="link_pixiv" value="{{$artist->link_pixiv}}">
                  </div>
                </div>
                <div class="col-6 mt-2">
                  <div class="input-group input-group-static">
                    <label>Bilibili</label>
                    <input type="text" class="form-control" id="link_bilibili" value="{{$artist->link_bilibili}}">
                  </div>
                </div>
                <div class="col-6 mt-2">
                  <div class="input-group input-group-static">
                    <label>Weibo</label>
                    <input type="text" class="form-control"  id="link_weibo" value="{{$artist->link_weibo}}">
                  </div>
                </div>
                
              </div>

              
              <button class="btn bg-gradient-info btn-sm float-end mt-6 mb-0" id="btn_save">保存</button>
            </div>
          </div>
          <!-- Card Change Password -->
          
          
        </div>
      </div>
     
    </div>
  </main>
  <div class="modal" tabindex="-1" id="avatar-modal" role="dialog" aria-hidden="true">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">
            更换头像
            
          </h5>
          
          <button type="button" class="btn-close text-dark" data-bs-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <p>支持PNG/JPG格式，最大2MB</p>
          <div class="text-center">
            <img class="avatar avatar-xxl shadow-xl border" src="{{asset('storage/'.$artist->avatar)}}" alt="artist avatar" loading="lazy" id="avatar_preview">
            <div class="mt-3">
              <button class="btn bg-gradient-info btn-sm" id="choose_avatar">更换头像</button>
              <input type="file" class="d-none" accept="image/png, image/jpeg" id="avatar_file" onchange="previewImage(event);" >
            </div>
          </div>
          
          {{-- <div class="text-danger" id="avatar-error"></div> --}}
        </div>
        <div class="modal-footer">
          <button type="button" class="btn bg-gradient-dark btn-sm" data-bs-dismiss="modal">取消</button>
          <button type="button" class="btn bg-gradient-success btn-sm" id="save-avatar">保存</button>
        </div>
      </div>
    </div>
  </div>

  <div class="modal modal-lg" tabindex="-1" id="cover-modal" role="dialog" aria-hidden="true">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">
            更换首页背景
          </h5>
          
          <button type="button" class="btn-close text-dark" data-bs-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <p>支持PNG/JPG格式，最大5MB</p>
          <div class="text-center">
            <div class="page-header min-height-300 border-radius-xl" style="background-image: url('');">
              
            </div>
            <div class="mt-3">
              <button class="btn bg-gradient-info btn-sm" id="choose_avatar">更换首页背景</button>
              <input type="file" class="d-none" accept="image/png, image/jpeg" id="avatar_file" onchange="previewImage(event);" >
            </div>
          </div>
          
          {{-- <div class="text-danger" id="avatar-error"></div> --}}
        </div>
        <div class="modal-footer">
          <button type="button" class="btn bg-gradient-dark btn-sm" data-bs-dismiss="modal">取消</button>
          <button type="button" class="btn bg-gradient-success btn-sm" id="save-banner">保存</button>
        </div>
      </div>
    </div>
  </div>
  
  <!--   Core JS Files   -->
  @include('artist_center/js')
  <script src="{{asset('dashboard/js/plugins/dropzone.min.js')}}"></script>
  <script>
    // if (document.getElementById('edit-deschiption-edit')) {
    //   var quill = new Quill('#edit-deschiption-edit', {
    //     theme: 'snow' // Specify theme in configuration
        
    //   });
    // };


    $( "#btn_save" ).click(function() {
      let remove_data = {
        "_token": "{{ csrf_token() }}",
        'artist_id':"{{$artist->id}}",
        'name':$("#name").val(),
        'intro':quill.root.innerHTML,
        'link_twitter':$("#link_twitter").val(),
        'link_pixiv':$("#link_pixiv").val(),
        'link_bilibili':$("#link_bilibili").val(),
        'link_weibo':$("#link_weibo").val(),
      }
      
      $.ajax({
        method: "POST",
        url: "{{url('artist_center/artist/updateProfile')}}",
        data: remove_data,
        success: function(response) {
          if(response.code == 200 ){
            toastr.success(response.message, 'Success',{"positionClass": "toast-top-center",})
            location.reload();
          }
          else{
            toastr.error(response.message, 'Error',{"positionClass": "toast-top-center",})
          }

        }
      });

    });

    $( ".edit-avatar-icon" ).click(function() {
      $('#avatar-modal').modal('show')
      
      // let key = $(this).attr('image-key');
      // $("#edit_image").attr('src',"{{asset('storage')}}/" + images[key]['image_url']);
    })

    $("#choose_avatar").click(function(){
      document.getElementById('avatar_file').click();
    })
    



    $( "#save-avatar" ).click(function() {
      
      // toastr.error("文件不能为空",'Error',{"positionClass": "toast-top-center",})
      
    })


    $( ".edit-cover-icon" ).click(function() {
      $('#cover-modal').modal('show')
      // let key = $(this).attr('image-key');
      // $("#edit_image").attr('src',"{{asset('storage')}}/" + images[key]['image_url']);
    })

    const previewImage = (event) => {
      const imageFiles = event.target.files;
      
      const imageFilesLength = imageFiles.length;
    
      if (imageFilesLength > 0) {

          const imageSrc = URL.createObjectURL(imageFiles[0]);
          
          const imagePreviewElement = document.querySelector("#avatar_preview");
          
          imagePreviewElement.src = imageSrc;
          
      }
    };







  </script>
  {{-- <script type="module">
    import { createApp } from 'https://unpkg.com/vue@3/dist/vue.esm-browser.js'
    createApp({
      data() {
        return {
          artist_name:"{{$artist->name}}",
          link_twitter:"{{$artist->link_twitter}}",
          link_pixiv:"{{$artist->link_pixiv}}",
          link_bilibili:"{{$artist->link_bilibili}}",
          link_weibo:"{{$artist->link_weibo}}",

        }
      },
      methods:{
        saveInfo: function(){
          
        }
      },
      created(){
        
      }
    }).mount('#app')
  </script> --}}
 
</body>

</html>
