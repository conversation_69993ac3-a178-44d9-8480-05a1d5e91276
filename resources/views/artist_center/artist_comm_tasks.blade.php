<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <link rel="apple-touch-icon" sizes="76x76" href="../../../assets/img/apple-icon.png">
  <link rel="icon" type="image/png" href="../../../assets/img/favicon.png">
  <title>
    Pipipen
  </title>
  <!--     Fonts and icons     -->
  @include('artist_center/css')
</head>

<body class="g-sidenav-show bg-gray-200">
  @include('artist_center/sidebar')
  <main class="main-content position-relative border-radius-lg ">
    <!-- Navbar -->
    @include('artist_center/topnav')
    <!-- End Navbar -->
    
    <div class="container-fluid py-5" id="app">
      <div class="row mt-5 mb-5">
        <div class="col-lg-3">
          <div class="card position-sticky top-9">
            <ul class="nav flex-column bg-white border-radius-lg p-3">
              <li class="nav-item">
                <a class="nav-link text-dark d-flex" data-scroll="" href="">
                  <i class="material-icons text-lg me-2">badge</i>
                  <span class="text-sm">委托 新申请</span>
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link text-dark d-flex" data-scroll="" href="#">
                  <i class="material-icons text-lg me-2">receipt_long</i>
                  <span class="text-sm">委托 进行中</span>
                </a>
              </li>
             
              {{-- <li class="nav-item pt-2">
                <a class="nav-link text-dark d-flex" data-scroll="" href="">
                  <i class="material-icons text-lg me-2">receipt_long</i>
                  <span class="text-sm">Change Password</span>
                </a>
              </li> --}}
              {{-- <li class="nav-item pt-2">
                <a class="nav-link text-dark d-flex" data-scroll="" href="#password">
                  <i class="material-icons text-lg me-2">lock</i>
                  <span class="text-sm">Change Password</span>
                </a>
              </li>
              <li class="nav-item pt-2">
                <a class="nav-link text-dark d-flex" data-scroll="" href="#2fa">
                  <i class="material-icons text-lg me-2">security</i>
                  <span class="text-sm">2FA</span>
                </a>
              </li>
              <li class="nav-item pt-2">
                <a class="nav-link text-dark d-flex" data-scroll="" href="#accounts">
                  <i class="material-icons text-lg me-2">badge</i>
                  <span class="text-sm">Accounts</span>
                </a>
              </li>
              <li class="nav-item pt-2">
                <a class="nav-link text-dark d-flex" data-scroll="" href="#notifications">
                  <i class="material-icons text-lg me-2">campaign</i>
                  <span class="text-sm">Notifications</span>
                </a>
              </li>
              <li class="nav-item pt-2">
                <a class="nav-link text-dark d-flex" data-scroll="" href="#sessions">
                  <i class="material-icons text-lg me-2">settings_applications</i>
                  <span class="text-sm">Sessions</span>
                </a>
              </li>
              <li class="nav-item pt-2">
                <a class="nav-link text-dark d-flex" data-scroll="" href="#delete">
                  <i class="material-icons text-lg me-2">delete</i>
                  <span class="text-sm">Delete Account</span>
                </a>
              </li> --}}
            </ul>
          </div>
        </div>
        <div class="col-lg-9 mt-lg-0 mt-4">
          <!-- Card Basic Info -->
          <div class="card" id="">
            <div class="card-body mt-2">
              <div class="d-flex border-bottom w-100">
                <div class="d-flex ">
                  <h6>委托申请</h6>
                </div>
                {{-- <button type="button" class="btn btn-info btn-sm m-0 ms-auto" data-mdb-toggle="tooltip" title="Remove item" id="add-comms">
                  添加
                </button> --}}
              </div>
              
              <div class="row" id="">
                {{-- <p>查看</p> --}}
                @foreach($comm_tasks as $task)
                <div class="row mt-lg-4 mt-2">
                  <div class="col-12 mb-4">
                    <div class="card border">
                      <div class="card-body p-3">
                        <div class="d-flex">
                          <div class="">
                            <h5 class="">委托编号 #{{$task->id}}</h5>
                            <div class="text-sm">类型：{{$task->originalContent->title}}</div>
                            <div class="text-sm">截稿日：{{$task->deadline}}</div>
                          </div>

                          <div class="ms-auto">
                            <a href="{{url('/user/')}}" class="w-100">
                              <img class="shadow-8 border avatar avatar-lg rounded-circle" alt="artist avatar" src="{{asset('storage/'.$task->user->avatar)}}">
                              <div class="text-sm text-center opacity-5 mt-2">{{$task->user->name}}</div>
                            </a>
                          </div>
                        </div>
                       
                        

                        <h6 class="mt-4 mb-0">需求细节</h6>
                        <div class="text-sm mt-1 max-height-vh-20 overflow-auto border border-radius-lg p-2" style="white-space: pre-line">{{$task->request_detail_translation}}</div>
                        

                        <h6 class="mt-4 mb-0">参考例图</h6>
                        <div class="row" id="showcases">
                          @foreach($task->taskFiles()->get() as $request_file)
                          <div class="col-2 sort-card" request-file="{{$request_file->id}}">
                            <div class="card shadow-lg mb-3">
                              <img class="img-fluid border-radius-lg aspect-ratio-1 border work-image w-100" src="{{$request_file->url_lg}}" />      
                            </div>
                          </div>
                          @endforeach
                        </div>
                        
                        <div class="row">
                          <div class="col-6">
                            {{-- <h6 class="text-sm mb-0">5 个申请</h6> --}}
                            {{-- <p class="text-secondary text-sm font-weight-normal mb-0">个申请</p> --}}
                          </div>
                          <div class="col-6 text-end">
                            <a href="{{url('/artist_center/comm_task/'.$task->id)}}" class="btn btn-outline-info btn-sm m-0 me-2" data-mdb-toggle="tooltip" title="Edit">
                              <i class="fa-solid fa-gear"></i> 查看
                            </a>
                            {{-- <button type="button" class="btn btn-outline-danger btn-sm m-0 me-2" data-mdb-toggle="tooltip" title="Edit">
                              <i class="fas fa-trash"></i> 删除
                            </button> --}}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                @endforeach
                
              </div>
            </div>
          </div>
        
          
          
        </div>
      </div>
     
    </div>
  </main>

  <div class="modal fade modal-xl" id="new-comms" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
      <div class="modal-content max-height-vh-80">
        <div class="modal-header">
          <h5 class="modal-title" id="exampleModalLabel">添加新的接稿类型</h5>
          <button type="button" class="btn-close text-dark" data-bs-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body text-center show overflow-y-auto">
          <div class="row">
            <div class="col-6">
              <div class="border p-2">
                <img class="w-100" src="" id="edit_image">
              </div>
            </div>
            <div class="col-6">
              <div class="form-group text-start">
                <label class="text-start"> 标题 </label>
                <input type="email" class="form-control border border-radius-lg px-2" v-model="user_email" placeholder="标题">
              </div>
              <div class="form-group text-start mt-4 d-flex">
                <div class="w-75 me-2">
                  <label class="text-start"> 价格 </label>
                  <input type="number" class="form-control border border-radius-lg px-2 me-2" v-model="user_email" placeholder="" id="categories">
                </div>
                <div class="w-25">
                  <label class="text-start"> 结算货币 </label>
                  <select type="email" class="form-control border border-radius-lg px-2" v-model="user_email" placeholder="" id="categories">
                    <option>¥ 人民币</option>
                    {{-- <option>$ 美金</option> --}}
                  </select>
                </div>
                
                
              </div>
              <div class="form-group text-start mt-4">
                <label class="text-start"> 分类 </label>
                <input type="email" class="form-control border border-radius-lg px-2" v-model="user_email" placeholder="" id="categories">
                {{-- <div class="drop-down w-100 card">
                  <div class="card-body">
                    <div class="row">
                      <div class="col-6">
                        <div class="form-check ps-0 me-2">
                          <input class="form-check-input product-c" type="checkbox" id="product-c-" value="" v-model="checkedCategories" @change="check()">
                          <label class="custom-control-label ms-0 cursor-pointer ps-1" for="product-c-"> LIVE2D</label>
                        </div>
                      </div>
                      <div class="col-6">
                        <div class="form-check ps-0 me-2">
                          <input class="form-check-input product-c" type="checkbox" id="product-c-" value="" v-model="checkedCategories" @change="check()">
                          <label class="custom-control-label ms-0 cursor-pointer ps-1" for="product-c-"> LIVE2D</label>
                        </div>
                      </div>
                      <div class="col-6">
                        <div class="form-check ps-0 me-2">
                          <input class="form-check-input product-c" type="checkbox" id="product-c-" value="" v-model="checkedCategories" @change="check()">
                          <label class="custom-control-label ms-0 cursor-pointer ps-1" for="product-c-"> LIVE2D</label>
                        </div>
                      </div>
                      <div class="col-6">
                        <div class="form-check ps-0 me-2">
                          <input class="form-check-input product-c" type="checkbox" id="product-c-" value="" v-model="checkedCategories" @change="check()">
                          <label class="custom-control-label ms-0 cursor-pointer ps-1" for="product-c-"> LIVE2D</label>
                        </div>
                      </div>
                      <div class="col-6">
                        <div class="form-check ps-0 me-2">
                          <input class="form-check-input product-c" type="checkbox" id="product-c-" value="" v-model="checkedCategories" @change="check()">
                          <label class="custom-control-label ms-0 cursor-pointer ps-1" for="product-c-"> LIVE2D</label>
                        </div>
                      </div>
                      <div class="col-3"></div>
                      <div class="col-3"></div>
                    </div>
                  </div>
                  
                </div> --}}
              </div>

              {{-- <div class="form-group text-start mt-4">
                <label class="text-start"> 详情 </label>
                <input type="email" class="form-control border border-radius-lg px-2" v-model="user_email" placeholder="输入自定义标签后按回车" id="tags">
                <div class="drop-down w-100 card">
                  <div class="card-body">
                    <div class="row">
                      <div class="col-6">
                        <div class="form-check ps-0 me-2">
                          <input class="form-check-input product-c" type="checkbox" id="product-c-" value="" v-model="checkedCategories" @change="check()">
                          <label class="custom-control-label ms-0 cursor-pointer ps-1" for="product-c-"> LIVE2D</label>
                        </div>
                      </div>
                      <div class="col-6">
                        <div class="form-check ps-0 me-2">
                          <input class="form-check-input product-c" type="checkbox" id="product-c-" value="" v-model="checkedCategories" @change="check()">
                          <label class="custom-control-label ms-0 cursor-pointer ps-1" for="product-c-"> LIVE2D</label>
                        </div>
                      </div>
                      <div class="col-6">
                        <div class="form-check ps-0 me-2">
                          <input class="form-check-input product-c" type="checkbox" id="product-c-" value="" v-model="checkedCategories" @change="check()">
                          <label class="custom-control-label ms-0 cursor-pointer ps-1" for="product-c-"> LIVE2D</label>
                        </div>
                      </div>
                      <div class="col-6">
                        <div class="form-check ps-0 me-2">
                          <input class="form-check-input product-c" type="checkbox" id="product-c-" value="" v-model="checkedCategories" @change="check()">
                          <label class="custom-control-label ms-0 cursor-pointer ps-1" for="product-c-"> LIVE2D</label>
                        </div>
                      </div>
                      <div class="col-6">
                        <div class="form-check ps-0 me-2">
                          <input class="form-check-input product-c" type="checkbox" id="product-c-" value="" v-model="checkedCategories" @change="check()">
                          <label class="custom-control-label ms-0 cursor-pointer ps-1" for="product-c-"> LIVE2D</label>
                        </div>
                      </div>
                      <div class="col-3"></div>
                      <div class="col-3"></div>
                    </div>
                  </div>
                  
                </div>
              </div> --}}
              <div class="form-group text-start mt-4">
                <label class="text-start"> 详情 </label>
                <div class="">
                  <div id="edit-deschiption-edit" style="min-height:200px">
                    
                  </div>
                </div>
              </div>

              <div class="form-group text-start mt-4">
                <label class="text-start"> 使用表单 </label>
                <select type="email" class="form-control border border-radius-lg px-2" v-model="user_email" placeholder="" id="categories">
                  <option>L2D表单</option>
                </select>
                
              </div>

              {{-- <div class="mt-4">
                <div class="form-check form-switch d-flex align-items-center mb-4">
                  <input class="form-check-input" type="checkbox" id="rememberMe" checked>
                  <label class="form-check-label mb-0 ms-3 text-gray" for="rememberMe">展示</label>
                </div>
              </div> --}}

              {{-- <div class="mt-4">
                <div class="form-check form-switch d-flex align-items-center mb-4">
                  <input class="form-check-input" type="checkbox" id="rememberMe" checked>
                  <label class="form-check-label mb-0 ms-3 text-gray" for="rememberMe">水印</label>
                </div>
              </div> --}}
            </div>
          </div>
          
        </div>
        <div class="modal-footer">
          <button type="button" class="btn bg-gradient-dark btn-sm" data-bs-dismiss="modal">取消</button>
          <button type="button" class="btn bg-gradient-info btn-sm" id="confirm">添加</button>
        </div>
      </div>
    </div>
  </div>
  
  <!--   Core JS Files   -->
  @include('artist_center/js')
  {{-- <script src="{{asset('dashboard/js/plugins/quill.min.js')}}"></script>
  <script>
    $( "#add-comms" ).click(function() {
      $('#new-comms').modal('show')
    })

    if (document.getElementById('edit-deschiption-edit')) {
      var quill = new Quill('#edit-deschiption-edit', {
        theme: 'snow' // Specify theme in configuration
        
      });
    };
  </script> --}}
</body>

</html>
