<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <link rel="apple-touch-icon" sizes="76x76" href="../../../assets/img/apple-icon.png">
  <link rel="icon" type="image/png" href="../../../assets/img/favicon.png">
  <title>
    Pipipen
  </title>
  <!--     Fonts and icons     -->
  @include('artist_center/css')
</head>

<body class="g-sidenav-show bg-gray-200">
  @include('artist_center/sidebar')
  <main class="main-content position-relative border-radius-lg ">
    <!-- Navbar -->
    @include('artist_center/topnav')
    <!-- End Navbar -->
    <div class="container-fluid py-5" id="app">
      <div class="row mt-5">
        <div class="col-lg-3">
          <div class="card position-sticky top-9">
            <ul class="nav flex-column bg-white border-radius-lg p-3">
              <li class="nav-item">
                <a class="nav-link text-dark d-flex" data-scroll="" href="{{url('/artist_center/comm_task/'.$comm_task->id)}}">
                  <i class="material-icons text-lg me-2">badge</i>
                  <span class="text-sm">委托详情</span>
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link text-dark d-flex" data-scroll="" href="{{url('/artist_center/comm_task_chat/'.$comm_task->id)}}">
                  <i class="material-icons text-lg me-2">receipt_long</i>
                  <span class="text-sm">消息中心</span>
                </a>
              </li>
             
              {{-- <li class="nav-item pt-2">
                <a class="nav-link text-dark d-flex" data-scroll="" href="">
                  <i class="material-icons text-lg me-2">receipt_long</i>
                  <span class="text-sm">Change Password</span>
                </a>
              </li> --}}
              {{-- <li class="nav-item pt-2">
                <a class="nav-link text-dark d-flex" data-scroll="" href="#password">
                  <i class="material-icons text-lg me-2">lock</i>
                  <span class="text-sm">Change Password</span>
                </a>
              </li>
              <li class="nav-item pt-2">
                <a class="nav-link text-dark d-flex" data-scroll="" href="#2fa">
                  <i class="material-icons text-lg me-2">security</i>
                  <span class="text-sm">2FA</span>
                </a>
              </li>
              <li class="nav-item pt-2">
                <a class="nav-link text-dark d-flex" data-scroll="" href="#accounts">
                  <i class="material-icons text-lg me-2">badge</i>
                  <span class="text-sm">Accounts</span>
                </a>
              </li>
              <li class="nav-item pt-2">
                <a class="nav-link text-dark d-flex" data-scroll="" href="#notifications">
                  <i class="material-icons text-lg me-2">campaign</i>
                  <span class="text-sm">Notifications</span>
                </a>
              </li>
              <li class="nav-item pt-2">
                <a class="nav-link text-dark d-flex" data-scroll="" href="#sessions">
                  <i class="material-icons text-lg me-2">settings_applications</i>
                  <span class="text-sm">Sessions</span>
                </a>
              </li>
              <li class="nav-item pt-2">
                <a class="nav-link text-dark d-flex" data-scroll="" href="#delete">
                  <i class="material-icons text-lg me-2">delete</i>
                  <span class="text-sm">Delete Account</span>
                </a>
              </li> --}}
            </ul>
          </div>
        </div>
        <div class="col-lg-9 mt-lg-0 mt-4">
          <!-- Card Basic Info -->
          <div class="" id="">
            <div class="row">
              <div class="col-lg-8">
                <div class="card shadow-blur max-height-vh-90 border">
                  <div class="card-header p-0 position-relative z-index-2 bg-transparent">
                    <div class="bg-gradient-info shadow-info border-radius-lg p-3">
                      <div class="row">
                        <div class="col-md-6 col-lg-6">
                          <div class="d-flex align-items-center">
                            <div class="ms-3">
                              <h6 class="mb-0 d-block text-white">消息中心</h6>
                              <span class="text-sm text-white opacity-8">委托编号 #@{{comm_task_id}}</span>
                            </div>
                          </div>
                        </div>
                        <div class="col-6 my-auto text-end">
                          <div class="avatar-group">
                            <a href="javascript:;" class="avatar avatar-lg rounded-circle" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Ryan Tompson">
                              <img alt="Image placeholder" class="bg-blur bg-gradient-success aspect-ratio-1" src="{{asset('image/bg2.jpg')}}">
                            </a>
                            <a href="javascript:;" class="avatar avatar-lg rounded-circle" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Romina Hadid">
                              <img alt="Image placeholder" class="bg-blur bg-gradient-success" src="{{asset('image/pia_emote/cool.png')}}">
                            </a>
                            {{-- <a href="javascript:;" class="avatar avatar-lg rounded-circle" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Alexander Smith">
                              <img alt="Image placeholder" class="bg-blur bg-gradient-success" src="{{asset('image/pia_emote/cool.png')}}">
                            </a>
                            <a href="javascript:;" class="avatar avatar-lg rounded-circle" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Jessica Doe">
                              <img alt="Image placeholder" class="bg-blur bg-gradient-success" src="{{asset('image/pia_emote/cool.png')}}">
                            </a> --}}
                          </div>
                          
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="card-body overflow-auto overflow-x-hidden" id="chat-body">
                    <div v-for="msg in messages">
                      <div class="d-flex justify-content-end text-right align-items-center mb-4" v-if="msg.from_id == this.user_id" >
                        <div class="me-3">
                          <div class="card bg-gradient-info">
                            <div class="card-body py-2 px-3 text-white">
                              <p class="mb-1">
                                @{{msg.body}}
                              </p>
                              <hr class="horizontal light">
                              <p class="mb-1">
                               
                              </p>
                              {{-- <div class="d-flex align-items-center justify-content-end text-sm opacity-6">
                                <i class="ni ni-check-bold text-sm me-1"></i>
                                <small>4:42pm</small>
                              </div> --}}
                            </div>
                          </div>
                        </div>
                        <div class="">
                          <a href="javascript:;" class="avatar avatar-lg rounded-circle" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Ryan Tompson">
                            <img alt="Image placeholder" class="bg-blur bg-gradient-success aspect-ratio-1" src="{{asset('image/bg2.jpg')}}">
                          </a>
                        </div>
                      </div>
                      <div class="d-flex justify-content-start align-items-center mb-4" v-else>
                        <div class="me-3 d-flex">
                          <a href="javascript:;" class="avatar avatar-lg rounded-circle" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Ryan Tompson">
                            <img alt="Image placeholder" class="bg-blur bg-gradient-success aspect-ratio-1" src="{{asset('image/bg2.jpg')}}">
                          </a>
                        </div>
                        <div class="">
                          <div class="card ">
                            <div class="card-body py-2 px-3">
                              <p class="mb-1">
                                @{{msg.body}}
                              </p>
                              {{-- <div class="d-flex align-items-center text-sm opacity-6">
                                <i class="ni ni-check-bold text-sm me-1"></i>
                                <small>3:14am</small>
                              </div> --}}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    {{-- <div class="d-flex justify-content-start align-items-center mb-4">
                      <div class="me-3 d-flex">
                        <a href="javascript:;" class="avatar avatar-lg rounded-circle" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Ryan Tompson">
                          <img alt="Image placeholder" class="bg-blur bg-gradient-success aspect-ratio-1" src="{{asset('image/bg2.jpg')}}">
                        </a>
                      </div>
                      <div class="">
                        <div class="card ">
                          <div class="card-body py-2 px-3">
                            <p class="mb-1">
                               asdas dasd as as dasd asd asd asd asd as dasd asd as dasdasd asd asd
                            </p>
                            <div class="d-flex align-items-center text-sm opacity-6">
                              <i class="ni ni-check-bold text-sm me-1"></i>
                              <small>3:14am</small>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="d-flex justify-content-end text-right align-items-center mb-4">
                      <div class="me-3">
                        <div class="card bg-gradient-primary">
                          <div class="card-body py-2 px-3 text-white">
                            <p class="mb-1">
                              Can it generate daily design links that include essays and data visualizations ?<br>
                            </p>
                            <div class="d-flex align-items-center justify-content-end text-sm opacity-6">
                              <i class="ni ni-check-bold text-sm me-1"></i>
                              <small>4:42pm</small>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="">
                        <a href="javascript:;" class="avatar avatar-lg rounded-circle" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Ryan Tompson">
                          <img alt="Image placeholder" class="bg-blur bg-gradient-success aspect-ratio-1" src="{{asset('image/bg2.jpg')}}">
                        </a>
                      </div>
                    </div> --}}
                    {{-- <div class="row mt-4">
                      <div class="col-md-12 text-center">
                        <span class="badge text-dark">Wed, 3:27pm</span>
                      </div>
                    </div>
                    <div class="row justify-content-start mb-4">
                      <div class="col-auto">
                        <div class="card ">
                          <div class="card-body py-2 px-3">
                            <p class="mb-1">
                              Yeah! Responsive Design is geared towards those trying to build web apps
                            </p>
                            <div class="d-flex align-items-center text-sm opacity-6">
                              <i class="ni ni-check-bold text-sm me-1"></i>
                              <small>4:31pm</small>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="row justify-content-end text-right mb-4">
                      <div class="col-auto">
                        <div class="card bg-gradient-primary">
                          <div class="card-body py-2 px-3 text-white">
                            <p class="mb-1">
                              Excellent, I want it now !
                            </p>
                            <div class="d-flex align-items-center justify-content-end text-sm opacity-6">
                              <i class="ni ni-check-bold text-sm me-1"></i>
                              <small>4:42pm</small>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="row justify-content-start mb-4">
                      <div class="col-auto">
                        <div class="card ">
                          <div class="card-body py-2 px-3">
                            <p class="mb-1">
                              You can easily get it; The content here is all free
                            </p>
                            <div class="d-flex align-items-center text-sm opacity-6">
                              <i class="ni ni-check-bold text-sm me-1"></i>
                              <small>4:42pm</small>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="row justify-content-end text-right mb-4">
                      <div class="col-auto">
                        <div class="card bg-gradient-primary">
                          <div class="card-body py-2 px-3 text-white">
                            <p class="mb-1">
                              Awesome, blog is important source material for anyone who creates apps? <br>
                              Beacuse these blogs offer a lot of information about website development.
                            </p>
                            <div class="d-flex align-items-center justify-content-end text-sm opacity-6">
                              <i class="ni ni-check-bold text-sm me-1"></i>
                              <small>4:42pm</small>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="row justify-content-start mb-4">
                      <div class="col-5">
                        <div class="card ">
                          <div class="card-body p-2">
                            <div class="col-12 p-0">
                              <img src="https://images.unsplash.com/photo-1547949003-9792a18a2601?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&q=80" alt="Rounded image" class="img-fluid mb-2 border-radius-lg">
                            </div>
                            <div class="d-flex align-items-center text-sm opacity-6">
                              <i class="ni ni-check-bold text-sm me-1"></i>
                              <small>4:47pm</small>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="row justify-content-end text-right mb-4">
                      <div class="col-auto">
                        <div class="card bg-gradient-primary">
                          <div class="card-body py-2 px-3 text-white">
                            <p class="mb-0">
                              At the end of the day … the native dev apps is where users are
                            </p>
                            <div class="d-flex align-items-center justify-content-end text-sm opacity-6">
                              <i class="ni ni-check-bold text-sm me-1"></i>
                              <small>4:42pm</small>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="row justify-content-start">
                      <div class="col-auto">
                        <div class="card ">
                          <div class="card-body py-2 px-3">
                            <p class="mb-0">
                              Charlie is Typing...
                            </p>
                          </div>
                        </div>
                      </div>
                    </div> --}}
                  </div>
                  <div class="card-footer d-block">
                    <form class="align-items-center">
                      <div class="input-group input-group-outline d-flex">
                        {{-- <input type="text" class="form-control form-control-lg"> --}}
                        <textarea class="form-control form-control-lg over-flow-hidden chat-input" rows="1" v-model="message"  @keydown.enter="addMessage()"> </textarea>
                        <button class="btn bg-gradient-info mb-0">
                          <i class="material-icons">send</i>
                        </button>
                      </div>
                    </form>
                  </div>
                </div>
              </div>
              <div class="col-md-5 col-lg-4">
                <div class="card blur shadow-blur max-height-vh-90 overflow-auto overflow-x-hidden mb-lg-0">
                  <div class="card-header border-bottom">
                    <h6>群聊用户</h6>
                  </div>
                  <div class="card-body p-2">
                    <a href="javascript:;" class="d-block p-2">
                      <div class="d-flex p-2">
                        <img alt="Image" src="../../../assets/img/team-1.jpg" class="avatar avatar-xl shadow">
                        <div class="ms-3">
                          <h6 class="mb-0">Jane Doe</h6>
                          <p class="text-muted text-xs mb-2">1 hour ago</p>
                          <span class="text-muted text-sm col-11 p-0 text-truncate d-block">Computer users and programmers</span>
                        </div>
                      </div>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        
          
          
        </div>
      </div>
     
    </div>
    
  </main>

  
  
  <!--   Core JS Files   -->
  @include('artist_center/js')
  <script src="https://js.pusher.com/8.0.1/pusher.min.js"></script>
  <script type="module">
    Pusher.logToConsole = true; 
    const pusher = new Pusher('0a912ab23e09bfefc652', {
      cluster: 'us2',
      encrypted: true,
      authEndpoint: "{{url('/dashboard/pusher_auth')}}",
      auth: {
              params: {
                "_token":"{{ csrf_token() }}"
              },
            }
    });
    import { createApp } from 'https://unpkg.com/vue@3/dist/vue.esm-browser.js'
    createApp({
      data() {
        return {
          messages: [],
          user_id:"{{Auth::user()->id}}",
          message:"",
          comm_task_id:"",
          comm_task:{!! json_encode($comm_task) !!}
        }
      },
      methods:{
        joinChat(comm_task_id){
          let channel = pusher.subscribe("private-chat-"+comm_task_id);

          channel.bind("pusher:subscription_succeeded", () => {
            channel.bind("sent", (data) => {
              if(data.message.from_id != this.user_id){
                this.messages.push(data.message);
                $("#chat-body").stop().animate({scrollTop: 999999});
              }
            });
          });
          this.comm_task_id = comm_task_id
          this.fetchMessages();
          $("#chat-body").stop().animate({scrollTop: 999999});
        },
        fetchMessages() {
          let data = {
            'comm_task_id':this.comm_task_id
          }
          $.ajax({
            method: "GET",
            url: "{{url('dashboard/fetch_message')}}",
            data: data,
            success: response => {
              this.messages = response.data;
            }
          });

          // $("#chat-body").stop().animate({scrollTop: 9999999});
        },
        //Receives the message that was emitted from the ChatForm Vue component
        addMessage() {
          let message_obj = {
            '_token':"{{ csrf_token() }}",
            'from_id': this.user_id,
            'comm_task_id':this.comm_task_id,
            'body':this.message,
          }
          
          this.messages.push(message_obj);
          let chat_body = document.getElementById("chat-body");
          $("#chat-body").stop().animate({scrollTop: 999999});
          $.ajax({
            method: "POST",
            url: "{{url('dashboard/send_message')}}",
            data: message_obj,
            success: response => {
              if(!response.error){
                
              }
            }
          });
        },
        scrollToBottom(container) {
          $(container)
            .stop()
            .animate({
              scrollTop: $(container)[0].scrollHeight,
            });
        }
      },
      created(){
        
      },
      mounted(){
        this.joinChat(this.comm_task.id)
        $("#chat-body").stop().animate({scrollTop: 9999999});
      },
      beforeDestroy() { 
        
      }
    }).mount('#app')

      //app and el already exists.
      // const app = new Vue({
      //     el: '#app',
      //     //Store chat messages for display in this array.
      //     data: {
      //         messages: []
      //     },
      //     //Upon initialisation, run fetchMessages(). 
      //     created() {
      //         this.fetchMessages();
      //     },
      //     methods: {
      //         fetchMessages() {
      //             //GET request to the messages route in our Laravel server to fetch all the messages
      //             axios.get('/messages').then(response => {
      //                 //Save the response in the messages array to display on the chat view
      //                 this.messages = response.data;
      //             });
      //         },
      //         //Receives the message that was emitted from the ChatForm Vue component
      //         addMessage(message) {
      //             //Pushes it to the messages array
      //             this.messages.push(message);
      //             //POST request to the messages route with the message data in order for our Laravel server to broadcast it.
      //             axios.post('/messages', message).then(response => {
      //                 console.log(response.data);
      //             });
      //         }
      //     }
      // });

  </script>
  {{-- <script src="{{asset('dashboard/js/plugins/quill.min.js')}}"></script>
  <script>
    $( "#add-comms" ).click(function() {
      $('#new-comms').modal('show')
    })

    if (document.getElementById('edit-deschiption-edit')) {
      var quill = new Quill('#edit-deschiption-edit', {
        theme: 'snow' // Specify theme in configuration
        
      });
    };
  </script> --}}
</body>

</html>
