<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <link rel="apple-touch-icon" sizes="76x76" href="../../../assets/img/apple-icon.png">
  <link rel="icon" type="image/png" href="../../../assets/img/favicon.png">
  <title>
    ESS Dashboard
  </title>
  <!--     Fonts and icons     -->
  @include('dashboard/css')
</head>

<body class="g-sidenav-show bg-gray-200">
  @include('dashboard/sidebar')
  <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg ">
    <!-- Navbar -->
    <nav class="navbar navbar-main navbar-expand-lg position-sticky mt-4 top-1 px-0 mx-4 shadow-none border-radius-xl z-index-sticky" id="navbarBlur" data-scroll="true">
      <div class="container-fluid py-1 px-3">
        <nav aria-label="breadcrumb">
          <ol class="breadcrumb bg-transparent mb-0 pb-0 pt-1 px-0 me-sm-6 me-5">
            <li class="breadcrumb-item text-sm">
              <a class="opacity-3 text-dark" href="javascript:;">
                <svg width="12px" height="12px" class="mb-1" viewBox="0 0 45 40" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                  <title>shop </title>
                  <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                    <g transform="translate(-1716.000000, -439.000000)" fill="#252f40" fill-rule="nonzero">
                      <g transform="translate(1716.000000, 291.000000)">
                        <g transform="translate(0.000000, 148.000000)">
                          <path d="M46.7199583,10.7414583 L40.8449583,0.949791667 C40.4909749,0.360605034 39.8540131,0 39.1666667,0 L7.83333333,0 C7.1459869,0 6.50902508,0.360605034 6.15504167,0.949791667 L0.280041667,10.7414583 C0.0969176761,11.0460037 -1.23209662e-05,11.3946378 -1.23209662e-05,11.75 C-0.00758042603,16.0663731 3.48367543,19.5725301 7.80004167,19.5833333 L7.81570833,19.5833333 C9.75003686,19.5882688 11.6168794,18.8726691 13.0522917,17.5760417 C16.0171492,20.2556967 20.5292675,20.2556967 23.494125,17.5760417 C26.4604562,20.2616016 30.9794188,20.2616016 33.94575,17.5760417 C36.2421905,19.6477597 39.5441143,20.1708521 42.3684437,18.9103691 C45.1927731,17.649886 47.0084685,14.8428276 47.0000295,11.75 C47.0000295,11.3946378 46.9030823,11.0460037 46.7199583,10.7414583 Z"></path>
                          <path d="M39.198,22.4912623 C37.3776246,22.4928106 35.5817531,22.0149171 33.951625,21.0951667 L33.92225,21.1107282 C31.1430221,22.6838032 27.9255001,22.9318916 24.9844167,21.7998837 C24.4750389,21.605469 23.9777983,21.3722567 23.4960833,21.1018359 L23.4745417,21.1129513 C20.6961809,22.6871153 17.4786145,22.9344611 14.5386667,21.7998837 C14.029926,21.6054643 13.533337,21.3722507 13.0522917,21.1018359 C11.4250962,22.0190609 9.63246555,22.4947009 7.81570833,22.4912623 C7.16510551,22.4842162 6.51607673,22.4173045 5.875,22.2911849 L5.875,44.7220845 C5.875,45.9498589 6.7517757,46.9451667 7.83333333,46.9451667 L19.5833333,46.9451667 L19.5833333,33.6066734 L27.4166667,33.6066734 L27.4166667,46.9451667 L39.1666667,46.9451667 C40.2482243,46.9451667 41.125,45.9498589 41.125,44.7220845 L41.125,22.2822926 C40.4887822,22.4116582 39.8442868,22.4815492 39.198,22.4912623 Z"></path>
                        </g>
                      </g>
                    </g>
                  </g>
                </svg>
              </a>
            </li>
            <li class="breadcrumb-item text-sm"><a class="opacity-5 text-dark" href="javascript:;">Dashboard</a></li>
            <li class="breadcrumb-item text-sm text-dark active" aria-current="page">Orders</li>
          </ol>
          <h6 class="font-weight-bolder mb-0">My Orders</h6>
        </nav>
        <div class="sidenav-toggler sidenav-toggler-inner d-xl-block d-none ">
          <a href="javascript:;" class="nav-link text-body p-0">
            <div class="sidenav-toggler-inner">
              <i class="sidenav-toggler-line"></i>
              <i class="sidenav-toggler-line"></i>
              <i class="sidenav-toggler-line"></i>
            </div>
          </a>
        </div>
        {{-- <div class="collapse navbar-collapse mt-sm-0 mt-2 me-md-0 me-sm-4" id="navbar">
          <div class="ms-md-auto pe-md-3 d-flex align-items-center">
            <div class="input-group input-group-outline">
              <label class="form-label">Search here</label>
              <input type="text" class="form-control">
            </div>
          </div>
          <ul class="navbar-nav  justify-content-end">
            <li class="nav-item">
              <a href="../../../pages/authentication/signin/illustration.html" class="nav-link text-body p-0 position-relative" target="_blank">
                <i class="material-icons me-sm-1">
              account_circle
            </i>
              </a>
            </li>
            <li class="nav-item d-xl-none ps-3 d-flex align-items-center">
              <a href="javascript:;" class="nav-link text-body p-0" id="iconNavbarSidenav">
                <div class="sidenav-toggler-inner">
                  <i class="sidenav-toggler-line"></i>
                  <i class="sidenav-toggler-line"></i>
                  <i class="sidenav-toggler-line"></i>
                </div>
              </a>
            </li>
            <li class="nav-item px-3">
              <a href="javascript:;" class="nav-link text-body p-0">
                <i class="material-icons fixed-plugin-button-nav cursor-pointer">
              settings
            </i>
              </a>
            </li>
            <li class="nav-item dropdown pe-2">
              <a href="javascript:;" class="nav-link text-body p-0 position-relative" id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="material-icons cursor-pointer">
              notifications
            </i>
                <span class="position-absolute top-5 start-100 translate-middle badge rounded-pill bg-danger border border-white small py-1 px-2">
                  <span class="small">11</span>
                  <span class="visually-hidden">unread notifications</span>
                </span>
              </a>
              <ul class="dropdown-menu dropdown-menu-end p-2 me-sm-n4" aria-labelledby="dropdownMenuButton">
                <li class="mb-2">
                  <a class="dropdown-item border-radius-md" href="javascript:;">
                    <div class="d-flex align-items-center py-1">
                      <span class="material-icons">email</span>
                      <div class="ms-2">
                        <h6 class="text-sm font-weight-normal my-auto">
                          Check new messages
                        </h6>
                      </div>
                    </div>
                  </a>
                </li>
                <li class="mb-2">
                  <a class="dropdown-item border-radius-md" href="javascript:;">
                    <div class="d-flex align-items-center py-1">
                      <span class="material-icons">podcasts</span>
                      <div class="ms-2">
                        <h6 class="text-sm font-weight-normal my-auto">
                          Manage podcast session
                        </h6>
                      </div>
                    </div>
                  </a>
                </li>
                <li>
                  <a class="dropdown-item border-radius-md" href="javascript:;">
                    <div class="d-flex align-items-center py-1">
                      <span class="material-icons">shopping_cart</span>
                      <div class="ms-2">
                        <h6 class="text-sm font-weight-normal my-auto">
                          Payment successfully completed
                        </h6>
                      </div>
                    </div>
                  </a>
                </li>
              </ul>
            </li>
          </ul>
        </div> --}}
      </div>
    </nav>
    <!-- End Navbar -->
    <div class="container-fluid px-2 px-md-4" id="app">
      <div class="card card-body mx-3 mx-md-4 mt-4">
        <div class="row gx-4 mb-2">
          <div class="col-auto">
            <div class="avatar avatar-xl position-relative">
              <img src="{{asset('storage/').'/'.Auth::user()->avatar}}" alt="profile_image" class="w-100 border-radius-lg shadow-sm">
            </div>
          </div>
          <div class="col-auto my-auto">
            <div class="h-100">
              <h5 class="mb-1">
                {{Auth::user()->name}}
              </h5>
              <p class="mb-0 font-weight-normal text-sm">
                {{Auth::user()->email}}
              </p>
            </div>
          </div>
          <div class="col-lg-4 col-md-6 my-sm-auto ms-sm-auto me-sm-0 mx-auto mt-3">
            <div class="nav-wrapper position-relative end-0">
              <ul class="nav nav-pills nav-fill p-1" role="tablist">
                <li class="nav-item">
                  <a class="nav-link mb-0 px-0 py-1 active " data-bs-toggle="tab" href="javascript:;" role="tab" aria-selected="true">
                    <i class="material-icons text-lg position-relative">home</i>
                    <span class="ms-1">App</span>
                  </a>
                </li>
                <li class="nav-item">
                  <a class="nav-link mb-0 px-0 py-1 " data-bs-toggle="tab" href="javascript:;" role="tab" aria-selected="false">
                    <i class="material-icons text-lg position-relative">email</i>
                    <span class="ms-1">Messages</span>
                  </a>
                </li>
                <li class="nav-item">
                  <a class="nav-link mb-0 px-0 py-1 " data-bs-toggle="tab" href="javascript:;" role="tab" aria-selected="false">
                    <i class="material-icons text-lg position-relative">settings</i>
                    <span class="ms-1">Settings</span>
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="row mt-4">
            <div class="col-md-4 col-lg-4">
              <div class="card blur shadow-blur max-height-vh-70 overflow-auto overflow-x-hidden mb-5 mb-lg-0">
                {{-- <div class="card-header">
                  <h6>Friends</h6>
                  <div class="input-group input-group-outline">
                    <label class="form-label">Search contact</label>
                    <input type="text" class="form-control">
                  </div>
                </div> --}}
                <div class="card-body p-2" >
                  <div v-for="comm in comms">
                    <div class="d-block p-2 cursor-pointer border border-radius-lg bg-gradient-info my-2" v-if="comm.id == this.com_id">
                      <div class="d-flex p-2" @click="joinChat(comm.id)">
                        {{-- <img alt="Image" src="../../../assets/img/team-2.jpg" class="avatar shadow"> --}}
                        <div class="ms-3">
                          <div class="justify-content-between align-items-center">
                            <h6 class="mb-0  text-white">Commission #@{{comm.id}}
                              <span class="badge badge-success"></span>
                            </h6>
                            <p class=" text-white mb-0 text-xs">2 people in Chat</p>
                            <span class=" text-white text-sm col-11 p-0 text-truncate d-block">Computer users and programmers</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="d-block p-2 cursor-pointer border border-radius-lg my-2" v-else>
                      <div class="d-flex p-2" @click="joinChat(comm.id)">
                        {{-- <img alt="Image" src="../../../assets/img/team-2.jpg" class="avatar shadow"> --}}
                        <div class="ms-3">
                          <div class="justify-content-between align-items-center">
                            <h6 class="mb-0">Commission #@{{comm.id}}
                              <span class="badge badge-success"></span>
                            </h6>
                            <p class="text-muted mb-0 text-xs">2 people in Chat</p>
                            <span class="text-muted text-sm col-11 p-0 text-truncate d-block">Computer users and programmers</span>
                          </div>
                        </div>
                      </div>
                    </div>

                  </div>
                  
                  
                  {{-- <a href="javascript:;" class="d-block p-2 border-radius-lg bg-gradient-info">
                    <div class="d-flex p-2">
                      <img alt="Image" src="../../../assets/img/team-3.jpg" class="avatar shadow">
                      <div class="ms-3">
                        <h6 class="mb-0">Mila Skylar</h6>
                        <p class="text-muted text-xs mb-2">24 min ago</p>
                        <span class="text-muted text-sm col-11 p-0 text-truncate d-block">You can subscribe to receive weekly...</span>
                      </div>
                    </div>
                  </a> --}}
                  
                </div>
              </div>
            </div>
            <div class="col-md-6 col-lg-8">
              <div class="card blur shadow-blur max-height-vh-70">
                <div class="card-header p-0 position-relative mt-n4 mx-3 z-index-2 bg-transparent">
                  <div class="bg-gradient-info shadow-info border-radius-lg p-3">
                    <div class="row">
                      <div class="col-md-6 col-lg-6">
                        <div class="d-flex align-items-center">
                          <div class="ms-3">
                            <h6 class="mb-0 d-block text-white">Commission #@{{com_id}}</h6>
                            <span class="text-sm text-white opacity-8">last seen today at 1:53am</span>
                          </div>
                        </div>
                      </div>
                      <div class="col-6 my-auto text-end">
                        <div class="avatar-group">
                          <a href="javascript:;" class="avatar avatar-lg rounded-circle" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Ryan Tompson">
                            <img alt="Image placeholder" class="bg-blur bg-gradient-success aspect-ratio-1" src="{{asset('image/bg2.jpg')}}">
                          </a>
                          <a href="javascript:;" class="avatar avatar-lg rounded-circle" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Romina Hadid">
                            <img alt="Image placeholder" class="bg-blur bg-gradient-success" src="{{asset('image/pia_emote/cool.png')}}">
                          </a>
                          {{-- <a href="javascript:;" class="avatar avatar-lg rounded-circle" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Alexander Smith">
                            <img alt="Image placeholder" class="bg-blur bg-gradient-success" src="{{asset('image/pia_emote/cool.png')}}">
                          </a>
                          <a href="javascript:;" class="avatar avatar-lg rounded-circle" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Jessica Doe">
                            <img alt="Image placeholder" class="bg-blur bg-gradient-success" src="{{asset('image/pia_emote/cool.png')}}">
                          </a> --}}
                        </div>
                        
                      </div>
                      {{-- <div class="col-1 my-auto">
                        <div class="dropdown">
                          <button class="btn btn-icon-only text-white mb-0" type="button" data-bs-toggle="dropdown">
                            <i class="material-icons">settings</i>
                          </button>
                          <ul class="dropdown-menu dropdown-menu-end me-sm-n2 p-2" aria-labelledby="chatmsg">
                            <li>
                              <a class="dropdown-item border-radius-md" href="javascript:;">
                                Profile
                              </a>
                            </li>
                            <li>
                              <a class="dropdown-item border-radius-md" href="javascript:;">
                                Mute conversation
                              </a>
                            </li>
                            <li>
                              <a class="dropdown-item border-radius-md" href="javascript:;">
                                Block
                              </a>
                            </li>
                            <li>
                              <a class="dropdown-item border-radius-md" href="javascript:;">
                                Clear chat
                              </a>
                            </li>
                            <li>
                              <hr class="dropdown-divider">
                            </li>
                            <li>
                              <a class="dropdown-item border-radius-md text-danger" href="javascript:;">
                                Delete chat
                              </a>
                            </li>
                          </ul>
                        </div>
                      </div> --}}
                    </div>
                  </div>
                </div>
                <div class="card-body overflow-auto overflow-x-hidden" id="chat-body">
                  <div v-for="msg in messages">
                    <div class="d-flex justify-content-end text-right align-items-center mb-4" v-if="msg.from_id == this.user_id" >
                      <div class="me-3">
                        <div class="card bg-gradient-info">
                          <div class="card-body py-2 px-3 text-white">
                            <p class="mb-1">
                              @{{msg.body}}
                            </p>
                            <hr class="horizontal light">
                            <p class="mb-1">
                             
                            </p>
                            {{-- <div class="d-flex align-items-center justify-content-end text-sm opacity-6">
                              <i class="ni ni-check-bold text-sm me-1"></i>
                              <small>4:42pm</small>
                            </div> --}}
                          </div>
                        </div>
                      </div>
                      <div class="">
                        <a href="javascript:;" class="avatar avatar-lg rounded-circle" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Ryan Tompson">
                          <img alt="Image placeholder" class="bg-blur bg-gradient-success aspect-ratio-1" src="{{asset('image/bg2.jpg')}}">
                        </a>
                      </div>
                    </div>
                    <div class="d-flex justify-content-start align-items-center mb-4" v-else>
                      <div class="me-3 d-flex">
                        <a href="javascript:;" class="avatar avatar-lg rounded-circle" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Ryan Tompson">
                          <img alt="Image placeholder" class="bg-blur bg-gradient-success aspect-ratio-1" src="{{asset('image/bg2.jpg')}}">
                        </a>
                      </div>
                      <div class="">
                        <div class="card ">
                          <div class="card-body py-2 px-3">
                            <p class="mb-1">
                              @{{msg.body}}
                            </p>
                            {{-- <div class="d-flex align-items-center text-sm opacity-6">
                              <i class="ni ni-check-bold text-sm me-1"></i>
                              <small>3:14am</small>
                            </div> --}}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {{-- <div class="d-flex justify-content-start align-items-center mb-4">
                    <div class="me-3 d-flex">
                      <a href="javascript:;" class="avatar avatar-lg rounded-circle" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Ryan Tompson">
                        <img alt="Image placeholder" class="bg-blur bg-gradient-success aspect-ratio-1" src="{{asset('image/bg2.jpg')}}">
                      </a>
                    </div>
                    <div class="">
                      <div class="card ">
                        <div class="card-body py-2 px-3">
                          <p class="mb-1">
                             asdas dasd as as dasd asd asd asd asd as dasd asd as dasdasd asd asd
                          </p>
                          <div class="d-flex align-items-center text-sm opacity-6">
                            <i class="ni ni-check-bold text-sm me-1"></i>
                            <small>3:14am</small>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="d-flex justify-content-end text-right align-items-center mb-4">
                    <div class="me-3">
                      <div class="card bg-gradient-primary">
                        <div class="card-body py-2 px-3 text-white">
                          <p class="mb-1">
                            Can it generate daily design links that include essays and data visualizations ?<br>
                          </p>
                          <div class="d-flex align-items-center justify-content-end text-sm opacity-6">
                            <i class="ni ni-check-bold text-sm me-1"></i>
                            <small>4:42pm</small>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="">
                      <a href="javascript:;" class="avatar avatar-lg rounded-circle" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Ryan Tompson">
                        <img alt="Image placeholder" class="bg-blur bg-gradient-success aspect-ratio-1" src="{{asset('image/bg2.jpg')}}">
                      </a>
                    </div>
                  </div> --}}
                  {{-- <div class="row mt-4">
                    <div class="col-md-12 text-center">
                      <span class="badge text-dark">Wed, 3:27pm</span>
                    </div>
                  </div>
                  <div class="row justify-content-start mb-4">
                    <div class="col-auto">
                      <div class="card ">
                        <div class="card-body py-2 px-3">
                          <p class="mb-1">
                            Yeah! Responsive Design is geared towards those trying to build web apps
                          </p>
                          <div class="d-flex align-items-center text-sm opacity-6">
                            <i class="ni ni-check-bold text-sm me-1"></i>
                            <small>4:31pm</small>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="row justify-content-end text-right mb-4">
                    <div class="col-auto">
                      <div class="card bg-gradient-primary">
                        <div class="card-body py-2 px-3 text-white">
                          <p class="mb-1">
                            Excellent, I want it now !
                          </p>
                          <div class="d-flex align-items-center justify-content-end text-sm opacity-6">
                            <i class="ni ni-check-bold text-sm me-1"></i>
                            <small>4:42pm</small>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="row justify-content-start mb-4">
                    <div class="col-auto">
                      <div class="card ">
                        <div class="card-body py-2 px-3">
                          <p class="mb-1">
                            You can easily get it; The content here is all free
                          </p>
                          <div class="d-flex align-items-center text-sm opacity-6">
                            <i class="ni ni-check-bold text-sm me-1"></i>
                            <small>4:42pm</small>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="row justify-content-end text-right mb-4">
                    <div class="col-auto">
                      <div class="card bg-gradient-primary">
                        <div class="card-body py-2 px-3 text-white">
                          <p class="mb-1">
                            Awesome, blog is important source material for anyone who creates apps? <br>
                            Beacuse these blogs offer a lot of information about website development.
                          </p>
                          <div class="d-flex align-items-center justify-content-end text-sm opacity-6">
                            <i class="ni ni-check-bold text-sm me-1"></i>
                            <small>4:42pm</small>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="row justify-content-start mb-4">
                    <div class="col-5">
                      <div class="card ">
                        <div class="card-body p-2">
                          <div class="col-12 p-0">
                            <img src="https://images.unsplash.com/photo-1547949003-9792a18a2601?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&q=80" alt="Rounded image" class="img-fluid mb-2 border-radius-lg">
                          </div>
                          <div class="d-flex align-items-center text-sm opacity-6">
                            <i class="ni ni-check-bold text-sm me-1"></i>
                            <small>4:47pm</small>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="row justify-content-end text-right mb-4">
                    <div class="col-auto">
                      <div class="card bg-gradient-primary">
                        <div class="card-body py-2 px-3 text-white">
                          <p class="mb-0">
                            At the end of the day … the native dev apps is where users are
                          </p>
                          <div class="d-flex align-items-center justify-content-end text-sm opacity-6">
                            <i class="ni ni-check-bold text-sm me-1"></i>
                            <small>4:42pm</small>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="row justify-content-start">
                    <div class="col-auto">
                      <div class="card ">
                        <div class="card-body py-2 px-3">
                          <p class="mb-0">
                            Charlie is Typing...
                          </p>
                        </div>
                      </div>
                    </div>
                  </div> --}}
                </div>
                <div class="card-footer d-block">
                  <form class="align-items-center">
                    <div class="input-group input-group-outline d-flex">
                      {{-- <input type="text" class="form-control form-control-lg"> --}}
                      <textarea class="form-control form-control-lg over-flow-hidden chat-input" rows="1" v-model="message"  @keydown.enter="addMessage()"> </textarea>
                      <button class="btn bg-gradient-info mb-0">
                        <i class="material-icons">send</i>
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </main>
  
  <!--   Core JS Files   -->
  @include('dashboard/js')
  <!-- Kanban scripts -->
  <script src="https://js.pusher.com/8.0.1/pusher.min.js"></script>
  <script type="module">
    Pusher.logToConsole = true; 
    const pusher = new Pusher('0a912ab23e09bfefc652', {
      cluster: 'us2',
      encrypted: true,
      authEndpoint: "{{url('/dashboard/pusher_auth')}}",
      auth: {
              params: {
                "_token":"{{ csrf_token() }}"
              },
            }
    });
    


    import { createApp } from 'https://unpkg.com/vue@3/dist/vue.esm-browser.js'
    createApp({
      data() {
        return {
          messages: [],
          user_id:"{{Auth::user()->id}}",
          message:"",
          com_id:"",
          comms:{!! json_encode($commissions) !!}
        }
      },
      methods:{
        joinChat(com_id){
          let channel = pusher.subscribe("private-chat-"+com_id);

          channel.bind("pusher:subscription_succeeded", () => {
            channel.bind("sent", (data) => {
              if(data.message.from_id != this.user_id){
                this.messages.push(data.message);
                $("#chat-body").stop().animate({scrollTop: 999999});
              }
            });
          });
          this.com_id = com_id
          this.fetchMessages();
          $("#chat-body").stop().animate({scrollTop: 999999});
        },
        fetchMessages() {
          let data = {
            'com_id':this.com_id
          }
          $.ajax({
            method: "GET",
            url: "{{url('dashboard/fetch_message')}}",
            data: data,
            success: response => {
              this.messages = response.data;
            }
          });

          // $("#chat-body").stop().animate({scrollTop: 9999999});
        },
        //Receives the message that was emitted from the ChatForm Vue component
        addMessage() {
          let message_obj = {
            '_token':"{{ csrf_token() }}",
            'from_id': this.user_id,
            'com_id':this.com_id,
            'body':this.message,
          }
          
          this.messages.push(message_obj);

          let chat_body = document.getElementById("chat-body");
          this.scrollToBottom(chat_body)

          $.ajax({
            method: "POST",
            url: "{{url('dashboard/send_message')}}",
            data: message_obj,
            success: response => {
              if(!response.error){
                
              }
            }
          });
        },
        scrollToBottom(container) {
          $(container)
            .stop()
            .animate({
              scrollTop: $(container)[0].scrollHeight,
            });
        }
      },
      created(){
      
      },
      mounted(){
        $("#chat-body").stop().animate({scrollTop: 9999999});
      },
      beforeDestroy() { 
        console.log(123123)
      }
    }).mount('#app')

      //app and el already exists.
      // const app = new Vue({
      //     el: '#app',
      //     //Store chat messages for display in this array.
      //     data: {
      //         messages: []
      //     },
      //     //Upon initialisation, run fetchMessages(). 
      //     created() {
      //         this.fetchMessages();
      //     },
      //     methods: {
      //         fetchMessages() {
      //             //GET request to the messages route in our Laravel server to fetch all the messages
      //             axios.get('/messages').then(response => {
      //                 //Save the response in the messages array to display on the chat view
      //                 this.messages = response.data;
      //             });
      //         },
      //         //Receives the message that was emitted from the ChatForm Vue component
      //         addMessage(message) {
      //             //Pushes it to the messages array
      //             this.messages.push(message);
      //             //POST request to the messages route with the message data in order for our Laravel server to broadcast it.
      //             axios.post('/messages', message).then(response => {
      //                 console.log(response.data);
      //             });
      //         }
      //     }
      // });

  </script>
 
</body>

</html>
