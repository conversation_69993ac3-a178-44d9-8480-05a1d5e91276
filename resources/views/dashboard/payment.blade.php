<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <link rel="apple-touch-icon" sizes="76x76" href="../../../assets/img/apple-icon.png">
  <link rel="icon" type="image/png" href="../../../assets/img/favicon.png">
  <title>
    ESS Payment
  </title>
  <!--     Fonts and icons     -->
  <link rel="stylesheet" type="text/css" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700,900|Roboto+Slab:400,700" />
  <!-- Nucleo Icons -->
  <link href="{{asset('dashboard/css/nucleo-icons.css')}}" rel="stylesheet" />
  <link href="{{asset('dashboard/css/nucleo-svg.css')}}" rel="stylesheet" />
  <!-- Font Awesome Icons -->
  <script src="https://kit.fontawesome.com/42d5adcbca.js" crossorigin="anonymous"></script>
  <!-- Material Icons -->
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Round" rel="stylesheet">
  <!-- CSS Files -->
  <link id="pagestyle" href="{{asset('dashboard/css/material-dashboard.css?v=3.0.5')}}" rel="stylesheet" />
</head>

<body class="">
  {{-- @include('dashboard/sidebar') --}}
  <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg ">
    <!-- Navbar -->
  
    <!-- End Navbar -->
    <div class="container-fluid py-4">
      <div class="row mt-4">
        <div class="col-md-10 col-lg-8 col-sm-10 mx-auto">
          @if($order_payment->pay_status == 0)
          <div class="card my-sm-5">
            <div class="card-header text-center">
              <div class="row justify-content-between">
                <div class="col-md-4 text-start">
                  <img class="mb-2 w-25" src="{{asset('/image/logo_nb.png')}}" alt="Logo">
                  <h6>
                    Elda Solar Studio
                  </h6>
                  {{-- <p class="d-block text-secondary">Email: <EMAIL></p> --}}
                </div>
                <div class="col-lg-3 col-md-7 text-md-end text-start mt-5">
                  <h6 class="d-block mt-2 mb-0">Billed to: {{$user->name}}</h6>
                  <p class="text-secondary">{{$user->email}}<br>
                    
                  </p>
                </div>
              </div>
              <br>
              <div class="row justify-content-md-between">
                <div class="col-md-4 mt-auto">
                  <h6 class="mb-0 text-start text-secondary font-weight-normal">
                    Commission #{{$order->com_id}}</br>
                  </h6>
                  <h5 class="text-start mb-0">
                    
                    {{$commission->project_name}}
                  </h5>
                </div>
                <div class="col-lg-5 col-md-7 mt-auto">
                  <div class="row mt-md-5 mt-4 text-md-end text-start">
                    <div class="col-md-6">
                      <h6 class="text-secondary font-weight-normal mb-0">Invoice date:</h6>
                    </div>
                    <div class="col-md-6">
                      <h6 class="text-dark mb-0">{{$order_payment->created_at}}</h6>
                    </div>
                  </div>
                  <div class="row text-md-end text-start">
                    {{-- <div class="col-md-6">
                      <h6 class="text-secondary font-weight-normal mb-0">Due date:</h6>
                    </div>
                    <div class="col-md-6">
                      <h6 class="text-dark mb-0">11/03/2019</h6>
                    </div> --}}
                  </div>
                </div>
              </div>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-12">
                  <div class="table-responsive">
                    <table class="table text-right">
                      <thead>
                        <tr>
                          <th scope="col" class="pe-2 text-start ps-2">Item</th>
                          <th scope="col" class="pe-2">Qty</th>
                          <th scope="col" class="pe-2" colspan="2">Rate</th>
                          <th scope="col" class="pe-2">Amount</th>
                        </tr>
                      </thead>
                      <tbody>
                        @foreach($order_items as $item)
                        <tr>
                          <td class="text-start">{{$item->name}}</td>
                          <td class="ps-4">{{$item->quantity}}</td>
                          <td class="ps-4" colspan="2">$ {{$item->unit_price}}</td>
                          <td class="ps-4">$ {{$item->total_price}}</td>
                        </tr>
                        @endforeach
                        
                      </tbody>
                      <tfoot>
                        <tr>
                          <th></th>
                          <th></th>
                          <th class="h6 ps-4" colspan="2">Total</th>
                          <th colspan="1" class="text-right h6 ps-4">$ {{$order->price}}</th>
                        </tr>
                      </tfoot>
                    </table>
                  </div>
                </div>
              </div>
              <div class="mt-5 text-end">
                <h6>Payment Detail : </h6>
                <h6>{{$order_payment->name}}</h6>
                <h4>Total : ${{$order_payment->pay_amount}}</h4>
                
              </div>
            </div>
            <div class="card-footer mt-md-5 mt-4">
              <div class="row">
                <div class="col-lg-5 text-left">
                  <h5>Thank you!</h5>
                  <p class="text-secondary text-sm">If you encounter any issues related to the invoice you can contact us at:</p>
                  <h6 class="text-secondary font-weight-normal mb-0">
                    email:
                    <span class="text-dark"><EMAIL></span>
                  </h6>
                </div>
                <div class="col-lg-4 text-left">
                  
                </div>
                <div class="col-lg-3 text-md-end mt-md-0 mt-3">
                  <div id="paypal-button-container"></div>
                </div>
              </div>
            </div>
          </div>
          @elseif($order_payment->pay_status == 1)
          <div class="card invoice">
            <div class="card-body">
              <div class="text-center">
                <img src="{{asset('image/pia_emote/love.gif')}}" class="w-30" alt="avatar image">
                <h3 class="text-info-gradiant mt-2">Thank you</h3>
                <p>This payment has been paid.</p>
                <p>You may now close this window.</p>
                {{-- <a href="{{url('/dashboard/commissions')}}" type="button" class="btn bg-gradient-info mt-4">Close Window</a> --}}
              </div>
              
              
            </div>
            <div class="card-footer mt-md-5 mt-4">
              <div class="row">
                <div class="col-lg-12 text-left">
                  <h5>Thank you!</h5>
                  <p class="text-secondary text-sm">If you encounter any issues, you can contact us at:</p>
                  <h6 class="text-secondary font-weight-normal mb-0">
                    email:
                    <span class="text-dark"><EMAIL></span>
                  </h6>
                  
                </div>
                <div class="col-lg-8 text-md-end mt-md-0 mt-3">
                  {{-- <button class="btn bg-gradient-info mt-lg-7 mb-0" onClick="window.print()" type="button" name="button">Pay Now</button> --}}
                  {{-- <div id="paypal-button-container"></div> --}}
                </div>
                <div class="col-lg-4 text-md-end mt-md-0 mt-3">
                  {{-- <button class="btn bg-gradient-info mt-lg-7 mb-0" onClick="window.print()" type="button" name="button">Pay Now</button> --}}
                  {{-- <div id="paypal-button-container"></div> --}}
                </div>
              </div>
            </div>
          </div>
          @endif
        </div>
      </div>
    </div>
  </main>
  
  <!--   Core JS Files   -->
  @include('dashboard/js')
  <!-- Kanban scripts -->
  <script src="https://www.paypal.com/sdk/js?client-id={{ env('PAYPAL_SANDBOX_CLIENT_ID') }}&currency=USD"></script>
  <script>
    paypal.Buttons({
        // Sets up the transaction when a payment button is clicked
        createOrder: (data, actions) => {
          return actions.order.create({
            purchase_units: [{
              amount: {
                value: '{{$order_payment->pay_amount}}' // Can also reference a variable or function
              }
            }]
          });
        },
        // Finalize the transaction after payer approval
        onApprove: (data, actions) => {
          return actions.order.capture().then(function(paypal_order) {
            // Successful capture! For dev/demo purposes:
            // console.log('Capture result', paypal_order, JSON.stringify(paypal_order, null, 2));
            // const transaction = paypal_order.purchase_units[0].payments.captures[0];
            let paypal_data = {
              "_token": "{{ csrf_token() }}",
              "order_id":"{{$order->id}}",
              "payment_id":"{{$order_payment->id}}",
              "paypal_order_id":paypal_order.id,
              'paid_amount':paypal_order.purchase_units[0].amount.value,
              'full_data':paypal_order,
              'payment_id':{{$order_payment->id}}
            }

            $.ajax({
              method: "POST",
              url: "{{url('dashboard/payment/paypal/approve')}}",
              data: paypal_data,
              success: function(response) {
                location.reload();
              }
            });

        
            
          });
        }
      }).render('#paypal-button-container');
  </script>
</body>

</html>
