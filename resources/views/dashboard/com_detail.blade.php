<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <link rel="apple-touch-icon" sizes="76x76" href="../../../assets/img/apple-icon.png">
  <link rel="icon" type="image/png" href="../../../assets/img/favicon.png">
  <title>
    ESS Dashboard
  </title>

  @include('dashboard/css')
</head>


<body class="g-sidenav-show  bg-gray-200">
  @include('dashboard/sidebar')
  <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg ">
    <!-- Navbar -->
    <nav class="navbar navbar-main navbar-expand-lg position-sticky mt-4 top-1 px-0 mx-4 shadow-none border-radius-xl z-index-sticky" id="navbarBlur" data-scroll="true">
      <div class="container-fluid py-1 px-3">
        <nav aria-label="breadcrumb">
          <ol class="breadcrumb bg-transparent mb-0 pb-0 pt-1 px-0 me-sm-6 me-5">
            <li class="breadcrumb-item text-sm">
              <a class="opacity-3 text-dark" href="javascript:;">
                <svg width="12px" height="12px" class="mb-1" viewBox="0 0 45 40" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                  <title>shop </title>
                  <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                    <g transform="translate(-1716.000000, -439.000000)" fill="#252f40" fill-rule="nonzero">
                      <g transform="translate(1716.000000, 291.000000)">
                        <g transform="translate(0.000000, 148.000000)">
                          <path d="M46.7199583,10.7414583 L40.8449583,0.949791667 C40.4909749,0.360605034 39.8540131,0 39.1666667,0 L7.83333333,0 C7.1459869,0 6.50902508,0.360605034 6.15504167,0.949791667 L0.280041667,10.7414583 C0.0969176761,11.0460037 -1.23209662e-05,11.3946378 -1.23209662e-05,11.75 C-0.00758042603,16.0663731 3.48367543,19.5725301 7.80004167,19.5833333 L7.81570833,19.5833333 C9.75003686,19.5882688 11.6168794,18.8726691 13.0522917,17.5760417 C16.0171492,20.2556967 20.5292675,20.2556967 23.494125,17.5760417 C26.4604562,20.2616016 30.9794188,20.2616016 33.94575,17.5760417 C36.2421905,19.6477597 39.5441143,20.1708521 42.3684437,18.9103691 C45.1927731,17.649886 47.0084685,14.8428276 47.0000295,11.75 C47.0000295,11.3946378 46.9030823,11.0460037 46.7199583,10.7414583 Z"></path>
                          <path d="M39.198,22.4912623 C37.3776246,22.4928106 35.5817531,22.0149171 33.951625,21.0951667 L33.92225,21.1107282 C31.1430221,22.6838032 27.9255001,22.9318916 24.9844167,21.7998837 C24.4750389,21.605469 23.9777983,21.3722567 23.4960833,21.1018359 L23.4745417,21.1129513 C20.6961809,22.6871153 17.4786145,22.9344611 14.5386667,21.7998837 C14.029926,21.6054643 13.533337,21.3722507 13.0522917,21.1018359 C11.4250962,22.0190609 9.63246555,22.4947009 7.81570833,22.4912623 C7.16510551,22.4842162 6.51607673,22.4173045 5.875,22.2911849 L5.875,44.7220845 C5.875,45.9498589 6.7517757,46.9451667 7.83333333,46.9451667 L19.5833333,46.9451667 L19.5833333,33.6066734 L27.4166667,33.6066734 L27.4166667,46.9451667 L39.1666667,46.9451667 C40.2482243,46.9451667 41.125,45.9498589 41.125,44.7220845 L41.125,22.2822926 C40.4887822,22.4116582 39.8442868,22.4815492 39.198,22.4912623 Z"></path>
                        </g>
                      </g>
                    </g>
                  </g>
                </svg>
              </a>
            </li>
            <li class="breadcrumb-item text-sm"><a class="opacity-5 text-dark" href="javascript:;">Dashboard</a></li>
            <li class="breadcrumb-item text-sm text-dark active" aria-current="page">Requests</li>
          </ol>
          <h6 class="font-weight-bolder mb-0">My Requests</h6>
        </nav>
        <div class="sidenav-toggler sidenav-toggler-inner d-xl-block d-none ">
          <a href="javascript:;" class="nav-link text-body p-0">
            <div class="sidenav-toggler-inner">
              <i class="sidenav-toggler-line"></i>
              <i class="sidenav-toggler-line"></i>
              <i class="sidenav-toggler-line"></i>
            </div>
          </a>
        </div>

      </div>
    </nav>
    <!-- End Navbar -->

    <div class="container-fluid mt-4">
      <div class="row align-items-center">
        <div class="col-lg-4 col-sm-8">
          <div class="nav-wrapper position-relative end-0">
            <ul class="nav nav-pills nav-pills-css nav-fill p-1" role="tablist">
              <li class="nav-item cursor-pointer">
                <div class="nav-link active" id="v-pills-prog-tab" data-bs-toggle="pill" href="#v-pills-prog" role="tab" aria-controls="v-pills-prog" aria-selected="true">Progress</div>
              </li>
              {{-- @if(Auth::user()->role_id == 1)
              <li class="nav-item cursor-pointer">
                <div class="nav-link" id="v-pills-info-tab" data-bs-toggle="pill" href="#v-pills-info" role="tab" aria-controls="v-pills-info" aria-selected="false">Commission Info</div>
              </li>
              @endif --}}
            </ul>
          </div>
        </div>
      </div>
    </div>
    <div class="tab-content" id="v-pills-tabContent">
      <div class="tab-pane fade pb-7 show active" id="v-pills-prog" role="tabpanel" aria-labelledby="v-pills-prog-tab">
        <div class="container-fluid my-3 py-3">
          <div class="row mb-5">
            <div class="col-lg-4">

              <div class="card position-sticky top-1">
                <div class="card-header pb-0">
                  <h6>Progress</h6>
                </div>
                <div class="card-body p-3">
                  <div class="timeline timeline-one-side" data-timeline-axis-style="dotted">
                    @if($commission->status < 100)
                    <div class="timeline-block mb-3">
                      @if($commission->status == 1)
                      <span class="timeline-step bg-info p-3">
                        <i class="material-icons text-white text-sm opacity-10">arrow_forward</i>
                      </span>
                      @elseif($commission->status >= 2)
                      <span class="timeline-step bg-success p-3">
                        <i class="material-icons text-white text-sm opacity-10">check</i>
                      </span>
                      @endif
                      <div class="timeline-content pt-1">
                        <h6 class="text-dark text-sm font-weight-bold mb-0">Commission Request Submited</h6>
                        <p class="text-sm text-dark mt-3 mb-2">
                          @if($commission->status == 1)
                          Your request has been submited.
                          @elseif($commission->status >= 2)
                          You commission has been accepted.
                          @endif
                        </p>
                      </div>
                    </div>
                    <div class="timeline-block mb-3">
                      @if($commission->status < 2)
                      <span class="timeline-step bg-secondary p-3">
                        <i class="material-icons text-white text-sm opacity-10">arrow_downward</i>
                      </span>
                      @elseif($commission->status <= 3)
                      <span class="timeline-step bg-info p-3">
                        <i class="material-icons text-white text-sm opacity-10">arrow_forward</i>
                      </span>
                      @elseif($commission->status > 3)
                      <span class="timeline-step bg-success p-3">
                        <i class="material-icons text-white text-sm opacity-10">check</i>
                      </span>
                      @endif
                      <div class="timeline-content pt-1">
                        <h6 class="text-dark text-sm font-weight-bold mb-0">Match Artist</h6>
                        {{-- <p class="text-secondary text-xs mt-1 mb-0">21 DEC 11 PM</p> --}}
                        <p class="text-sm text-dark mt-3 mb-2">
                          @if($commission->status == 2)
                          Matching the best artist(s) for your commission.
                          @elseif($commission->status == 3)
                          Match artists found, waiting for your confirm.
                          @elseif($commission->status >= 4)
                          Artists matched
                          @endif
                        </p>
                      </div>
                    </div>
                    <div class="timeline-block mb-3">
                      @if($commission->status < 5)
                      <span class="timeline-step bg-secondary p-3">
                        <i class="material-icons text-white text-sm opacity-10">arrow_downward</i>
                      </span>
                      @elseif($commission->status < 7)
                      <span class="timeline-step bg-info p-3">
                        <i class="material-icons text-white text-sm opacity-10">arrow_forward</i>
                      </span>
                      @elseif($commission->status >= 7)
                      <span class="timeline-step bg-success p-3">
                        <i class="material-icons text-white text-sm opacity-10">check</i>
                      </span>
                      @endif
                      <div class="timeline-content pt-1">
                        <h6 class="text-dark text-sm font-weight-bold mb-0">Deposit Payment</h6>
                        <p class="text-sm text-dark mt-3 mb-2">
                          @if($commission->status == 5)
                          Invoice created, waiting for payment.
                          @elseif($commission->status == 6)
                          Deposit payment received, waiting for confirm.
                          @elseif($commission->status >= 7)
                          Deposit payment confirmed
                          @endif
                        </p>
                      </div>
                    </div>
                    <div class="timeline-block mb-3">
                      @if($commission->status < 8)
                      <span class="timeline-step bg-secondary p-3">
                        <i class="material-icons text-white text-sm opacity-10">arrow_downward</i>
                      </span>
                      @elseif($commission->status == 8)
                      <span class="timeline-step bg-info p-3">
                        <i class="material-icons text-white text-sm opacity-10">arrow_forward</i>
                      </span>
                      @elseif($commission->status > 8)
                      <span class="timeline-step bg-success p-3">
                        <i class="material-icons text-white text-sm opacity-10">check</i>
                      </span>
                      @endif
                      <div class="timeline-content pt-1">
                        <h6 class="text-dark text-sm font-weight-bold mb-0">Art Creation</h6>
                        <p class="text-sm text-dark mt-3 mb-2">
                          @if($commission->status == 8)
                          Work in progress. Update will be posted on note & chat.
                          @elseif($commission->status > 8)
                          Work finished.
                          @endif
                        </p>
                      </div>
                    </div>
                    <div class="timeline-block mb-3">
                      @if($commission->status < 9)
                      <span class="timeline-step bg-secondary p-3">
                        <i class="material-icons text-white text-sm opacity-10">
                          arrow_downward
                        </i>
                      </span>
                      @elseif($commission->status == 9)
                      <span class="timeline-step bg-info p-3">
                        <i class="material-icons text-white text-sm opacity-10">arrow_forward</i>
                      </span>
                      @elseif($commission->status > 9)
                      <span class="timeline-step bg-success p-3">
                        <i class="material-icons text-white text-sm opacity-10">check</i>
                      </span>
                      @endif
                      <div class="timeline-content pt-1">
                        <h6 class="text-dark text-sm font-weight-bold mb-0">Finish & Review</h6>
                        <p class="text-sm text-dark mt-3 mb-2">
                        </p>
                      </div>
                    </div>
                    @else
                    <div class="timeline-block mb-3">
                      <span class="timeline-step bg-danger p-3">
                        x
                      </span>
                      <div class="timeline-content pt-1">
                        <h6 class="text-dark text-sm font-weight-bold mb-0">Canceled</h6>
                        <p class="text-sm text-dark mt-3 mb-2">
                          Cancel Reason : {{$commission->cancel_reason}}
                        </p>
                      </div>
                    </div>
                    @endif
                  </div>
                </div>
              </div>
            </div>

            <div class="col-lg-8">
              @if($commission->status == 1 || $commission->status == 2)
              <div class="card request-submit">
                <div class="card-header text-center">
                  <div class="row justify-content-between">
                    <div class="col-md-4 text-start">
                      <img class="mb-2 w-20 p-2" src="{{asset('image/logo_nb.png')}}" alt="Logo">

                    </div>
                    <div class="col-lg-6 col-md-7 text-md-end text-start">
                      <h6 class="d-block mb-0">Commission Number : {{$commission->id}}</h6>
                      <p class="text-secondary">Status : {{$commission->status_text}}</p>
                    </div>
                  </div>
                  <br>

                </div>
                <div class="card-body">
                  <div class="row justify-content-md-between">
                    <div class="col-12 col-xl-6 mt-md-0 mt-4 position-relative">
                      <div class="card card-plain h-100">
                        <div class="card-header pb-0 p-3">
                          <div class="row">
                            <div class="col-md-8 d-flex align-items-center">
                              <h6 class="mb-0">Commission #{{$commission->id}} : {{$commission->project_name}}</h6>
                            </div>
                            <div class="col-md-4 text-end">
                              {{-- <a href="javascript:;">
                                <i class="fas fa-user-edit text-secondary text-sm" data-bs-toggle="tooltip" data-bs-placement="top" title="Edit Profile"></i>
                              </a> --}}
                            </div>
                          </div>
                        </div>
                        <div class="card-body p-3">
                          {{-- <p class="text-sm">
                            Hi, I’m Alec Thompson, Decisions: If you can’t decide, the answer is no. If two equally difficult paths, choose the one more painful in the short term (pain avoidance is creating an illusion of equality).
                          </p> --}}
                          {{-- <hr class="horizontal gray-light my-4"> --}}
                          <ul class="list-group">
                            <li class="list-group-item border-0 ps-0 pt-0 text-sm"><strong class="text-dark">Client Name:</strong> &nbsp;{{$commission->client_name}}</li>
                            <li class="list-group-item border-0 ps-0 text-sm"><strong class="text-dark">Email:</strong> &nbsp; {{$commission->email}}</li>
                            {{-- <li class="list-group-item border-0 ps-0 text-sm"><strong class="text-dark">Commission Name:</strong> &nbsp; {{$commission->project_name}}</li> --}}
                            <li class="list-group-item border-0 ps-0 text-sm"><strong class="text-dark">Commission Deadline:</strong> &nbsp; {{$commission->deadline}}</li>
                            <li class="list-group-item border-0 ps-0 text-sm"><strong class="text-dark">Budget:</strong> &nbsp; ${{$commission->budget}}</li>
                            {{-- <li class="list-group-item border-0 ps-0 pb-0">
                              <strong class="text-dark text-sm">Social:</strong> &nbsp;
                              <a class="btn btn-facebook btn-simple mb-0 ps-1 pe-2 py-0" href="javascript:;">
                                <i class="fab fa-facebook fa-lg"></i>
                              </a>
                              <a class="btn btn-twitter btn-simple mb-0 ps-1 pe-2 py-0" href="javascript:;">
                                <i class="fab fa-twitter fa-lg"></i>
                              </a>
                              <a class="btn btn-instagram btn-simple mb-0 ps-1 pe-2 py-0" href="javascript:;">
                                <i class="fab fa-instagram fa-lg"></i>
                              </a>
                            </li> --}}
                          </ul>
                          <hr class="horizontal dark my-4">
                          <h6 class="mb-0">Message</h6>
                          <p class="text-sm">
                            @if($commission->status == 1)
                            Hi, we have received your request. Please allow up to 3 business days for us to confirm your request, we may contact you via email or chat-app
                            @elseif($commission->status == 2)
                            Congratuations!! Your request has been accepted, we are now matching the artists for your request.
                            @endif
                          </p>
                          <hr class="horizontal dark my-4">
                          <p class="text-secondary text-sm">If you encounter any issues, you may contact us at:</p>
                          <h6 class="text-secondary font-weight-normal mb-0">
                            email:
                            <span class="text-dark"><EMAIL></span>
                          </h6>
                        </div>
                      </div>
                      <hr class="vertical dark">
                    </div>
                    <div class="col-12 col-xl-6 mt-xl-0 mt-4">
                      <div class="card card-plain h-100">
                        <div class="card-header pb-0 p-3">
                          <div class="row">
                            <div class="col-md-8 d-flex align-items-center">
                              <h6 class="mb-0">Prefer Artists</h6>
                            </div>
                            <div class="col-md-4 text-end">
                              <span type="button" class="badge bg-gradient-info" id="edit">Edit Priorty</span>
                              <span type="button" class="badge bg-gradient-success d-none-ni" id="save">Save</span>
                            </div>
                          </div>
                        </div>
                        <div class="card-body p-3">
                          @if(count($pref_artists) > 0)
                          <ul class="list-group" id="pref-list">
                            @foreach($pref_artists as $artist)
                            <li class="list-group-item border-0 d-flex align-items-center px-0 mb-2 pref-artist" artist-id="{{$artist->id}}">
                              <i class="fas fa-sort text-sm pe-3 sort-handle"  title="Drag To Move"></i>
                              <div class="avatar avatar-xl me-3">
                                <img src="{{asset('storage/'.$artist->avatar)}}" alt="artist avatar" class="border-radius-lg shadow">
                              </div>
                              <div class="d-flex align-items-start flex-column justify-content-center">
                                <h6 class="mb-0 text-sm">{{$artist->name}}</h6>
                                <p class="mb-0 text-xs">
                                  {{-- <span class="badge badge-info">Rigger</span>
                                  <span class="badge badge-primary">illustrator</span> --}}
                                </p>
                              </div>
                              <div class="btn btn-link pe-3 ps-0 mb-0 ms-auto w-25 w-md-auto remove-artist" artist-id="{{$artist->id}}">REMOVE</div>
                            </li>
                            @endforeach
                            @else
                            <div class="text-center" style="padding-top:35%">You don't have any prefer artists yet. To <a class="text-info" href="{{url('/artists')}}">artist page</a> </div>
                            @endif
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              @endif

              @if($commission->status == 3 || $commission->status == 4)
              <div class="card match-artist">
                <div class="card-header text-center">
                  <div class="row justify-content-between">
                    <div class="col-md-4 text-start">
                      <img class="mb-2 w-20 p-2" src="{{asset('image/logo_nb.png')}}" alt="Logo">

                    </div>
                    <div class="col-lg-6 col-md-7 text-md-end text-start">
                      <h6 class="d-block mb-0">Commission Number : {{$commission->id}}</h6>
                      <p class="text-secondary">Status : Matching with Artists</p>
                    </div>
                  </div>
                  <br>

                </div>
                <div class="card-body">
                  <div class="row justify-content-md-between">
                    <div class="">
                      <h6 class="mb-3 text-start text-secondary">
                        Matched Artists
                      </h6>
                      <div class="table-responsive shadow border-radius-xl">
                        <table class="table align-items-center mb-0">
                          <thead>
                            <tr>
                              <th class="text-uppercase text-xxs font-weight-bolder opacity-7">Name</th>

                              <th class="text-center text-uppercase text-xxs font-weight-bolder opacity-7">Price Est</th>
                              <th class="text-center text-uppercase text-xxs font-weight-bolder opacity-7">Start Date</th>
                              <th class="text-center text-uppercase text-xxs font-weight-bolder opacity-7">Time Need</th>
                              <th class="text-center text-uppercase text-xxs font-weight-bolder opacity-7">Role</th>
                            </tr>
                          </thead>
                          <tbody>
                            @foreach($ava_artists as $ava_artist)
                            <tr>
                              <td>
                                <a href="{{url('/artist').'/'.$ava_artist->id}}" target="_blank" class="d-flex px-2 py-1">
                                  <div>
                                    <img src="{{asset('storage/'.$ava_artist->avatar)}}" class="avatar avatar-md shadow me-3" alt="avatar image">
                                  </div>
                                  <div class="d-flex flex-column justify-content-center">
                                    <h6 class="mb-0 font-weight-normal text-sm">{{$ava_artist->name}}</h6>
                                  </div>
                                </a>
                              </td>

                              <td class="align-middle text-center text-sm">
                                <p class="mb-0 font-weight-normal text-sm">${{$ava_artist->price_est}}</p>
                              </td>
                              <td class="align-middle text-center text-sm">
                                <p class="mb-0 font-weight-normal text-sm">{{$ava_artist->start_date}}</p>
                              </td>
                              <td class="align-middle text-center">
                                <p class="text-sm font-weight-normal mb-0">{{$ava_artist->time_need}}</p>
                              </td>
                              <td class="align-middle text-center">
                                <p class="text-sm font-weight-normal mb-0">{{$ava_artist->role}}</p>
                              </td>
                            </tr>
                            @endforeach
                          </tbody>
                        </table>
                      </div>
                      <div class="text-end mt-5" id="confirm_area">
                        {{-- <div type="button" class="text-end btn bg-gradient-danger btn-sm me-2">Decline</div> --}}
                        @if($commission->status == 3)
                        {{-- <div type="button" class="text-end btn bg-gradient-info btn-sm" id="confirm_artist">Confirm</div> --}}
                        @elseif($commission->status == 4)
                        {{-- <h6 class="text-secondary">
                          Thank you for your confirmation.</br>
                          A detailed invoiced will be generated within a bussiness day.
                        </h6> --}}
                        @endif
                      </div>
                    </div>

                    {{-- <div class="mt-5">
                      <h6 class="mb-3 text-start text-secondary">
                        Available Artists (Rigging)
                      </h6>
                      <div class="table-responsive shadow border-radius-xl">
                        <table class="table align-items-center mb-0">
                          <thead>
                            <tr>
                              <th class="text-uppercase text-xxs font-weight-bolder opacity-7">Name</th>

                              <th class="text-center text-uppercase text-xxs font-weight-bolder opacity-7">Price Est</th>
                              <th class="text-center text-uppercase text-xxs font-weight-bolder opacity-7">Start Date</th>
                              <th class="text-center text-uppercase text-xxs font-weight-bolder opacity-7">Time Need</th>
                              <th class="text-center text-uppercase text-xxs font-weight-bolder opacity-7">Action</th>
                            </tr>
                          </thead>
                          <tbody>
                            @foreach($ava_artists[3] as $rig_artist)
                            <tr>
                              <td>
                                <a href="{{url('/artist').'/'.$rig_artist->id}}" target="_blank" class="d-flex px-2 py-1">
                                  <div>
                                    <img src="{{asset('storage/'.$rig_artist->avatar)}}" class="avatar avatar-md shadow me-3" alt="avatar image">
                                  </div>
                                  <div class="d-flex flex-column justify-content-center">
                                    <h6 class="mb-0 font-weight-normal text-sm">{{$rig_artist->name}}</h6>
                                  </div>
                                </a>
                              </td>

                              <td class="align-middle text-center text-sm">
                                <p class="mb-0 font-weight-normal text-sm">${{$rig_artist->price_est}}</p>
                              </td>
                              <td class="align-middle text-center text-sm">
                                <p class="mb-0 font-weight-normal text-sm">{{$rig_artist->start_date}}</p>
                              </td>
                              <td class="align-middle text-center">
                                <p class="text-sm font-weight-normal mb-0">{{$rig_artist->time_need}}</p>
                              </td>
                              <td class="align-middle text-center">
                                <div type="button" class="btn bg-gradient-info btn-sm mb-0">Select</div>
                              </td>
                            </tr>
                            @endforeach
                          </tbody>
                        </table>
                      </div>
                    </div> --}}

                  </div>
                </div>
              </div>
              @endif

              @if($commission->status == 5)
              <div class="card invoice">
                <div class="card-header text-center">
                  <div class="row justify-content-between">
                    <div class="col-md-4 text-start">
                      <img class="mb-2 w-20 p-2" src="{{asset('image/logo_nb.png')}}" alt="Logo">

                    </div>
                    <div class="col-lg-6 col-md-7 text-md-end text-start">
                      <h6 class="d-block mb-0">Commission Number : {{$commission->id}}</h6>
                      <p class="text-secondary">Status : {{$commission->status_text}}</p>
                    </div>
                  </div>
                  <br>
                </div>
                <div class="card-body">
                  <h6 class="mb-3 text-start text-secondary">
                    Matched Artists
                  </h6>
                  <div class="table-responsive shadow border-radius-xl">
                    <table class="table align-items-center mb-0">
                      <thead>
                        <tr>
                          <th class="text-uppercase text-xxs font-weight-bolder opacity-7">Name</th>

                          <th class="text-center text-uppercase text-xxs font-weight-bolder opacity-7">Price</th>
                          <th class="text-center text-uppercase text-xxs font-weight-bolder opacity-7">Start Date</th>
                          <th class="text-center text-uppercase text-xxs font-weight-bolder opacity-7">Time Need</th>
                          <th class="text-center text-uppercase text-xxs font-weight-bolder opacity-7">Role</th>
                        </tr>
                      </thead>
                      <tbody>
                        @foreach($ava_artists as $ava_artist)
                        <tr>
                          <td>
                            <a href="{{url('/artist').'/'.$ava_artist->artist_id}}" target="_blank" class="d-flex px-2 py-1">
                              <div>
                                <img src="{{asset('storage/'.$ava_artist->avatar)}}" class="avatar avatar-md shadow me-3" alt="avatar image">
                              </div>
                              <div class="d-flex flex-column justify-content-center">
                                <h6 class="mb-0 font-weight-normal text-sm">{{$ava_artist->name}}</h6>
                              </div>
                            </a>
                          </td>

                          <td class="align-middle text-center text-sm">
                            <p class="mb-0 font-weight-normal text-sm">${{$ava_artist->price_est}}</p>
                          </td>
                          <td class="align-middle text-center text-sm">
                            <p class="mb-0 font-weight-normal text-sm">{{$ava_artist->start_date}}</p>
                          </td>
                          <td class="align-middle text-center">
                            <p class="text-sm font-weight-normal mb-0">{{$ava_artist->time_need}}</p>
                          </td>
                          <td class="align-middle text-center">
                            <p class="text-sm font-weight-normal mb-0">{{$ava_artist->role}}</p>
                          </td>
                        </tr>
                        @endforeach
                      </tbody>
                    </table>
                  </div>
                  <h6 class="my-3 mt-7 text-start text-secondary">
                    Payment Detail
                  </h6>
                  <div class="card">
                    <div class="card-body">
                      <div class="row">
                        <div class="col-12">
                          <div class="table-responsive">
                            <table class="table text-right">
                              <thead>
                                <tr>
                                  <th scope="col" class="pe-2 text-start ps-2">Item</th>
                                  <th scope="col" class="pe-2">Qty</th>
                                  <th scope="col" class="pe-2" colspan="2">Rate</th>
                                  <th scope="col" class="pe-2">Amount</th>
                                </tr>
                              </thead>
                              <tbody>
                                @foreach($order_items as $item)
                                <tr>
                                  <td class="text-start">{{$item->name}}</td>
                                  <td class="ps-4">{{$item->quantity}}</td>
                                  <td class="ps-4" colspan="2">$ {{$item->unit_price}}</td>
                                  <td class="ps-4">$ {{$item->total_price}}</td>
                                </tr>
                                @endforeach

                              </tbody>
                              <tfoot>
                                <tr>
                                  <th></th>
                                  <th></th>
                                  <th class="h5 ps-4" colspan="2">Total</th>
                                  <th colspan="1" class="text-right h5 ps-4">$ {{$order->price}}</th>
                                </tr>
                              </tfoot>
                            </table>
                          </div>
                        </div>
                      </div>

                    </div>



                  </div>

                  <h6 class="my-3 mt-7 text-start text-secondary">
                    Payment Plan
                  </h6>
                  <div class="card">
                    <div class="card-body">
                      <div class="row">
                        <div class="col-12">
                          <div class="table-responsive">
                            <table class="table text-right">
                              <thead>
                                <tr>
                                  <th scope="col" class="pe-2 text-start ps-2">Step</th>
                                  <th scope="col" class="pe-2" colspan="2">Amount</th>
                                  <th scope="col" class="pe-2">Status</th>
                                  <th scope="col" class="pe-2">Action</th>
                                </tr>
                              </thead>
                              <tbody>
                                @foreach($order_payments as $payment)
                                <tr>
                                  <td class="text-start">{{$payment->name}}</td>
                                  <td class="ps-4" colspan="2">$ {{$payment->pay_amount}}</td>
                                  <td class="ps-4">{{$payment->status_text}}</td>
                                  @if($payment->status == 2)
                                  <td class="ps-4"><a target="_blank" href="{{url('dashboard/payment/').'/'.$payment->id}}" type="button" class="btn bg-gradient-info btn-sm my-0">Pay Now</a></td>                                  @elseif($payment->status == 1)
                                  <td class="ps-4">NAR</td>
                                  @elseif($payment->status == 3)
                                  <td class="ps-4">Paid</td>
                                  @elseif($payment->status == 4)
                                  <td class="ps-4">Confirm</td>
                                  @endif
                                </tr>
                                @endforeach
                              </tbody>
                            </table>
                          </div>
                        </div>
                      </div>

                    </div>



                  </div>

                </div>
                <div class="card-footer mt-md-5 mt-4">
                  <div class="row">
                    <div class="col-lg-12 text-left">
                      <h5>Thank you!</h5>
                      <p class="text-secondary text-sm">If you encounter any issues related to the invoice you can contact us at:</p>
                      <h6 class="text-secondary font-weight-normal mb-0">
                        email:
                        <span class="text-dark"><EMAIL></span>
                      </h6>
                    </div>
                    <div class="col-lg-8 text-md-end mt-md-0 mt-3">
                      {{-- <button class="btn bg-gradient-info mt-lg-7 mb-0" onClick="window.print()" type="button" name="button">Pay Now</button> --}}
                      {{-- <div id="paypal-button-container"></div> --}}
                    </div>
                    <div class="col-lg-4 text-md-end mt-md-0 mt-3">
                      {{-- <button class="btn bg-gradient-info mt-lg-7 mb-0" onClick="window.print()" type="button" name="button">Pay Now</button> --}}
                      {{-- <div id="paypal-button-container"></div> --}}
                    </div>
                  </div>
                </div>
              </div>
              @endif

              @if($commission->status == 6)
              <div class="card invoice">
                <div class="card-header text-center">
                  <div class="row justify-content-between">
                    <div class="col-md-4 text-start">
                      <img class="mb-2 w-20 p-2" src="{{asset('image/logo_nb.png')}}" alt="Logo">

                    </div>
                    <div class="col-lg-6 col-md-7 text-md-end text-start">
                      <h6 class="d-block mb-0">Commission Number : {{$commission->id}}</h6>
                      <p class="text-secondary">Status: {{$commission->status_text}}</p>
                    </div>
                  </div>
                  <br>
                </div>
                <div class="card-body">
                  <div class="text-center">
                    <img src="{{asset('image/pia_emote/love.png')}}" class="w-30" alt="avatar image">
                    <h3 class="text-info-gradiant mt-2">Thank you</h3>
                    <p>Please Allow 24 hours for us to confirm your payment</p>
                  </div>


                </div>
                <div class="card-footer mt-md-5 mt-4">
                  <div class="row">
                    <div class="col-lg-12 text-left">
                      <h5>Thank you!</h5>
                      <p class="text-secondary text-sm">If you encounter any issues, you can contact us at:</p>
                      <h6 class="text-secondary font-weight-normal mb-0">
                        email:
                        <span class="text-dark"><EMAIL></span>
                      </h6>
                    </div>
                    <div class="col-lg-8 text-md-end mt-md-0 mt-3">
                      {{-- <button class="btn bg-gradient-info mt-lg-7 mb-0" onClick="window.print()" type="button" name="button">Pay Now</button> --}}
                      {{-- <div id="paypal-button-container"></div> --}}
                    </div>
                    <div class="col-lg-4 text-md-end mt-md-0 mt-3">
                      {{-- <button class="btn bg-gradient-info mt-lg-7 mb-0" onClick="window.print()" type="button" name="button">Pay Now</button> --}}
                      {{-- <div id="paypal-button-container"></div> --}}
                    </div>
                  </div>
                </div>
              </div>
              @endif

              @if($commission->status == 7 || $commission->status == 8)
              <div class="card invoice">
                <div class="card-header text-center">
                  <div class="row justify-content-between">
                    <div class="col-md-4 text-start">
                      <img class="mb-2 w-20 p-2" src="{{asset('image/logo_nb.png')}}" alt="Logo">

                    </div>
                    <div class="col-lg-6 col-md-7 text-md-end text-start">
                      <h6 class="d-block mb-0">Commission Number : {{$commission->id}}</h6>
                      <p class="text-secondary">Status: {{$commission->status_text}}</p>
                    </div>
                  </div>
                  <br>
                </div>
                <div class="card-body">

                  <h6>Artists</h6>
                  <div class="row mb-5">
                    @foreach($ava_artists as $ava_artist)
                    <div class="col-auto text-left">
                      <div class="text-center" style="width: fit-content">
                        <a href="{{url('/artist').'/'.$ava_artist->artist_id}}" class="avatar avatar-xl rounded-circle border border-info">
                          <img alt="artist avatar image placeholder" class="p-1" src="{{asset('storage/'.$ava_artist->avatar)}}">
                          </a>
                          <p class="mb-0 text-sm">{{$ava_artist->name}}</p>
                      </div>
                    </div>
                    @endforeach
                  </div>
                  <h6>Progress Note</h6>
                  <div class="card">
                    <div class="card-body">
                      {!!$commission->note!!}
                    </div>
                  </div>

                  <hr class="horizontal dark my-6">
                  <h6>
                    Payment Plan
                  </h6>
                  <div class="card">
                    <div class="card-body">
                      <div class="row">
                        <div class="col-12">
                          <div class="table-responsive">
                            <table class="table text-right">
                              <thead>
                                <tr>
                                  <th scope="col" class="pe-2 text-start ps-2">Step</th>
                                  <th scope="col" class="pe-2" colspan="2">Amount</th>
                                  <th scope="col" class="pe-2">Status</th>
                                  <th scope="col" class="pe-2">Action</th>
                                </tr>
                              </thead>
                              <tbody>
                                @foreach($order_payments as $payment)
                                <tr>
                                  <td class="text-start">{{$payment->name}}</td>
                                  <td class="ps-4" colspan="2">$ {{$payment->pay_amount}}</td>
                                  <td class="ps-4">{{$payment->status_text}}</td>
                                  @if($payment->status == 2)
                                  <td class="ps-4"><a target="_blank" href="{{url('dashboard/payment/').'/'.$payment->id}}" type="button" class="btn bg-gradient-info btn-sm my-0">Pay Now</a></td>
                                  @elseif($payment->status == 1)
                                  <td class="ps-4">NAR</td>
                                  @elseif($payment->status == 3)
                                  <td class="ps-4">Paid</td>
                                  @elseif($payment->status == 4)
                                  <td class="ps-4">Confirm</td>
                                  @endif
                                </tr>
                                @endforeach
                              </tbody>
                            </table>
                          </div>
                        </div>
                      </div>

                    </div>



                  </div>
                </div>
                <div class="card-footer mt-md-5 mt-4">
                  <div class="row">
                    <div class="col-lg-12 text-left">
                      <h5>Thank you!</h5>
                      <p class="text-secondary text-sm">If you encounter any issues, you can contact us at:</p>
                      <h6 class="text-secondary font-weight-normal mb-0">
                        email:
                        <span class="text-dark"><EMAIL></span>
                      </h6>
                    </div>
                    <div class="col-lg-8 text-md-end mt-md-0 mt-3">
                      {{-- <button class="btn bg-gradient-info mt-lg-7 mb-0" onClick="window.print()" type="button" name="button">Pay Now</button> --}}
                      {{-- <div id="paypal-button-container"></div> --}}
                    </div>
                    <div class="col-lg-4 text-md-end mt-md-0 mt-3">
                      {{-- <button class="btn bg-gradient-info mt-lg-7 mb-0" onClick="window.print()" type="button" name="button">Pay Now</button> --}}
                      {{-- <div id="paypal-button-container"></div> --}}
                    </div>
                  </div>
                </div>
              </div>
              @endif

              @if($commission->status == 9|| $commission->status == 10)
              <div class="card invoice">
                <div class="card-header text-center">
                  <div class="row justify-content-between">
                    <div class="col-md-4 text-start">
                      <img class="mb-2 w-20 p-2" src="{{asset('image/logo_nb.png')}}" alt="Logo">
                      
                    </div>
                    <div class="col-lg-6 col-md-7 text-md-end text-start">
                      <h6 class="d-block mb-0">Commission Number : {{$commission->id}}</h6>
                      <p class="text-secondary">Status: {{$commission->status_text}}</p>
                    </div>
                  </div>
                  <br>
                </div>
                <div class="card-body">
            
                  <h6>
                    Review for the artists
                    <div class="dropup d-inline-block">
                      <i class="fa-regular fa-circle-question cursor-pointer" data-bs-toggle="dropdown" aria-expanded="false"></i>
                      <ul class="dropdown-menu px-2 py-3" aria-labelledby="dropdownMenuButton">
                        <li><span class="dropdown-item border-radius-md">Did they complete your commission on time?</span></li>
                        <li><span class="dropdown-item border-radius-md">Did the quality of their work correspond to their portfolios?</span></li>
                        {{-- <li><span class="dropdown-item border-radius-md"></span></li> --}}
                      </ul>
                    </div>
                  </h6>
                  
                  <div class="row mt-6 row-eq-height">
                    @foreach($ava_artists as $ava_artist)
                    @if($ava_artist->review)
                    <div class="col-lg-6 col-md-12">
                      <div class="card h-100">
                        <div class="card-body">
                          <img src="{{asset('storage/'.$ava_artist->avatar)}}" alt="..." class="avatar avatar-lg border-radius-lg shadow mt-n5">
                          <div class="author">
                            <div class="name">
                              <span>{{$ava_artist->name}}</span>
                              <div class="stats">
                                <small>{{$ava_artist->role}}</small>
                              </div>
                            </div>
                          </div>
                          <div class="rating-stars mt-3">
                            <ul class="my-0">
                              @for($i=1;$i<=5;$i++)
                              @if($i<=$ava_artist->review->rating)
                              <i class="fa fa-star selected"></i>
                              @else
                              <i class="fa fa-star"></i>
                              @endif
                              @endfor
                              <input class="review-rating" id="review-rating-{{$ava_artist->artist_id}}" type="hidden">
                            </ul>
                            
                          </div>
                          <div class="mt-3">
                            <p>{{$ava_artist->review->content}}</p>
                          </div>
                          
                          

                        </div>
                      </div>
                    </div>
                    @else
                    <div class="col-lg-6 col-md-12">
                      <div class="card">
                        <div class="card-body">
                          <img src="{{asset('storage/'.$ava_artist->avatar)}}" alt="..." class="avatar avatar-lg border-radius-lg shadow mt-n5">
                          <div class="author">
                            <div class="name">
                              <span>{{$ava_artist->name}}</span>
                              <div class="stats">
                                <small>{{$ava_artist->role}}</small>
                              </div>
                            </div>
                          </div>
                          <div class="rating-stars mt-3">
                            <ul class="my-0 stars">
                              <li class="star" title="Poor" data-value="1">
                                <i class="fa fa-star"></i>
                              </li>
                              <li class="star" title="Fair" data-value="2">
                                <i class="fa fa-star"></i>
                              </li>
                              <li class="star" title="Good" data-value="3">
                                <i class="fa fa-star"></i>
                              </li>
                              <li class="star" title="Excellent" data-value="4">
                                <i class="fa fa-star"></i>
                              </li>
                              <li class="star" title="WOW!!!" data-value="5">
                                <i class="fa fa-star"></i>
                              </li>
                              <input class="review-rating" id="review-rating-{{$ava_artist->artist_id}}" type="hidden">
                            </ul>
                            
                          </div>
                          <div class="mt-1">
                            <textarea class="form-control border px-3 py-2 review-content" id="review-content-{{$ava_artist->artist_id}}" rows="3"></textarea>
                          </div>
                          <div class="text-end mt-2">
                            <button type="button" class="btn btn-outline-info btn-sm btn-submit" review-type="artist" artist-id="{{$ava_artist->artist_id}}">Submit</button>
                          </div>
                          

                        </div>
                      </div>
                    </div>
                    @endif
                    @endforeach

                    {{-- <div class="col-lg-6 col-md-12">
                      <div class="card">
                        <div class="card-body">
                          <img src="{{asset('storage/'.$ava_artists[0]->avatar)}}" alt="..." class="avatar avatar-lg border-radius-lg shadow mt-n5">
                          <div class="author">
                            <div class="name">
                              <span>Mathew Glock</span>
                              <div class="stats">
                                <small>Rigger</small>
                              </div>
                            </div>
                          </div>
                          <div class='rating-stars mt-3'>
                            <ul class="my-0" id='stars'>
                              <li class="star" title='Poor' data-value='1'>
                                <i class="fa fa-star"></i>
                              </li>
                              <li class="star" title='Fair' data-value='2'>
                                <i class="fa fa-star"></i>
                              </li>
                              <li class="star" title='Good' data-value='3'>
                                <i class="fa fa-star"></i>
                              </li>
                              <li class="star" title='Excellent' data-value='4'>
                                <i class="fa fa-star"></i>
                              </li>
                              <li class="star" title='WOW!!!' data-value='5'>
                                <i class="fa fa-star"></i>
                              </li>
                            </ul>
                          </div>
                          <div class="mt-1">
                            <textarea class="form-control border px-3 py-2" rows="3">

                            </textarea>
                          </div>
                          <div class="text-end mt-2">
                            <button type="button" class="btn btn-outline-info btn-sm">Submit</button>

                          </div>
                          

                        </div>
                      </div>
                    </div> --}}
                  </div>
                  <hr class="horizontal dark my-6">
                  <h6>
                    Review for the project manager
                    <div class="dropup d-inline-block">
                      <i class="fa-regular fa-circle-question cursor-pointer" data-bs-toggle="dropdown" aria-expanded="false"></i>
                      <ul class="dropdown-menu px-2 py-3" aria-labelledby="dropdownMenuButton">
                        <li><span class="dropdown-item border-radius-md">Did they answer your questions clearly and offer you solutions?</span></li>
                        <li><span class="dropdown-item border-radius-md">Have you received responses on time from them?</span></li>
                        {{-- <li><span class="dropdown-item border-radius-md">Were they being responsible during the process of commission?</span></li> --}}
                      </ul>
                    </div>
                  </h6>
                  @if($pm->review)
                  <div class="row mt-6">
                    <div class="col-lg-6 col-md-12">
                      <div class="card h-100">
                        <div class="card-body">
                          <img src="{{asset('storage/'.$pm->avatar)}}" alt="..." class="avatar avatar-lg border-radius-lg shadow mt-n5">
                          <div class="author">
                            <div class="name">
                              <span>{{$pm->name}}</span>
                              <div class="stats">
                                <small>Project Manager</small>
                              </div>
                            </div>
                          </div>
                          <div class="rating-stars mt-3">
                            <ul class="my-0">
                              @for($i=1;$i<=5;$i++)
                              @if($i<=$pm->review->rating)
                              <i class="fa fa-star selected"></i>
                              @else
                              <i class="fa fa-star"></i>
                              @endif
                              @endfor
                            </ul>
                            
                          </div>
                          <div class="mt-3">
                            <p>{{$pm->review->content}}</p>
                          </div>
                          
                          

                        </div>
                      </div>
                    </div>
                  </div>
                  @else
                  <div class="row mt-6">
                    <div class="col-lg-6 col-md-12">
                      <div class="card">
                        <div class="card-body">
                          <img src="{{asset('storage/'.$pm->avatar)}}" alt="..." class="avatar avatar-lg border-radius-lg shadow mt-n5">
                          <div class="author">
                            <div class="name">
                              <span>{{$pm->name}}</span>
                              <div class="stats">
                                <small>Project Manager</small>
                              </div>
                            </div>
                          </div>
                          <div class="rating-stars mt-3">
                            <ul class="my-0 stars">
                              <li class="star" title="Poor" data-value="1">
                                <i class="fa fa-star"></i>
                              </li>
                              <li class="star" title="Fair" data-value="2">
                                <i class="fa fa-star"></i>
                              </li>
                              <li class="star" title="Good" data-value="3">
                                <i class="fa fa-star"></i>
                              </li>
                              <li class="star" title="Excellent" data-value="4">
                                <i class="fa fa-star"></i>
                              </li>
                              <li class="star" title="WOW!!!" data-value="5">
                                <i class="fa fa-star"></i>
                              </li>
                              <input class="review-rating" id="review-rating-pm-{{$pm->id}}" type="hidden">
                            </ul>
                            
                          </div>
                          <div class="mt-1">
                            <textarea class="form-control border px-3 py-2 review-content" id="review-content-pm-{{$pm->id}}" rows="3" placeholder=""></textarea>
                          </div>
                          <div class="text-end mt-2">
                            <button type="button" class="btn btn-outline-info btn-sm btn-submit" pm-id="{{$pm->id}}" review-type="pm">Submit</button>
                          </div>
                          

                        </div>
                      </div>
                    </div>
                  </div>
                  @endif
                </div>
                <div class="card-footer mt-md-5 mt-4">
                  <div class="row">
                    <div class="col-lg-12 text-left">
                      <h5>Thank you!</h5>
                      <p class="text-secondary text-sm">If you encounter any issues, you can contact us at:</p>
                      <h6 class="text-secondary font-weight-normal mb-0">
                        email:
                        <span class="text-dark"><EMAIL></span>
                      </h6>
                    </div>
                    <div class="col-lg-8 text-md-end mt-md-0 mt-3">
                      {{-- <button class="btn bg-gradient-info mt-lg-7 mb-0" onClick="window.print()" type="button" name="button">Pay Now</button> --}}
                      {{-- <div id="paypal-button-container"></div> --}}
                    </div>
                    <div class="col-lg-4 text-md-end mt-md-0 mt-3">
                      {{-- <button class="btn bg-gradient-info mt-lg-7 mb-0" onClick="window.print()" type="button" name="button">Pay Now</button> --}}
                      {{-- <div id="paypal-button-container"></div> --}}
                    </div>
                  </div>
                </div>
              </div>
              @endif
            </div>
          </div>
          
        </div>
      </div>
    </div>


  </main>

  <!--   Core JS Files   -->
  @include('dashboard/js')
  

  <script>
    $('.stars li').on('mouseover', function(){
      var onStar = parseInt($(this).data('value'), 10); // The star currently mouse on
      $(this).parent().children('li.star').each(function(e){
        if (e < onStar) {
          $(this).addClass('hover');
        }
        else {
          $(this).removeClass('hover');
        }
      });
    }).on('mouseout', function(){
      $(this).parent().children('li.star').each(function(e){
        $(this).removeClass('hover');
      });
    });
  
    $('.stars li').on('click', function(){
      var onStar = parseInt($(this).data('value'), 10); // The star currently selected
      var stars = $(this).parent().children('li.star');
      
      for (i = 0; i < stars.length; i++) {
        $(stars[i]).removeClass('selected');
      }
      
      for (i = 0; i < onStar; i++) {
        $(stars[i]).addClass('selected');
      }
      
      // JUST RESPONSE (Not needed)
      var ratingValue = parseInt($('.stars li.selected').last().data('value'), 10);

      $(this).parent().children('.review-rating').val(ratingValue); 
      // var msg = "";
      // if (ratingValue > 1) {
      //     msg = "Thanks! You rated this " + ratingValue + " stars.";
      // }
      // else {$
      //     msg = "We will improve ourselves. You rated this " + ratingValue + " stars.";
      // }
    });

    $('.btn-submit').on('click', function(){
      let review_data = {}
      let rating = ''
      let content = ''
      if($(this).attr('review-type') == "artist"){
        let artist_id = $(this).attr('artist-id')
         rating = $('#review-rating-'+artist_id).val()
        content = $('#review-content-'+artist_id).val()
        review_data = {
          'com_id':"{{$commission->id}}",
          'user_id':"{{$commission->user_id}}",
          'artist_id':artist_id,
          'rating':rating,
          'content':content,
          "_token": "{{ csrf_token() }}",
        }
      }
      else if($(this).attr('review-type') == "pm"){
        let pm_id = $(this).attr('pm-id')
        rating = $('#review-rating-pm-'+pm_id).val()
        content = $('#review-content-pm-'+pm_id).val()
        review_data = {
          'com_id':"{{$commission->id}}",
          'user_id':"{{$commission->user_id}}",
          'pm_user_id':pm_id,
          'rating':rating,
          'content':content,
          "_token": "{{ csrf_token() }}",
        }
      }
      console.log(review_data);
      if(rating == '' || content==''){
        toastr.warning('Please rate and fill out the review details', 'Missing Info',{"positionClass": "toast-top-center",})
      }
      else{
        $.ajax({
        method: "POST",
        url: "{{url('dashboard/submit_review')}}",
        data: review_data,
        success: function(response) {
          if(response.code == 200 ){
            toastr.success(response.message, 'Success',{"positionClass": "toast-top-center",})
            location.reload();
          }
          else{
            toastr.error(response.message, 'Error',{"positionClass": "toast-top-center",})
          }
        }
      });  
      }
    })


  </script>
  @if($commission->status < 3)
  <script>
    var el = document.getElementById('pref-list');
    var pl = Sortable.create(el, {
      handle: '.sort-handle',
      animation: 150,
    });


    $( "#edit" ).click(function() {
      $('.sort-handle').show();
      $(this).hide();
      $('#save').show();
    });



    $( "#save" ).click(function() {
      $('.sort-handle').hide();
      $(this).hide();
      $('#edit').show();

      let sorted_artist_ids = [];
      $('.pref-artist').each(function(i, obj) {
        sorted_artist_ids[i] = $(this).attr('artist-id');
      });

      let list_data = {
        "_token": "{{ csrf_token() }}",
        'com_id':{{$commission->id}},
        'sorted_artist_ids':sorted_artist_ids
      }

      // console.log(list_data);

      $.ajax({
        method: "POST",
        url: "{{url('dashboard/sort_artists')}}",
        data: list_data,
        success: function(response) {
          // if(response.code == 200 ){
          //   toastr.success(response.message, 'Success',{"positionClass": "toast-top-center",})
          // }
          // else{
          //   toastr.error(response.message, 'Error',{"positionClass": "toast-top-center",})
          // }

        }
      });
    });

    $( ".remove-artist" ).click(function() {
      let artist_id = $(this).attr('artist-id');
      let remove_data = {
        "_token": "{{ csrf_token() }}",
        'artist_id':$(this).attr('artist-id'),
        'com_id':{{$commission->id}},
      }
      var remove_btn = $(this);
      $.ajax({
        method: "POST",
        url: "{{url('dashboard/remove_artist')}}",
        data: remove_data,
        success: function(response) {
          if(response.code == 200 ){
            toastr.success(response.message, 'Success',{"positionClass": "toast-top-center",})
            remove_btn.parent('.pref-artist').remove();
          }
          else{
            toastr.error(response.message, 'Error',{"positionClass": "toast-top-center",})
          }

        }
      });

    });
  </script>
  @endif
  @if($commission->status == 5)
  {{-- <script src="https://www.paypal.com/sdk/js?client-id={{ env('PAYPAL_SANDBOX_CLIENT_ID') }}&currency=USD"></script>
  <script>
    paypal.Buttons({
        // Sets up the transaction when a payment button is clicked
        createOrder: (data, actions) => {
          return actions.order.create({
            purchase_units: [{
              amount: {
                value: '{{$order->price}}' // Can also reference a variable or function
              }
            }]
          });
        },
        // Finalize the transaction after payer approval
        onApprove: (data, actions) => {
          return actions.order.capture().then(function(paypal_order) {
            // Successful capture! For dev/demo purposes:
            // console.log('Capture result', paypal_order, JSON.stringify(paypal_order, null, 2));
            // const transaction = paypal_order.purchase_units[0].payments.captures[0];


            let paypal_data = {
              "_token": "{{ csrf_token() }}",
              "order_id":"{{$order->id}}",
              "paypal_order_id":paypal_order.id,
              'pay_amount':paypal_order.purchase_units[0].amount.value,
              'full_data':paypal_order,
            }

            $.ajax({
              method: "POST",
              url: "{{url('dashboard/payment/paypal/approve')}}",
              data: paypal_data,
              success: function(response) {


              }
            });


            // When ready to go live, remove the alert and show a success message within this page. For example:
            // const element = document.getElementById('paypal-button-container');
            // element.innerHTML = '<h3>Thank you for your payment!</h3>';
            // Or go to another URL:  actions.redirect('thank_you.html');
          });
        }
      }).render('#paypal-button-container');
  </script> --}}
  @endif
  <!-- Control Center for Material Dashboard: parallax effects, scripts for the example pages etc -->
  {{-- <script src="{{asset('dashboard/js/material-dashboard.min.js')}}?v=3.0.5"></script> --}}
</body>

</html>
