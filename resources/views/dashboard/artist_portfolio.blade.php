<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <link rel="apple-touch-icon" sizes="76x76" href="../../../assets/img/apple-icon.png">
  <link rel="icon" type="image/png" href="../../../assets/img/favicon.png">
  <title>
    Pipipen Dashboard
  </title>
  <!--     Fonts and icons     -->
  @include('dashboard/css')
  <style>
    .aspect-ratio-1{
      aspect-ratio: 1/1;
      object-fit: cover;
      object-position: top;
      overflow: hidden;
    }
    .object-fit-contain{
    object-fit: contain;
    }
    .object-fit-cover{
      object-fit: cover;
      object-position: top 0 left 0;
    }
  </style>
</head>

<body class="g-sidenav-show bg-gray-200">
  @include('dashboard/sidebar')
  <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg ">
    <!-- Navbar -->
    @include('dashboard/topnav')
    <div class="container-fluid py-5" id="app">
      <div class="row mt-5 mb-5">
        <div class="col-lg-3">
          <div class="card position-sticky top-9">
            <ul class="nav flex-column bg-white border-radius-lg p-3">
              <li class="nav-item">
                <a class="nav-link text-dark d-flex" data-scroll="" href="#portfos">
                  <i class="fa-solid fa-palette text-lg me-2"></i>
                  <span class="text-sm">作品合集</span>
                </a>
              </li>
              <li class="nav-item pt-2">
                <a class="nav-link text-dark d-flex" data-scroll="" href="#upload-portfo">
                  <i class="fa-solid fa-upload text-lg me-2"></i>
                  <span class="text-sm">上传图片</span>
                </a>
              </li>
              <li class="nav-item pt-2">
                <a class="nav-link text-dark d-flex" data-scroll="" href="#video-portfo">
                  <i class="fa-brands fa-youtube text-lg me-2"></i>
                  <span class="text-sm">视频链接</span>
                </a>
              </li>
              {{-- <li class="nav-item pt-2">
                <a class="nav-link text-dark d-flex" data-scroll="" href="">
                  <i class="material-icons text-lg me-2">receipt_long</i>
                  <span class="text-sm">Change Password</span>
                </a>
              </li> --}}
              {{-- <li class="nav-item pt-2">
                <a class="nav-link text-dark d-flex" data-scroll="" href="#password">
                  <i class="material-icons text-lg me-2">lock</i>
                  <span class="text-sm">Change Password</span>
                </a>
              </li>
              <li class="nav-item pt-2">
                <a class="nav-link text-dark d-flex" data-scroll="" href="#2fa">
                  <i class="material-icons text-lg me-2">security</i>
                  <span class="text-sm">2FA</span>
                </a>
              </li>
              <li class="nav-item pt-2">
                <a class="nav-link text-dark d-flex" data-scroll="" href="#accounts">
                  <i class="material-icons text-lg me-2">badge</i>
                  <span class="text-sm">Accounts</span>
                </a>
              </li>
              <li class="nav-item pt-2">
                <a class="nav-link text-dark d-flex" data-scroll="" href="#notifications">
                  <i class="material-icons text-lg me-2">campaign</i>
                  <span class="text-sm">Notifications</span>
                </a>
              </li>
              <li class="nav-item pt-2">
                <a class="nav-link text-dark d-flex" data-scroll="" href="#sessions">
                  <i class="material-icons text-lg me-2">settings_applications</i>
                  <span class="text-sm">Sessions</span>
                </a>
              </li>
              <li class="nav-item pt-2">
                <a class="nav-link text-dark d-flex" data-scroll="" href="#delete">
                  <i class="material-icons text-lg me-2">delete</i>
                  <span class="text-sm">Delete Account</span>
                </a>
              </li> --}}
            </ul>
          </div>
        </div>
        <div class="col-lg-9 mt-lg-0 mt-4">
          <!-- Card Basic Info -->
          <div class="card" id="portfos">
            <div class="card-body mt-2">
              <div class="d-flex">
                <h6>作品合集</h6>
                <button type="button" class="btn btn-info btn-sm  m-0 ms-auto" data-mdb-toggle="tooltip" title="Remove item" id="sort-btn">
                  调整顺序
                </button>
                <button type="button" class="btn btn-success btn-sm m-0 ms-auto d-none-ni" data-mdb-toggle="tooltip" title="Remove item" id="sort-save">
                  保存顺序
                </button>
              </div>
              <hr class="horizontal dark">
              <div class="row" id="portfolios">
                @foreach($artist->images as $key=>$image)
                <div class="col-xl-3 col-lg-4 sort-card" image-id="{{$image->id}}">
                  <div class="card shadow-lg mb-3">
                    <div class="card-header p-3 position-relative z-index-1">
                      <div class="position-relative border-radius-lg overflow-hidden">
                        <span class="mask bg-gradient-dark center-flex cursor-grab sort-mask">
                          <div class="text-center">
                            <i class="fa-solid fa-up-down-left-right text-white" style="font-size:3rem"></i>
                            <div class="text-white">拖动调整顺序</div>
                          </div> 
                        </span>
                        <img class="img-fluid border-radius-lg aspect-ratio-1 border portfo-image" src="{{asset('storage/'.$image->image_url)}}" />      
                      </div>
                    </div>
                    <div class="card-body p-3 pt-0">
                      <div class="text-end mt-2">
                        <button type="button" class="btn btn-outline-info btn-sm m-0 me-2 btn-edit" data-mdb-toggle="tooltip" title="Edit" image-key="{{$key}}">
                          <i class="fa-solid fa-gear"></i>
                        </button>
                        <button type="button" class="btn btn-outline-danger btn-sm m-0 btn-delete" data-mdb-toggle="tooltip" title="Delete" image-key="{{$key}}">
                          <i class="fas fa-trash"></i>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
                @endforeach
              </div>
            </div>
          </div>
          <div class="card mt-4" id="upload-portfo">
            <div class="card-header">
              <h5>上传图片</h5>
            </div>
            <div class="card-body pt-0">
              <div class="form-control border dropzone dz-clickable" id="cd_ref" file-type="design">
                <div class="dz-default dz-message"><button class="dz-button" type="button">文件拖到此处上传</button>
                </div>
              </div>
              <button class="btn bg-gradient-info btn-sm float-end mt-6 mb-0" id="confirm_upload">确认上传</button>
            </div>
          </div>
          <div class="card mt-4" id="video-portfo">
            <div class="card-header">
              <h5>视频链接</h5>
            </div>
            <div class="card-body pt-0 text-center">
              <button class="btn bg-gradient-info btn-lg my-3">上传视频链接作品</button>
            </div>
          </div>
          
          
        </div>
      </div>
     
    </div>
  </main>
  <div class="modal fade modal-lg" id="edit-modal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="exampleModalLabel">编辑作品</h5>
          <button type="button" class="btn-close text-dark" data-bs-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body text-center show">
          <div class="row">
            <div class="col-6">
              <div class="border p-2">
                <img class="w-100" src="" id="edit_image">
              </div>
            </div>
            <div class="col-6">
              <div class="form-group text-start">
                <label class="text-start"> 标题 </label>
                <input type="email" class="form-control border border-radius-lg px-2" v-model="user_email" placeholder="标题">
              </div>
              <div class="form-group text-start mt-4">
                <label class="text-start"> 分类 </label>
                <input type="email" class="form-control border border-radius-lg px-2" v-model="user_email" placeholder="" id="categories">
                {{-- <div class="drop-down w-100 card">
                  <div class="card-body">
                    <div class="row">
                      <div class="col-6">
                        <div class="form-check ps-0 me-2">
                          <input class="form-check-input product-c" type="checkbox" id="product-c-" value="" v-model="checkedCategories" @change="check()">
                          <label class="custom-control-label ms-0 cursor-pointer ps-1" for="product-c-"> LIVE2D</label>
                        </div>
                      </div>
                      <div class="col-6">
                        <div class="form-check ps-0 me-2">
                          <input class="form-check-input product-c" type="checkbox" id="product-c-" value="" v-model="checkedCategories" @change="check()">
                          <label class="custom-control-label ms-0 cursor-pointer ps-1" for="product-c-"> LIVE2D</label>
                        </div>
                      </div>
                      <div class="col-6">
                        <div class="form-check ps-0 me-2">
                          <input class="form-check-input product-c" type="checkbox" id="product-c-" value="" v-model="checkedCategories" @change="check()">
                          <label class="custom-control-label ms-0 cursor-pointer ps-1" for="product-c-"> LIVE2D</label>
                        </div>
                      </div>
                      <div class="col-6">
                        <div class="form-check ps-0 me-2">
                          <input class="form-check-input product-c" type="checkbox" id="product-c-" value="" v-model="checkedCategories" @change="check()">
                          <label class="custom-control-label ms-0 cursor-pointer ps-1" for="product-c-"> LIVE2D</label>
                        </div>
                      </div>
                      <div class="col-6">
                        <div class="form-check ps-0 me-2">
                          <input class="form-check-input product-c" type="checkbox" id="product-c-" value="" v-model="checkedCategories" @change="check()">
                          <label class="custom-control-label ms-0 cursor-pointer ps-1" for="product-c-"> LIVE2D</label>
                        </div>
                      </div>
                      <div class="col-3"></div>
                      <div class="col-3"></div>
                    </div>
                  </div>
                  
                </div> --}}
              </div>
              <div class="form-group text-start mt-4">
                <label class="text-start"> 自定义标签 </label>
                <input type="email" class="form-control border border-radius-lg px-2" v-model="user_email" placeholder="输入自定义标签后按回车" id="tags">
                {{-- <div class="drop-down w-100 card">
                  <div class="card-body">
                    <div class="row">
                      <div class="col-6">
                        <div class="form-check ps-0 me-2">
                          <input class="form-check-input product-c" type="checkbox" id="product-c-" value="" v-model="checkedCategories" @change="check()">
                          <label class="custom-control-label ms-0 cursor-pointer ps-1" for="product-c-"> LIVE2D</label>
                        </div>
                      </div>
                      <div class="col-6">
                        <div class="form-check ps-0 me-2">
                          <input class="form-check-input product-c" type="checkbox" id="product-c-" value="" v-model="checkedCategories" @change="check()">
                          <label class="custom-control-label ms-0 cursor-pointer ps-1" for="product-c-"> LIVE2D</label>
                        </div>
                      </div>
                      <div class="col-6">
                        <div class="form-check ps-0 me-2">
                          <input class="form-check-input product-c" type="checkbox" id="product-c-" value="" v-model="checkedCategories" @change="check()">
                          <label class="custom-control-label ms-0 cursor-pointer ps-1" for="product-c-"> LIVE2D</label>
                        </div>
                      </div>
                      <div class="col-6">
                        <div class="form-check ps-0 me-2">
                          <input class="form-check-input product-c" type="checkbox" id="product-c-" value="" v-model="checkedCategories" @change="check()">
                          <label class="custom-control-label ms-0 cursor-pointer ps-1" for="product-c-"> LIVE2D</label>
                        </div>
                      </div>
                      <div class="col-6">
                        <div class="form-check ps-0 me-2">
                          <input class="form-check-input product-c" type="checkbox" id="product-c-" value="" v-model="checkedCategories" @change="check()">
                          <label class="custom-control-label ms-0 cursor-pointer ps-1" for="product-c-"> LIVE2D</label>
                        </div>
                      </div>
                      <div class="col-3"></div>
                      <div class="col-3"></div>
                    </div>
                  </div>
                  
                </div> --}}
              </div>
              <div class="form-group text-start mt-4">
                <label class="text-start"> 作品介绍 </label>
                <textarea class="form-control border border-radius-lg px-2" autocomplete="off" rows="3" maxlength="30"></textarea>
              </div>

              <div class="mt-4">
                <div class="form-check form-switch d-flex align-items-center mb-4">
                  <input class="form-check-input" type="checkbox" id="rememberMe" checked>
                  <label class="form-check-label mb-0 ms-3 text-gray" for="rememberMe">展示</label>
                </div>
              </div>

              {{-- <div class="mt-4">
                <div class="form-check form-switch d-flex align-items-center mb-4">
                  <input class="form-check-input" type="checkbox" id="rememberMe" checked>
                  <label class="form-check-label mb-0 ms-3 text-gray" for="rememberMe">水印</label>
                </div>
              </div> --}}
            </div>
          </div>
          
        </div>
        <div class="modal-footer">
          <button type="button" class="btn bg-gradient-dark btn-sm" data-bs-dismiss="modal">取消</button>
          <button type="button" class="btn bg-gradient-info btn-sm" id="confirm">保存</button>
        </div>
      </div>
    </div>
  </div>

  <div class="modal fade" tabindex="-1" id="delete-modal" role="dialog" aria-hidden="true">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">删除作品</h5>
          <button type="button" class="btn-close text-dark" data-bs-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-12" id="delete-img">
              <img class="img-fluid border-radius-lg aspect-ratio-1 border w-100" src="" id="delete_image">
            </div>
            <div class="col-12 text-center">
              <h3 class="text-danger">你确定要删除该作品吗</h3>
            </div>
          </div>
          
        </div>
        <div class="modal-footer">
          
          <button type="button" class="btn bg-gradient-danger btn-sm" id="confirm_delete">确认删除</button>
          <button type="button" class="btn bg-gradient-dark btn-sm" data-bs-dismiss="modal">取消</button>
        </div>
      </div>
    </div>
  </div>

  {{-- <div class="modal fade" tabindex="-1" id="delete-modal" role="dialog" aria-hidden="true">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">删除作品</h5>
          <button type="button" class="btn-close text-dark" data-bs-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-12" id="delete-img">
              <img class="img-fluid border-radius-lg aspect-ratio-1 border w-100" src="" id="delete_image">
            </div>
            <div class="col-12 text-center">
              <h3 class="text-danger">你确定要删除该作品吗</h3>
            </div>
          </div>
          
        </div>
        <div class="modal-footer">
          
          <button type="button" class="btn bg-gradient-danger btn-sm" id="confirm_delete">确认删除</button>
          <button type="button" class="btn bg-gradient-dark btn-sm" data-bs-dismiss="modal">取消</button>
        </div>
      </div>
    </div>
  </div> --}}


  
    
 
  
  <!--   Core JS Files   -->
  @include('dashboard/js')
  <script src="{{asset('dashboard/js/plugins/dropzone.min.js')}}"></script>
  <script>
    const images = {!! json_encode($artist->images) !!}

    var input = document.querySelector('#categories')
    

    const sort = document.getElementById('portfolios');
    var pl = Sortable.create(sort, {
      swapThreshold: 1,
      handle: '.sort-mask',
      animation: 150,
      onEnd: function (e) {

      },

    });
    
    $( "#sort-btn" ).click(function() {
      $('.sort-mask').css("display","flex");
      $(this).hide();
      $('#sort-save').show();
    });

    $( "#sort-save" ).click(function() {
      let sorted_images_ids = [];
      $('.sort-card').each(function(i, obj) {
        sorted_images_ids[i] = $(this).attr('image-id');
      });

      let list_data = {
        "_token": "{{ csrf_token() }}",
        'sorted_images_ids':sorted_images_ids
      }
      console.log(list_data);

      $.ajax({
        method: "POST",
        url: "{{url('artist_center/sortportfo')}}",
        data: list_data,
        success: function(response) {
          if(response.code == 200 ){
            $( "#sort-save").hide();
            $('#sort-btn').show();
            $('.sort-mask').css("display","none");
            toastr.success(response.message, 'Success',{"positionClass": "toast-top-center",})
          }
          else{
            toastr.error(response.message, 'Error',{"positionClass": "toast-top-center",})
          }
        }
      });
    });

    $( ".btn-edit" ).click(function() {
     
      let key = $(this).attr('image-key');
      $("#edit_image").attr('src',"{{asset('storage')}}/" + images[key]['image_url']);
      $('#edit-modal').modal('show')
    })

    $( ".btn-delete" ).click(function() {
      
      let key = $(this).attr('image-key');
      $("#delete_image").attr('src',"{{asset('storage')}}/" + images[key]['image_url']);
      $("#confirm_delete").attr('image-id',images[key]['id'])
      $('#delete-modal').modal('show')
    })

    $( "#confirm_delete" ).click(function() {
      let delete_data = {
        "_token": "{{ csrf_token() }}",
        'artist_id':"{{$artist->id}}",
        'image_id': $(this).attr('image-id')
      }
      console.log(delete_data)
      
      $.ajax({
        method: "POST",
        url: "{{url('artist_center/delete_portfo')}}",
        data: delete_data,
        success: function(response) {
          if(response.code == 200 ){
            toastr.success(response.message, 'Success',{"positionClass": "toast-top-center",})
            location.reload();
          }
          else{
            toastr.error(response.message, 'Error',{"positionClass": "toast-top-center",})
          }
        }
      });
      // let key = $(this).attr('image-key');
      // $("#edit_image").attr('src',"{{asset('storage')}}/" + images[key]['image_url']);
    })

    Dropzone.autoDiscover = false;
    const dropzone = $('.dropzone').dropzone({
      url: '{{url('user/request/refrence')}}',
      autoProcessQueue:false,
      method: 'post',
      addRemoveLinks: true,
      headers: {
          'X-CSRF-TOKEN': "{{ csrf_token() }}"
      },
      init: function() {
        this.on('sending', function(file, xhr, formData){
          // let dropzone_id = this.element.getAttribute('file-type');
          // formData.append('dropzone_id', dropzone_id);
        });
        this.on("addedfile", file => {
          
        });
        this.on("removedfile", file => {
            
        });
        this.on("success", function(file, response){
          
        });
        const dorpzone = this;
        const submitButton = document.querySelector("#confirm_upload")
        submitButton.addEventListener("click", function() {
          dorpzone.processQueue(); 
        });
      }
    });

    // $("#confirm_upload").click(function() {
    //   dropzone.processQueue()
    // })

    







    
    


  </script>
  
 
</body>

</html>
