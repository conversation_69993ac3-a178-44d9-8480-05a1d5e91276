<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <link rel="apple-touch-icon" sizes="76x76" href="../../../assets/img/apple-icon.png">
  <link rel="icon" type="image/png" href="../../../assets/img/favicon.png">
  <title>
    Pipipen
  </title>
  <!--     Fonts and icons     -->
  @include('dashboard/css')
</head>

<body class="g-sidenav-show bg-gray-200">
  @include('dashboard/sidebar')
  <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg ">
    <!-- Navbar -->
    @include('dashboard/topnav')
    <!-- End Navbar -->
    <div class="container-fluid py-5" id="app">
      <div class="row mt-5 mb-5">
        <div class="row mt-4">
          <div class="col-lg-6 col-12 position-relative">
            <div class="card">
              <div class="card-header p-3 pt-2">
                <div class="icon icon-lg icon-shape bg-gradient-dark shadow text-center border-radius-xl mt-n4 me-3 float-start">
                  <i class="material-icons opacity-10">event</i>
                </div>
                <h6 class="mb-0">{{ __('dashboard.comms_setting') }}</h6>
                {{-- <p class="text-sm mb-0">请确认无误后再点击 "保存并翻译"</p> --}}
              </div>
              <div class="card-body pt-2">
                <div class="mt-4">
                  <p class="mb-2">委托标题</p>
                  <input type="email" class="form-control border border-radius-lg px-2" v-model="user_email" placeholder="标题">
                </div>
                <div class="mt-4">
                  <p class="mb-2">价格￥</p>
                  <div class="d-flex align-items-center">
                    <div class="w-30">
                      <input type="email" class="form-control border border-radius-lg px-2" v-model="user_email" placeholder="">
                    </div>
                    <div class="text-center mx-3">~</div>
                    <div class="w-30">
                      <input type="email" class="form-control border border-radius-lg px-2" v-model="user_email" placeholder="">
                    </div>
                  </div>
                </div>
                
                
                <div class="mt-4">
                  <p class="mb-2">简介</p>
                  <textarea class="form-control border border-radius-lg px-2" autocomplete="off" rows="5" maxlength="30"></textarea>
                </div>

                <div class="mt-4">
                  <p class="mb-2">类型</p>
                  <select class="form-control border border-radius-lg px-2 select-2" multiple="multiple" id="types">
                    @foreach ($coms_types as $type)
                      <option value="{{$type->id}}">{{$type->name_zh}}</option>
                    @endforeach
                  </select>
                 
                </div>

                <div class="mt-4">
                  <p class="mb-2">标签</p>
                  <select class="form-control border border-radius-lg px-2 select-2" multiple="multiple" id="tags">
                    @foreach ($tags as $tag)
                      <option value="{{$tag->id}}">{{$tag->name_en}}</option>
                    @endforeach
                  </select>
                </div>

                

                <div class="mt-4">
                  <p class="mb-2">验收节点</p>
                  <div class="d-flex">
                    <select class="form-control border border-radius-lg px-2" >
                      <option>L2D 验收 （30% - 80% - 100%）</option>
                    </select>
                    <div class="ms-3 w-25 btn bg-gradient-info m-0 ms-2" id="check-points">编辑验收节点</div>
                  </div>
                </div>

                {{-- <div class="mt-4">
                  <p class="mb-2">使用申请表</p>
                  <div class="d-flex">
                    <select class="form-control border border-radius-lg px-2" >
                      <option>1</option>
                    </select>
                    <div class="ms-3 w-25 btn bg-gradient-info m-0 ms-2" id="check-points">编辑申请表</div>
                  </div>
                  
                </div> --}}

                {{-- <div class="mt-4">
                  <p class="mb-2">使用申请表</p>
                  <select class="form-control border border-radius-lg px-2" disabled>
                    <option>1</option>
                    <option>1</option>
                    <option>1</option>
                    <option>1</option>
                  </select>
                </div> --}}

                <div class="mt-4">
                  <p class="mb-1">例图 <span class="ms-3 badge bg-gradient-success cursor-pointer" id="add-showcase-btn">+</span></p>
                  <p class="mb-2 opacity-5 text-sm">例图的添加与删除会立即生效，无需再点击保存</p>
                  <div class="row">
                    <div class="col-xl-3 col-lg-4 sort-card" image-id="">
                      <div class="card shadow-lg mb-3">
                        <div class="card-header p-3 position-relative z-index-1">
                          <div class="position-relative border-radius-lg overflow-hidden">
                            <span class="mask bg-gradient-dark center-flex cursor-grab sort-mask">
                              <div class="text-center">
                                <i class="fa-solid fa-up-down-left-right text-white" style="font-size:3rem"></i>
                                <div class="text-white">拖动调整顺序</div>
                              </div> 
                            </span>
                            <img class="img-fluid border-radius-lg aspect-ratio-1 border portfo-image" src="{{asset('storage/images\October2022\VDRaRN8JbOwq10awle1Y.jpg')}}" />      
                          </div>
                        </div>
                        <div class="card-body p-3 pt-0">
                          <div class="text-end mt-2 text-danger">
                              <i class="fas fa-trash cursor-pointer"></i>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                  </div>
                </div>




                
                <div class="d-flex justify-content-end mt-4">
                  <button type="button" name="button" class="btn btn-light m-0">返回</button>
                  <button type="button" name="button" class="btn bg-gradient-dark m-0 ms-2">保存并翻译</button>
                </div>
              </div>
            </div>
          </div>
          {{-- <div class="col-lg-6 col-12 position-relative">
            <div class="card">
              <div class="card-header p-3 pt-2">
                <div class="icon icon-lg icon-shape bg-gradient-dark shadow text-center border-radius-xl mt-n4 me-3 float-start">
                  <i class="material-icons opacity-10">event</i>
                </div>
                <h6 class="mb-0">翻译 - English</h6>
                <p class="text-sm mb-0">有能力的小伙伴可以自主修改并点击保存，也可以申请人工润色.</p>
              </div>
              <div class="card-body pt-2">
                <div class="input-group input-group-dynamic">
                  <label for="projectName" class="form-label">Project Name</label>
                  <input type="text" class="form-control" id="projectName">
                </div>
                <div class="row mt-4">
                  <div class="col-12 col-md-6">
                    <div class="form-group">
                      <label>
                        Private Project
                      </label>
                      <p class="form-text text-muted ms-1">
                        If you are available for hire outside of the current situation, you can encourage others to hire you.
                      </p>
                      <div class="form-check form-switch ms-1">
                        <input class="form-check-input" type="checkbox" id="flexSwitchCheckDefault" onclick="notify(this)" data-type="warning" data-content="Once a project is made private, you cannot revert it to a public project." data-title="Warning" data-icon="ni ni-bell-55">
                        <label class="form-check-label" for="flexSwitchCheckDefault"></label>
                      </div>
                    </div>
                  </div>
                </div>
                <label class="mt-4">Project Description</label>
                <p class="form-text text-muted ms-1">
                  This is how others will learn about the project, so make it good!
                </p>
                <div id="editor">
                  
                </div>
                <label class="mt-4 form-label">Project Tags</label>
                <select class="form-control" name="choices-multiple-remove-button" id="choices-multiple-remove-button" multiple>
                  <option value="Choice 1" selected>Choice 1</option>
                  <option value="Choice 2">Choice 2</option>
                  <option value="Choice 3">Choice 3</option>
                  <option value="Choice 4">Choice 4</option>
                </select>
                <div class="row">
                  <div class="col-6">
                    <div class="input-group input-group-static">
                      <label>Start Date</label>
                      <input class="form-control datetimepicker" type="text" data-input>
                    </div>
                  </div>
                  <div class="col-6">
                    <div class="input-group input-group-static">
                      <label>End Date</label>
                      <input class="form-control datetimepicker" type="text" data-input>
                    </div>
                  </div>
                </div>
                <div class="input-group input-group-dynamic mt-4">
                  <label class="form-label">Starting Files</label>
                  <form action="/file-upload" class="form-control dropzone" id="dropzone">
                    <div class="fallback">
                      <input name="file" type="file" multiple />
                    </div>
                  </form>
                </div>
                <div class="d-flex justify-content-end mt-4">
                  <button type="button" name="button" class="btn bg-gradient-info mx-2">申请润色</button>
                  <button type="button" name="button" class="btn bg-gradient-info mx-2">保存</button>
                  
                </div>
              </div>
            </div>
          </div> --}}
        </div>
      </div>
     
    </div>
  </main>

  <div class="modal fade modal-xl" id="new-comms" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
      <div class="modal-content max-height-vh-80">
        <div class="modal-header">
          <h5 class="modal-title" id="exampleModalLabel">验收节点</h5>
          <button type="button" class="btn-close text-dark" data-bs-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body text-center show overflow-y-auto">
          <div class="row">
            <div class="col-4 text-start border-end">
              <h6>预设模板</h6>
              <select class="form-control border border-radius-lg px-2" >
                <option>立绘 （30% - 80% - 100%）</option>
                <option>Skeb（100%）</option>
              </select>
              <button type="button" class="btn btn-outline-info btn-sm mt-3" id="confirm">套用模板</button>
            </div>
            <div class="col-8 pb-5 ps-5">
              <h6 class="hidden">占位</h6>
              <div class="timeline timeline-one-side text-start" data-timeline-axis-style="dotted">
                <div class="check-points">
                  <div class="timeline-block mb-5">
                    <span class="timeline-step bg-dark p-3">
                      <i class="material-icons text-white text-sm opacity-10">
                        notifications
                      </i>
                    </span>
                    <div class="timeline-content pt-1 ">
                      <div class="d-flex align-items-center">
                        <div class="input-group input-group-outline me-3 w-50 is-focused">
                          <label class="form-label">节点名称</label>
                          <input type="text" class="form-control">
                        </div>
                        <div>--</div>
                        <div class="input-group input-group-outline mx-3 w-25 is-focused">
                          <label class="form-label">金额 % </label>
                          <input type="number" class="form-control">
                        </div>
                        <div class="cursor-pointer delete-check-point text-danger">x</div>
                      </div>
                    </div>
                  </div>
                </div>
                

                <div class="d-flex align-items-center cursor-pointer add-check-point" >
                  <span class="timeline-step bg-success p-3">
                    <i class="material-icons text-white text-sm opacity-10">
                      add
                    </i>
                  </span>
                  <div class="timeline-content top-0 p-0" >
                    <h6 class="text-dark text-sm font-weight-bold mb-0">添加新的节点</h6>
                  </div>
                </div>
                

                
                
                
              </div>
            </div>
          </div>
          
        </div>
        <div class="modal-footer">
          <button type="button" class="btn bg-gradient-dark btn-sm" data-bs-dismiss="modal">取消</button>
          <button type="button" class="btn bg-gradient-info btn-sm" id="confirm">确认</button>
        </div>
      </div>
    </div>
  </div>

  <div class="modal fade modal-xl" id="add-showcase" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
      <div class="modal-content max-height-vh-80">
        <div class="modal-header">
          <h5 class="modal-title" id="exampleModalLabel">添加例图</h5>
          <button type="button" class="btn-close text-dark" data-bs-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body text-center show overflow-y-auto">
          <div class="row">
            <div class="col-4 text-start border-end">
              <h6>例图来源</h6>
              <select class="form-control border border-radius-lg px-2" >
                <option>从作品选择</option>
                <option>上传图片</option>
              </select>
            </div>
            <div class="col-8 pb-5 ps-5 text-start">
              <div class="portfolios">
                <h6>作品</h6>
                <div class="row" id="portfolios">
                  @foreach($artist->images as $key=>$image)
                  <div class="col-xl-4" image-id="{{$image->id}}">
                    <div class="card shadow-lg mb-3">
                      <div class="card-body p-3">
                        <div class="position-relative border-radius-lg overflow-hidden">
                          <span class="mask bg-gradient-dark center-flex cursor-grab sort-mask">
                            <div class="text-center">
                              <i class="fa-solid fa-up-down-left-right text-white" style="font-size:3rem"></i>
                              <div class="text-white">拖动调整顺序</div>
                            </div> 
                          </span>
                          <img class="img-fluid border-radius-lg aspect-ratio-1 border portfo-image" src="{{asset('storage/'.$image->image_url)}}" />      
                        </div>
                      </div>
                    </div>
                  </div>
                  @endforeach
                </div>
              </div>
              <div class="upload d-none">
                <h6>上传图片</h6>
              </div>

              <div class="iframe d-none">
                <h6>外部链接</h6>
              </div>
              
              
            </div>
          </div>
          
        </div>
        <div class="modal-footer">
          <button type="button" class="btn bg-gradient-dark btn-sm" data-bs-dismiss="modal">取消</button>
          <button type="button" class="btn bg-gradient-info btn-sm" id="add_showcase">添加</button>
        </div>
      </div>
    </div>
  </div>
  
  <!--   Core JS Files   -->
  @include('dashboard/js')

  <script>
    $( "#check-points" ).click(function() {
      $('#new-comms').modal('show')
    })

    $()

    $('.select-2').select2();

    $( ".add-check-point" ).click(function() {
      let new_point_html = '<div class="timeline-block mb-5"><span class="timeline-step bg-dark p-3"><i class="material-icons text-white text-sm opacity-10">notifications</i></span>'+
                           '<div class="timeline-content pt-1 "><div class="d-flex align-items-center"><div class="input-group input-group-outline me-3 w-50 is-focused"><label class="form-label">节点名称</label><input type="text" class="form-control"></div><div>--</div><div class="input-group input-group-outline mx-3 w-25 is-focused"><label class="form-label">金额 % </label><input type="number" class="form-control"></div><div class="cursor-pointer delete-check-point text-danger">x</div></div></div></div>'
      $('.check-points').append(new_point_html);
    })

    $('#add-showcase-btn').click(function() {
      $('#add-showcase').modal('show')
    })



    // if (document.getElementById('edit-deschiption-edit')) {
    //   var quill = new Quill('#edit-deschiption-edit', {
    //     theme: 'snow' // Specify theme in configuration
        
    //   });
    // };
  </script>
</body>

</html>
