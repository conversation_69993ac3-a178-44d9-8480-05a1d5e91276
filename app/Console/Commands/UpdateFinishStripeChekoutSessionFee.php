<?php

namespace App\Console\Commands;

use App;
use App\Models\StripeCheckoutSession;
use App\Service\Stripe\StripeWebhookService;
use Illuminate\Console\Command;

class UpdateFinishStripeChekoutSessionFee extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:update-finish-stripe-chekout-session-fee';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $sessions = StripeCheckoutSession::where('status', StripeCheckoutSession::STATUS_FINISHED)->get();
        foreach ($sessions as $scs) {
            $order = $scs->order;
            $work_task = $order->busable;
            if ($work_task === null) {
                continue;
            }
            if (App::environment(['prod', 'production']) && str_starts_with($scs->checkout_session_id, 'cs_test')) {
                continue;
            }
            if ($order->fee !== null) {
                continue;
            }
            $calc_fee = StripeWebhookService::New()->calcFee($scs->checkout_session_id, $work_task->currency);
            $order->payment_info = $calc_fee->payment_info;
            $order->stripe_fee = $calc_fee->stripe_fee;
            $order->fee = $calc_fee->fee;
            $order->save();
        }
    }
}
