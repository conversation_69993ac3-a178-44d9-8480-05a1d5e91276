<?php

namespace App\Console\Commands;

use App\Models\Service;
use App\Service\ServiceService;
use Illuminate\Console\Command;

class UpdateServiceBasePrice extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:update-service-base-currency-price';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '更新 service 的基础价格，用于筛选查询使用';

    /**
     * Execute the console command.
     */
    public function handle()
    {

        try {
            $services = Service::query()->with(['currency'])->get();

            $this->withProgressBar($services, function (Service $service) {
                $service = ServiceService::updateServiceBasePrice($service);

                $this->info(sprintf('更新 service 的基础价格: id: %s, %s: %s, %s: %s', $service->id, $service->currency->code, $service->price, Service::BASE_PRICE_CURRENCY_CODE, $service->base_currency_price));
            });

            $this->info('service 的基础价格更新完成');

            return 0;

        } catch (\Exception $e) {
            $this->error('service 的基础价格更新失败: '.$e->getMessage());

            return 1;
        }
    }
}
