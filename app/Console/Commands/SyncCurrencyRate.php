<?php

namespace App\Console\Commands;

use App\Models\CurrencyRate;
use Http;
use Illuminate\Console\Command;

class SyncCurrencyRate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-currency-rate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {

        try {
            $baseCode = 'USD';
            $apiKey = config('services.exchange_rate.api_key', '7ee982f9a2bb63f6a61622ad');

            $response = Http::timeout(30)
                ->get("https://v6.exchangerate-api.com/v6/{$apiKey}/latest/{$baseCode}");

            if (! $response->ok()) {
                $this->error('API请求失败: '.$response->status());

                return 1;
            }

            $data = $response->json();
            $rates = $data['conversion_rates'] ?? null;

            if (! $rates) {
                $this->error('无法获取汇率数据');

                return 1;
            }

            $this->updateRates($baseCode, $rates);

            $this->info('汇率同步完成');

            return 0;

        } catch (\Exception $e) {
            $this->error('同步失败: '.$e->getMessage());

            return 1;
        }
    }

    /**
     * 更新汇率数据
     */
    private function updateRates(string $baseCode, array $rates): void
    {
        foreach ($rates as $targetCode => $rate) {
            $currencyRate = CurrencyRate::query()->where('base_code', $baseCode)->where('target_code', $targetCode)->first();
            if (! $currencyRate) {
                $currencyRate = CurrencyRate::create([
                    'base_code' => $baseCode,
                    'target_code' => $targetCode,
                    'rate' => $rate,
                ]);
            }

            // log
            $this->line(sprintf(
                '[USD -> %s] new: %.2f, old: %.2f',
                $targetCode,
                $rate,
                $currencyRate->rate ?? 'null'
            ));

            $currencyRate->rate = $rate;
            $currencyRate->save();
        }
    }
}
