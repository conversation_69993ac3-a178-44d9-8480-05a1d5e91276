<?php

namespace App\Console\Commands;

use App\Models\WorkTask;
use DB;
use Illuminate\Console\Command;
use Storage;

class UpdateOrderInitCurrencyId extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:update-order-init-currency_id';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $orders = DB::table('orders')->where('busable_type', 'work_task')->get();
        foreach ($orders as $order) {
            $workTask = WorkTask::find($order->busable_id);
            if (! $workTask) {
                continue;
            }
            DB::table('orders')->where('id', $order->id)->update([
                'init_currency_id' => $workTask->currency->id,
            ]);
        }
    }
}
