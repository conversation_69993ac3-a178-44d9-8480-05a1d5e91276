<?php

namespace App\Console\Commands;

use App\Models\Artist;
use App\Models\Artwork;
use App\Models\Service;
use Carbon\Carbon;
use Illuminate\Console\Command;

class CalculateRecommendScore extends Command
{
    protected $signature = 'pipipen:calculate-recommend-score';

    protected $description = 'Calculate recommend score for artworks';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        // 获取所有的 artwork 并计算 recommend_score
        Artwork::with('artist')->get()->each(function ($artwork) {
            $artwork->recommend_score = $this->calculateArtworkScore($artwork);
            $artwork->save();
        });

        Artist::get()->each(function ($artist) {
            $artist->recommend_score = $this->calculateArtistScore($artist);
            $artist->save();
        });

        Service::get()->each(function ($service) {
            $service->recommend_score = $this->calcServiceScore($service);
            $service->save();
        });

        $this->info('Recommend scores calculated successfully.');
    }

    private function calculateArtworkScore($artwork)
    {
        $likes = $artwork->likes;
        $artist_followers = $artwork->artist->followers;
        $artist_weight = $artwork->artist->weight;
        $days_from_now = floor(abs(Carbon::now()->diffInDays($artwork->created_at)));

        $this->info('Artwork ID: '.$artwork->id.' Likes: '.$likes.' Artist Followers: '.$artist_followers.' Artist Weight: '.$artist_weight.' Days From Now: '.$days_from_now);
        $ret = ($likes + $artist_followers) * $artist_weight * pow(0.9527, $days_from_now);

        $this->info('Artwork Score: '.$ret);

        return $ret * 1000 * 1000;
    }

    private function calculateArtistScore($artist)
    {
        $artist_followers = $artist->followers;
        $artist_weight = $artist->weight;

        $this->info('Artist ID: '.$artist->id.' Artist Followers: '.$artist_followers.' Artist Weight: '.$artist_weight);
        $ret = $artist_followers * $artist_weight;

        $this->info('Artist Score: '.$ret);

        return $ret * 1000 * 1000;
    }

    private function calcServiceScore($service)
    {
        $artist = $service->artist;

        $artist_followers = $artist->followers;
        $artist_weight = $artist->weight;
        $artist_base_weight = 1;

        // 用户收藏数指数：
        // 默认： 0
        // $artist_followers  [1，10]     	-> 0.2
        // $artist_followers  [11，50]    	-> 0.4
        // $artist_followers  [51，100]   	-> 0.6
        // $artist_followers  [101，500]  	-> 0.8
        // $artist_followers  [501，+∞]	    -> 1.0
        $user_collect_count_index = 0;
        if ($artist_followers >= 1 && $artist_followers <= 10) {
            $user_collect_count_index = 0.2;
        } elseif ($artist_followers >= 11 && $artist_followers <= 50) {
            $user_collect_count_index = 0.4;
        } elseif ($artist_followers >= 51 && $artist_followers <= 100) {
            $user_collect_count_index = 0.6;
        } elseif ($artist_followers >= 101 && $artist_followers <= 500) {
            $user_collect_count_index = 0.8;
        } elseif ($artist_followers >= 501) {
            $user_collect_count_index = 1.0;
        }

        // 购买指数 ：
        // 默认： 0.2
        // 最近7天的购买量 [1, 4] : 0.4
        // 最近7天的购买量 [5, 14] : 0.6
        // 最近7天的购买量 [15, 29] : 0.8
        // 最近7天的购买量 [30, +∞] : 1
        $user_buy_count = $service->serviceRequests()->where('created_at', '>=', Carbon::now()->subDays(7))->count();
        $user_buy_count_index = 0.2;
        if ($user_buy_count >= 1 && $user_buy_count <= 4) {
            $user_buy_count_index = 0.4;
        } elseif ($user_buy_count >= 5 && $user_buy_count <= 14) {
            $user_buy_count_index = 0.6;
        } elseif ($user_buy_count >= 15 && $user_buy_count <= 29) {
            $user_buy_count_index = 0.8;
        } elseif ($user_buy_count >= 30) {
            $user_buy_count_index = 1.0;
        }

        // 评分 = （用户收藏数指数 * 0.3) + （（画师权重/基础权重)*0.4） + 购买指数*0.3)
        $this->info('Service ID: '.$service->id.' User Collect Count Index: '.$user_collect_count_index.' User Buy Count Index: '.$user_buy_count_index.' Artist Weight: '.$artist_weight.' Artist Base Weight: '.$artist_base_weight);
        $ret = ($user_collect_count_index * 0.3) + (($artist_weight / $artist_base_weight) * 0.4) + ($user_buy_count_index * 0.3);

        $this->info('Service Score: '.$ret);

        return $ret * 1000 * 1000;
    }
}
