<?php

namespace App\Console\Commands;

use App\Models\WalletWithdraw;
use Illuminate\Console\Command;

class UpdateWalletWithdrawSnapshot extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:update-wallet-withdraw-snapshot';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {

        try {
            $walletWithdraws = WalletWithdraw::query()->whereNull('wallet_snapshot')->get();

            foreach ($walletWithdraws as $walletWithdraw) {
                $walletWithdraw->wallet_snapshot = $walletWithdraw->wallet()
                    ->with([
                        'currency',
                    ])->first();
                $walletWithdraw->withdraw_account_snapshot = $walletWithdraw->withdrawAccount()
                    ->with([
                        'user:id,name,email',
                        'currency',
                    ])->first();
                $walletWithdraw->timestamps = false;
                $walletWithdraw->save();
                $this->info('更新钱包提现快照: '.$walletWithdraw->id);
            }

            $this->info('钱包提现快照更新完成');

            return 0;

        } catch (\Exception $e) {
            $this->error('钱包提现快照更新失败: '.$e->getMessage());

            return 1;
        }
    }
}
