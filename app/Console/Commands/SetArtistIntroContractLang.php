<?php

namespace App\Console\Commands;

use App\Models\Artist;
use App\Models\Language;
use Illuminate\Console\Command;

class SetArtistIntroContractLang extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:set-artist-intro-contract-lang';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $artists = Artist::all();

        foreach ($artists as $artist) {
            $user = $artist->user;
            if ($user->language) {
                $langCode = $user->language->code;
            } else {
                $langCode = 'en';
                $user->language_id = Language::where('code', $langCode)->first()->id;
                $user->save();
            }

            $intro = (array) $artist->getOriginal('intro');
            if (empty($intro['_lang'])) {
                $intro['_lang'] = $langCode;
            }
            unset($intro['_key']);

            $contract = (array) $artist->getOriginal('contract');
            if (empty($contract['_lang'])) {
                $contract['_lang'] = $langCode;
            }
            unset($contract['_key']);
            Artist::where('id', $artist->id)->update([
                'intro' => $intro,
                'contract' => $contract,
            ]);
        }
    }
}
