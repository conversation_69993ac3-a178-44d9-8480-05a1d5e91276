<?php

namespace App\Console\Commands;

use App\Models\WorkTask;
use App\Service\WorkTaskService;
use Illuminate\Console\Command;

class CreateGroupFromWorkTask extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:create-group-from-work-task';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $workTasks = WorkTask::all();
        foreach ($workTasks as $workTask) {
            WorkTaskService::createGroupFromWorkTask($workTask->id);
        }
    }
}
