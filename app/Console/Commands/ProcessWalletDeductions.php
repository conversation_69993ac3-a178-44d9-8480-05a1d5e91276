<?php

namespace App\Console\Commands;

use App\Service\Stripe\StripeTransferService;
use Illuminate\Console\Command;

class ProcessWalletDeductions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'wallet:process-deductions {--limit=50}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '处理待补差的钱包扣减记录';

    /**
     * Execute the console command.
     */
    public function handle(StripeTransferService $transferService)
    {
        $limit = $this->option('limit');

        $this->info("开始处理待补差记录，限制数量: {$limit}");

        try {
            $result = $transferService->processPendingRecords($limit);

            $this->info("处理完成:");
            $this->info("- 成功处理: {$result['processed']} 条");
            $this->info("- 处理失败: {$result['failed']} 条");

            if (!empty($result['errors'])) {
                $this->error("失败详情:");
                foreach ($result['errors'] as $error) {
                    $this->error("- 记录ID {$error['record_id']}: {$error['error']}");
                }
            }

        } catch (\Exception $e) {
            $this->error("处理失败: " . $e->getMessage());
            return 1;
        }

        return 0;
    }
}
