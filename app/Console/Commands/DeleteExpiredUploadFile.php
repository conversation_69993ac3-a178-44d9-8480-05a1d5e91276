<?php

namespace App\Console\Commands;

use App\Enums\UploadFileState;
use App\Models\UploadFile;
use Illuminate\Console\Command;
use Storage;

class DeleteExpiredUploadFile extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:delete-expired-upload-file';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $files = UploadFile::query()
            ->where('scene', 'chat')
            ->whereNot('state', UploadFileState::Deleted)
            ->where('created_at', '<', now()->subDays(7))->get();

        $files->each(function ($file) {
            $path = $file->getRawOriginal('url_og');
            $exists = Storage::fileExists($path);
            if ($exists) {
                $this->info("File exists: {$path}");
                Storage::delete($path);
            }
            $file->state = UploadFileState::Deleted;
            $file->save();
            $this->info("File deleted: {$path}");
        });

        $this->info('Done');
    }
}
