<?php

namespace App\Console\Commands;

use App\Models\UploadImage;
use Illuminate\Console\Command;
use Image;
use Storage;

class UpdateUploadImageWidthHeight extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:update-upload-image-width-height';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $uploadImages = UploadImage::whereNull('width')->orWhereNull('height')->orderByDesc('id')->get();

        $this->withProgressBar($uploadImages, function (UploadImage $uploadImage) {
            $this->newLine();
            $this->info("current image id: {$uploadImage->id}, url : {$uploadImage->url_og}");
            if (! Storage::exists($uploadImage->url_og)) {
                $this->info("id: {$uploadImage->id} not found");

                return;
            }
            $image = Image::read(Storage::get($uploadImage->url_og));
            $uploadImage->width = $image->width();
            $uploadImage->height = $image->height();
            $this->info("id: {$uploadImage->id}, url : {$uploadImage->url_og}, width: {$uploadImage->width}, height: {$uploadImage->height}");
            $uploadImage->save();
        });

    }
}
