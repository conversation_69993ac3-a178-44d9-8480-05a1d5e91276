<?php

namespace App\Listeners;

use App\Models\StripeWebhook;
use App\Service\Stripe\StripeWebhookService;
use Illuminate\Support\Facades\Log;
use Laravel\Cashier\Events\WebhookReceived;

class StripeEventListener
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(WebhookReceived $event): void
    {
        Log::info('event: ', [$event->payload['type'], $event]);

        StripeWebhook::create([
            'type' => $event->payload['type'],
            'raw' => $event->payload,
        ]);

        if ($event->payload['type'] === 'checkout.session.completed') {
            $stripeWebhookService = new StripeWebhookService();
            $stripeWebhookService->processCheckoutSessionCompleted($event->payload);
        }
    }
}
