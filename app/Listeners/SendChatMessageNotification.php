<?php

namespace App\Listeners;

use App\Events\ChatMessageSentEvent;
use Illuminate\Contracts\Queue\ShouldQueue;

class SendChatMessageNotification implements ShouldQueue
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(ChatMessageSentEvent $event): void
    {
        $event->triggerChatUserPivot();
        $event->updateChatUserPivotVisableStatus();
        $event->updateLastReadCursor();
        $event->updateAttachments();
    }
}
