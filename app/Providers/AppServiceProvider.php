<?php

namespace App\Providers;

use App;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Support\ServiceProvider;
use URL;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {

        Relation::morphMap([
            'project' => 'App\Models\Project',
            'project_request' => 'App\Models\ProjectRequest',
            'service' => 'App\Models\Service',
            'artist' => 'App\Models\Artist',
            'artwork' => 'App\Models\Artwork',
            'product' => 'App\Models\Product',
            'service_request' => 'App\Models\ServiceRequest',
            'work_task_file' => 'App\Models\WorkTaskFile',
            'work_task' => 'App\Models\WorkTask',
            'user_applicant' => 'App\Models\UserApplicant',
            'chat_message' => 'App\Models\ChatMessage',
            'group_message' => 'App\Models\GroupMessage',
        ]);

        if (App::environment(['local', 'test'])) {
            \DB::listen(function ($query) {
                $sql = vsprintf(str_replace('?', "'%s'", $query->sql), $query->bindings);
                \Log::info('SQL Query', [
                    'sql' => $sql,
                    'time' => $query->time,
                ]);
            });
        }

        if (App::environment(['prod', 'production'])) {
            URL::forceScheme('https');
        }

    }
}
