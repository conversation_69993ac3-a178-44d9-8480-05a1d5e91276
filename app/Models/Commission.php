<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Notifications\Notifiable;
use App\Models\User;

//old commission model from eldasolar, abandoned
/**
 * 
 *
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection<int, \Illuminate\Notifications\DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Tag> $tags
 * @property-read int|null $tags_count
 * @method static \Illuminate\Database\Eloquent\Builder|Commission newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Commission newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Commission onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Commission query()
 * @method static \Illuminate\Database\Eloquent\Builder|Commission withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Commission withoutTrashed()
 * @mixin \Eloquent
 */
class Commission extends Model
{
    use HasFactory,SoftDeletes,Notifiable;
    protected $guarded = ['id'];

    public function tags(){
        return $this->morphToMany(Tag::class,'taggable');
    }

    public static function statusRead($commissions,$collection=1){
        
        $read_array = array(
            1 => 'Request Submited',
            2 => 'Request Accepted',
            3 => 'Artists Found',
            4 => 'Client Confirmed',
            5 => 'Invoice Created',
            6 => 'Paid',
            7 => 'Payment Confirm',
            8 => 'Artists Working',
            9 => 'Waiting for Review',
            10 => 'Completed',
            100 => 'Canceled'
        );

        if($collection == 1){
            foreach ($commissions as $commission){
                $commission->status_text = $read_array[$commission->status];
            }
            return $commissions;
        }
        else{
            $commission = $commissions;
            $commission->status_text = $read_array[$commission->status];
            return $commission; 
        }      
    }

    public static function getUser($commissions,$collection=1){
        if($collection == 1){
            foreach ($commissions as $commission){
                $commission->user = User::where('id',$commission->user_id)->first();
            }
            return $commissions;
        }
        else{
            $commission = $commissions;
            $commission->user = User::where('id',$commission->user_id)->first();
            return $commission; 
        }
    }
}
