<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Currency extends Model
{
    use HasFactory;

    const BaseCurrency = 'USD';

    const CanWithdrawCurrencies = ['USD', 'CNY'];

    protected $guarded = ['id'];

    protected $hidden = ['created_at', 'updated_at', 'name', 'sort'];

    protected $appends = ['is_zero_decimal'];

    public function currencyRates()
    {
        return $this->hasMany(CurrencyRate::class);
    }

    public function serviceRequests()
    {
        return $this->hasMany(ServiceRequest::class);
    }

    public function getIsZeroDecimalAttribute()
    {
        return $this->isZeroDecimal();
    }

    public function isZeroDecimal()
    {

        $arr = ['BIF', 'CLP', 'DJF', 'GNF', 'JPY', 'KMF', 'KRW', 'MGA', 'PYG', 'RWF', 'UGX', 'VND', 'VUV', 'XAF', 'XOF', 'XPF'];

        return in_array(strtoupper($this->code), $arr);
    }

    public function getIsCanWithdrawAttribute()
    {
        return $this->isCanWithdraw();
    }

    public function isCanWithdraw()
    {
        return in_array(strtoupper($this->code), $this::CanWithdrawCurrencies);
    }

    public function getIsDefaultWithdrawAttribute()
    {
        return $this->IsDefaultWithdraw();
    }

    public function IsDefaultWithdraw()
    {
        return strtoupper($this->code) === $this::BaseCurrency;
    }
}
