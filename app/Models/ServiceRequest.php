<?php

namespace App\Models;

use Amp\Parallel\Context\StatusError;
use App\Traits\TranslateTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Spatie\Translatable\HasTranslations;

class ServiceRequest extends Model
{
    use HasFactory, HasTranslations, TranslateTrait {
        TranslateTrait::getCasts insteadof HasTranslations;
        TranslateTrait::filterTranslations insteadof HasTranslations;
    }

    public $translatable = ['name', 'detail', 'reject_content', 'accept_content'];

    protected $guarded = ['id'];

    protected $casts = [
        'artist_is_read' => 'boolean',
        'user_is_read' => 'boolean',
        'is_user_deleted' => 'boolean',
        'is_artist_deleted' => 'boolean',
    ];

    const StatusPending = 'pending';

    const StatusArtistAccepted = 'artist_accepted';

    const StatusArtistRejected = 'artist_rejected';

    const StatusUserCanceled = 'user_canceled';

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function serviceRequestFiles()
    {
        return $this->hasMany(ServiceRequestFile::class);
    }

    public function service()
    {
        return $this->belongsTo(Service::class);
    }

    public function artist()
    {
        return $this->belongsTo(Artist::class);
    }

    public function currency()
    {
        return $this->belongsTo(Currency::class);
    }

    public function workTask()
    {
        return $this->morphOne(WorkTask::class, 'reqable');
    }

    public function translates(): MorphMany
    {
        return $this->morphMany(Translate::class, 'busable');
    }
}
