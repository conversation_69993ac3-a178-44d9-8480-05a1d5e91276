<?php

namespace App\Models;

use App\Traits\SyncMorphManyTrait;
use App\Traits\TranslateTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Spatie\Translatable\HasTranslations;

class WorkTaskFileChangeRequest extends Model
{
    use HasFactory, HasTranslations, TranslateTrait{
        TranslateTrait::getCasts insteadof HasTranslations;
        TranslateTrait::filterTranslations insteadof HasTranslations;
    }
    use SyncMorphManyTrait;

    public $translatable = ['content'];

    public $guarded = ['id'];

    protected $casts = [
        'artist_is_read' => 'boolean',
    ];

    public function uploadImages(): MorphMany
    {
        return $this->morphMany(UploadImage::class, 'busable');
    }

    public function workTaskFile(): BelongsTo
    {
        return $this->belongsTo(WorkTaskFile::class);
    }
}
