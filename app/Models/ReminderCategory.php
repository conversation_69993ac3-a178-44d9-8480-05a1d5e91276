<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;

class ReminderCategory extends Model
{
    use HasFactory, HasTranslations;

    protected $guarded = ['id'];

    public $translatable = ['name', 'description'];

    public function templates()
    {
        return $this->hasMany(ReminderTemplate::class, 'category_id')->active();
    }
}
