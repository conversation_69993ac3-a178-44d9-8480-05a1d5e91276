<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * 
 *
 * @method static \Illuminate\Database\Eloquent\Builder|ArtistTag newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ArtistTag newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ArtistTag query()
 * @mixin \Eloquent
 */
class ArtistTag extends Model
{
    use HasFactory;
    protected $table = "artist_tag";
}
