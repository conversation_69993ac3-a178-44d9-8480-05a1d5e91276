<?php

namespace App\Models;

use App\Enums\WorkTaskPriceChangeStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class WorkTaskPriceChange extends Model
{
    use HasFactory;

    protected $guarded = ['id'];

    protected $casts = [
        'approved_at' => 'datetime',
        'paid_at' => 'datetime',
        'status' => WorkTaskPriceChangeStatus::class,
    ];

    public function workTask(): BelongsTo
    {
        return $this->belongsTo(WorkTask::class);
    }

    public function currency(): BelongsTo
    {
        return $this->belongsTo(Currency::class);
    }

    /**
     * 发起人 user/artist
     */
    public function initiator(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * 审核人 user/artist
     */
    public function approver(): MorphTo
    {
        return $this->morphTo();
    }
}
