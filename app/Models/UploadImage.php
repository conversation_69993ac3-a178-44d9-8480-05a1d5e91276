<?php

namespace App\Models;

use App\Utils\Utils;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @mixin \Eloquent
 */
class UploadImage extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'upload_images';

    protected $fillable = [
        'user_id',
        'artist_id',
        'type',
        'url_og',
        'url_sm',
        'url_md',
        'url_lg',
        'is_compressed',
    ];

    protected $hidden = [
        'url_og',
        'is_compressed',
        'busable_type',
        'busable_id',
        'artist_id',
        'user_id',
        'work_task_file_change_request_id',
        'work_task_file_id',
        'type',
        'created_at',
        'updated_at',
        'deleted_at',
        'scene',
    ];

    public function busable(): MorphTo
    {
        return $this->morphTo();
    }

    protected function urlSm(): Attribute
    {
        return Attribute::make(
            get: fn (string $value) => Utils::storageUrl($value),
        );
    }

    protected function urlMd(): Attribute
    {
        return Attribute::make(
            get: fn (string $value) => Utils::storageUrl($value),
        );
    }

    protected function urlLg(): Attribute
    {
        return Attribute::make(
            get: fn (string $value) => Utils::storageUrl($value),
        );
    }
}
