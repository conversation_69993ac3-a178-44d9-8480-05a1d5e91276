<?php

namespace App\Models;

use App\Enums\WalletDeductionStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WalletDeductionRecord extends Model
{
    use HasFactory;

    protected $guarded = ['id'];

    protected $casts = [
        'status' => WalletDeductionStatus::class,
        'metadata' => 'array',
        'compensated_at' => 'datetime',
    ];

    // 关联关系
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function walletTransaction()
    {
        return $this->belongsTo(WalletTransaction::class);
    }

    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    public function artistUser()
    {
        return $this->belongsTo(User::class, 'artist_user_id');
    }

    public function artistWithdrawAccount()
    {
        return $this->belongsTo(UserWithdrawAccount::class, 'artist_withdraw_account_id');
    }

    public function currency()
    {
        return $this->belongsTo(Currency::class);
    }

    // 作用域
    public function scopePending($query)
    {
        return $query->where('status', WalletDeductionStatus::Pending);
    }

    public function scopeProcessing($query)
    {
        return $query->where('status', WalletDeductionStatus::Processing);
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', WalletDeductionStatus::Completed);
    }

    public function scopeFailed($query)
    {
        return $query->where('status', WalletDeductionStatus::Failed);
    }

    public function scopeCancelled($query)
    {
        return $query->where('status', WalletDeductionStatus::Cancelled);
    }
}
