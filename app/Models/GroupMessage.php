<?php

namespace App\Models;

use App\Enums\GroupMessageContentType;
use App\Traits\TranslateTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;

class GroupMessage extends Model
{
    use HasFactory, HasTranslations, TranslateTrait{
        TranslateTrait::getCasts insteadof HasTranslations;
        TranslateTrait::filterTranslations insteadof HasTranslations;
    }

    protected $guarded = ['id'];

    protected $translatable = ['content'];

    protected $hidden = ['created_at', 'updated_at'];

    protected $casts = [
        'content_type' => GroupMessageContentType::class,
        'is_undo' => 'boolean',
    ];

    protected static function booted()
    {
        static::creating(function ($model) {
            $model->ts = (int) (microtime(true) * 1000);
        });
    }

    public function translate()
    {
        return $this->belongsTo(Translate::class);
    }

    public function translates()
    {
        return $this->morphMany(Translate::class, 'busable');
    }

    public function group()
    {
        return $this->belongsTo(Group::class);
    }

    public function attachments()
    {
        return $this->morphMany(UploadFile::class, 'busable');
    }

    public function replyMessage()
    {
        return $this->belongsTo(GroupMessage::class, 'reply_message_id');
    }
}
