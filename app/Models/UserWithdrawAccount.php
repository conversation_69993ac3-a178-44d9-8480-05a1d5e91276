<?php

namespace App\Models;

use App\Enums\UserWithdrawAccountStatus;
use App\Enums\UserWithdrawAccountType;
use App\Enums\UserWithdrawStripeServiceAgreement;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class UserWithdrawAccount extends Model
{
    use HasFactory, SoftDeletes;

    protected $guarded = ['id'];

    protected $casts = [
        'type' => UserWithdrawAccountType::class,
        'bind_status' => UserWithdrawAccountStatus::class,
        'stripe_service_agreement' => UserWithdrawStripeServiceAgreement::class,
    ];

    protected $hidden = [
        'deleted_at',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function currency()
    {
        return $this->belongsTo(Currency::class);
    }
}
