<?php

namespace App\Models;

use App\Enums\WalletWithdrewStatus as WalletWithdrawStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WalletWithdraw extends Model
{
    use HasFactory;

    protected $guarded = ['id'];

    protected $hidden = ['deleted_at'];

    protected $casts = [
        'status' => WalletWithdrawStatus::class,
        'wallet_snapshot' => 'array',
        'withdraw_account_snapshot' => 'array',
    ];

    public function wallet()
    {
        return $this->belongsTo(Wallet::class);
    }

    public function withdrawAccount()
    {
        return $this->belongsTo(UserWithdrawAccount::class, 'withdraw_account_id');
    }

    public function transaction()
    {
        return $this->hasOne(WalletTransaction::class, 'withdraw_id');
    }
}
