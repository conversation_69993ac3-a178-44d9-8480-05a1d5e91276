<?php

namespace App\Models;

use App\Traits\TranslateTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Spatie\Translatable\HasTranslations;

class ProjectRequest extends Model
{
    use HasFactory, HasTranslations, TranslateTrait{
        TranslateTrait::getCasts insteadof HasTranslations;
        TranslateTrait::filterTranslations insteadof HasTranslations;
    }

    protected $guarded = ['id'];

    public $translatable = ['name', 'detail'];

    protected $hidden = ['pivot'];

    const StatusPending = 'pending';

    const StatusArtistCanceled = 'artist_canceled';

    const StatusUserChosen = 'user_chosen';

    public function artist()
    {
        return $this->belongsTo(Artist::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function artworks()
    {
        return $this->belongsToMany(Artwork::class, 'project_request_artwork_pivot');
    }

    public function currency()
    {
        return $this->belongsTo(Currency::class);
    }

    public function project()
    {
        return $this->belongsTo(Project::class);
    }

    public function workTasks(): MorphMany
    {
        return $this->morphMany(WorkTask::class, 'reqable');
    }
}
