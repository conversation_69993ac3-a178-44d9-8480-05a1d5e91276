<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StripeCheckoutSession extends Model
{
    use HasFactory;

    public $guarded = [];

    const STATUS_PENDING = 'pending';

    const STATUS_PROCESSING = 'processing';

    const STATUS_FINISHED = 'finished';

    const PAY_TYPE_FULL_PAY = 'full_pay';

    const PAY_TYPE_STAGE_PAY = 'stage_pay';

    const PAY_TYPE_PRICE_CHANGE = 'price_change';

    protected $casts = [
        'work_task_stage_ids' => 'array',
        'product_ids' => 'array',
    ];

    public function order()
    {
        return $this->belongsTo(Order::class);
    }
}
