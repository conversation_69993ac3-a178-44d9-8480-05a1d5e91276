<?php

namespace App\Models;

use App\Enums\ArtistInviteGiftType;
use App\Traits\TranslateTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Translatable\HasTranslations;

class ArtistInviteGift extends Model
{
    use HasFactory, SoftDeletes;
    use HasTranslations, TranslateTrait {
        TranslateTrait::getCasts insteadof HasTranslations;
        TranslateTrait::filterTranslations insteadof HasTranslations;
    }

    public $translatable = ['content'];

    public $casts = [
        'type' => ArtistInviteGiftType::class,
    ];

    protected $hidden = [
        'deleted_at',
    ];

    public function inviteCodeType(): BelongsTo
    {
        return $this->belongsTo(ArtistInviteCodeType::class, 'invite_code_type_id');
    }

    public function inviterCurrency(): BelongsTo
    {
        return $this->belongsTo(Currency::class, 'inviter_currency_id');
    }

    public function inviteeCurrency(): BelongsTo
    {
        return $this->belongsTo(Currency::class, 'invitee_currency_id');
    }
}
