<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ServiceShowcase extends Model
{
    use HasFactory, SoftDeletes;

    protected $guarded = ['id'];

    protected $hidden = [
        'artist_id',
        'service_id',
        'artwork_id',
        'created_at',
        'updated_at',
    ];

    public function artwork()
    {
        return $this->belongsTo(Artwork::class, 'artwork_id');
    }

    public function uploadImage()
    {
        return $this->belongsTo(UploadImage::class, 'upload_image_id');
    }

    public function uploadVideo()
    {
        return $this->belongsTo(UploadVideo::class, 'upload_video_id');
    }
}
