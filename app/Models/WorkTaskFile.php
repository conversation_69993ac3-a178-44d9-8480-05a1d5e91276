<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\SoftDeletes;

class WorkTaskFile extends Model
{
    use HasFactory, SoftDeletes;

    public $guarded = ['id'];

    protected $casts = [
        'user_is_read' => 'boolean',
    ];

    public function workTaskFileChangeRequests()
    {
        return $this->hasMany(WorkTaskFileChangeRequest::class);
    }

    public function workTask()
    {
        return $this->belongsTo(WorkTask::class);
    }

    public function uploadImages(): MorphMany
    {
        return $this->morphMany(UploadImage::class, 'busable');
    }

    public function uploadImage(): MorphOne
    {
        return $this->MorphOne(UploadImage::class, 'busable');
    }

    public function uploadFiles(): MorphMany
    {
        return $this->morphMany(UploadFile::class, 'busable');
    }

    public function uploadFile(): MorphOne
    {
        return $this->MorphOne(UploadFile::class, 'busable');
    }
}
