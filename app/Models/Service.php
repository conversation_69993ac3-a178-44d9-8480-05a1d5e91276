<?php

namespace App\Models;

use App\Enums\ServiceStatus;
use App\Enums\TagCategoryType;
use App\Traits\HasSavedStatusTrait;
use App\Traits\TranslateTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Translatable\HasTranslations;

class Service extends Model
{
    use HasFactory, HasTranslations, TranslateTrait {
        TranslateTrait::getCasts insteadof HasTranslations;
        TranslateTrait::filterTranslations insteadof HasTranslations;
    }
    use HasSavedStatusTrait, SoftDeletes;

    const BASE_PRICE_CURRENCY_CODE = 'USD';

    protected $guarded = ['id'];

    public $translatable = ['name', 'content'];

    protected $appends = [
        'is_saved',
    ];

    protected $hidden = [
        'url_sm', 'url_md', 'url_lg', 'deleted_at',
    ];

    protected function casts(): array
    {
        return [
            'prod_format' => 'array',
            'status' => ServiceStatus::class,
        ];
    }
    // public function originalContent(){
    //     return $this->hasOne(ServiceContent::class)->where('is_source',1);
    // }

    // public function enContent(){
    //   	return $this->hasOne(ServiceContent::class)->where('lang_code','en');
    // }

    public function artist()
    {
        return $this->belongsTo(Artist::class);
    }

    public function currency()
    {
        return $this->belongsTo(Currency::class);
    }

    public function showcases()
    {
        return $this->hasMany(ServiceShowcase::class)->with([
            'artwork' => [
                'uploadImage',
                'uploadVideo',
            ],
            'uploadImage:id,url_sm,url_md,url_lg',
            'uploadVideo:id,url_sm,url_md,url_lg',
        ])->orderBy('sort')->orderBy('id', 'asc');
    }

    public function showcase()
    {
        return $this->hasOne(ServiceShowcase::class)->with([
            'artwork' => [
                'uploadImage',
                'uploadVideo',
            ],
            'uploadImage:id,url_sm,url_md,url_lg',
            'uploadVideo:id,url_sm,url_md,url_lg',
        ])->orderBy('sort')->orderBy('id', 'asc');
    }

    public function artistRecReviews()
    {
        return $this->hasMany(ArtistRecReview::class);
    }

    public function stages(): BelongsToMany
    {
        return $this->belongsToMany(Stage::class, 'service_stage_pivot')
            ->withPivot('percent')
            ->withTimestamps()
            ->orderByPivot('percent');
    }

    public function categories()
    {
        return $this->belongsToMany(Category::class, 'service_category_pivot');
    }

    public function artStyles()
    {
        return $this->belongsToMany(ArtStyle::class, 'service_art_style_pivot');
    }

    public function workflowType()
    {
        return $this->belongsTo(WorkflowType::class);
    }

    public function creationType()
    {
        return $this->belongsTo(CreationType::class);
    }

    public function preferTags(): MorphToMany
    {
        return $this->morphToMany(Tag::class, 'taggable')
            ->withPivot('category')
            ->wherePivot('category', TagCategoryType::Prefer)
            ->withPivotValue('category', TagCategoryType::Prefer);
    }

    public function cantdoTags(): MorphToMany
    {
        return $this->morphToMany(Tag::class, 'taggable')
            ->withPivot('category')
            ->wherePivot('category', TagCategoryType::Cantdo)
            ->withPivotValue('category', TagCategoryType::Cantdo);
    }

    public function workTasks()
    {
        return $this->morphMany(WorkTask::class, 'busable');
    }

    public function serviceRequests()
    {
        return $this->hasMany(ServiceRequest::class);
    }

    public function rightTemplate(): BelongsTo
    {
        return $this->belongsTo(RightTemplate::class);
    }

    public function translates(): MorphMany
    {
        return $this->morphMany(Translate::class, 'busable');
    }
}
