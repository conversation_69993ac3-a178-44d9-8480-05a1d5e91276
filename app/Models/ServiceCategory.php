<?php

namespace App\Models;

use App\Traits\TranslateTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;
use <PERSON><PERSON><PERSON>meir\LaravelAdjacencyList\Eloquent\HasRecursiveRelationships;

class ServiceCategory extends Model
{
    use HasFactory;
    use HasRecursiveRelationships;
    use HasTranslations, TranslateTrait{
        TranslateTrait::getCasts insteadof HasTranslations;
        TranslateTrait::filterTranslations insteadof HasTranslations;
    }

    public $translatable = ['name'];

    public function children()
    {
        return $this->hasMany(ServiceCategory::class, 'parent_id');
    }

    public function parent()
    {
        return $this->belongsTo(ServiceCategory::class, 'parent_id');
    }
}
