<?php

namespace App\Models;

use App\Enums\ArtistInviteCodeStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class ArtistInviteCode extends Model
{
    use HasFactory, SoftDeletes;

    protected $guarded = ['id'];

    protected $casts = [
        'status' => ArtistInviteCodeStatus::class,
    ];

    protected $hidden = [
        'deleted_at',
    ];

    public function inviterUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'inviter_user_id');
    }

    public function inviterArtist(): BelongsTo
    {
        return $this->belongsTo(Artist::class, 'inviter_artist_id');
    }

    public function inviteeUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'invitee_user_id');
    }

    public function inviteeArtist(): BelongsT<PERSON>
    {
        return $this->belongsTo(Artist::class, 'invitee_artist_id');
    }

    public function inviteCodeType(): BelongsTo
    {
        return $this->belongsTo(ArtistInviteCodeType::class, 'invite_code_type_id');
    }
}
