<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class NotificationTemplate extends Model
{
    use HasFactory, SoftDeletes;

    protected $guarded = ['id'];

    protected $casts = [
        'title' => 'array',
        'content' => 'array',
    ];

    /**
     * 根据类型、事件和角色查找模板
     */
    public static function findTemplate(string $type, string $event, string $role): ?self
    {
        return static::query()
            ->where('type', $type)
            ->where('event', $event)
            ->where('role', $role)
            ->first();
    }
}
