<?php

namespace App\Models;

use App\Traits\TranslateTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;

class ServiceRequestFile extends Model
{
    use HasFactory, HasTranslations, TranslateTrait {
        TranslateTrait::getCasts insteadof HasTranslations;
        TranslateTrait::filterTranslations insteadof HasTranslations;
    }

    public $translatable = ['file_name'];

    protected $guarded = ['id'];

    public function uploadImage()
    {
        return $this->belongsTo(UploadImage::class);
    }
}
