<?php

namespace App\Models;

use App\Traits\TranslateTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Spatie\Translatable\HasTranslations;

class Project extends Model
{
    use HasFactory,HasTranslations, TranslateTrait{
        TranslateTrait::getCasts insteadof HasTranslations;
        TranslateTrait::filterTranslations insteadof HasTranslations;
    }

    protected $guarded = ['id'];

    public $translatable = ['name', 'content'];

    protected function casts(): array
    {
        return [
            'prod_format' => 'array',
            'is_archived' => 'boolean',
            'is_private' => 'boolean',
        ];
    }

    public function artist()
    {
        return $this->belongsTo(Artist::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function examples()
    {
        return $this->hasMany(ProjectExample::class)->with([
            'uploadImage:id,url_sm,url_md,url_lg',
        ])->orderBy('id');
    }

    public function currency()
    {
        return $this->belongsTo(Currency::class);
    }

    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(Category::class, 'project_category_pivot');
    }

    public function colorModels(): BelongsToMany
    {
        return $this->belongsToMany(ColorModel::class, 'project_color_model_pivot');
    }

    public function formats(): BelongsToMany
    {
        return $this->belongsToMany(Format::class, 'project_format_pivot');
    }

    public function stages(): BelongsToMany
    {
        return $this->belongsToMany(Stage::class, 'project_stage_pivot')
            ->withPivot('percent')
            ->orderByPivot('percent');
    }

    // public function styles(): BelongsToMany
    // {
    //     return $this->belongsToMany(Style::class, 'project_style_pivot');
    // }

    public function artStyles(): BelongsToMany
    {
        return $this->belongsToMany(ArtStyle::class, 'project_art_style_pivot');
    }

    public function translations(): BelongsToMany
    {
        return $this->belongsToMany(Translation::class, 'project_translation_pivot');
    }

    public function projectRequests()
    {
        return $this->hasMany(ProjectRequest::class);
    }

    public function projectManagers(): BelongsToMany
    {
        return $this->belongsToMany(ProjectManager::class, 'project_project_manager_pivot')
            ->withTimestamps();
    }

    public function workTasks(): MorphMany
    {
        return $this->morphMany(WorkTask::class, 'busable');
    }

    public function translates(): MorphMany
    {
        return $this->morphMany(Translate::class, 'busable');
    }

    public function rightTemplate(): BelongsTo
    {
        return $this->belongsTo(RightTemplate::class);
    }
}
