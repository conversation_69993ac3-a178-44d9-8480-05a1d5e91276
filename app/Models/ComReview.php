<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * 
 *
 * @method static \Illuminate\Database\Eloquent\Builder|ComReview newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ComReview newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ComReview query()
 * @mixin \Eloquent
 */
class ComReview extends Model
{
    use HasFactory;
    protected $guarded = ['id'];
}
