<?php

namespace App\Models;

use App\Enums\WalletTransactionBizType;
use App\Traits\TranslateTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;

class WalletTransaction extends Model
{
    use HasFactory, HasTranslations, TranslateTrait{
        TranslateTrait::getCasts insteadof HasTranslations;
        TranslateTrait::filterTranslations insteadof HasTranslations;
    }

    protected $translatable = ['reject_content'];

    protected $guarded = ['id'];

    protected $casts = [
        'biz_type' => WalletTransactionBizType::class,
        'detail' => 'array',
    ];

    public function currency()
    {
        return $this->belongsTo(Currency::class);
    }

    public function wallet()
    {
        return $this->belongsTo(Wallet::class);
    }

    public function withdraw()
    {
        return $this->belongsTo(WalletWithdraw::class);
    }
}
