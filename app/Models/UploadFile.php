<?php

namespace App\Models;

use App\Utils\Utils;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @mixin \Eloquent
 */
class UploadFile extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'upload_files';

    protected $fillable = [
        'user_id',
        'url_og',
    ];

    protected $hidden = [
        'busable_type',
        'busable_id',
        'user_id',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public function busable(): MorphTo
    {
        return $this->morphTo();
    }

    protected function urlOg(): Attribute
    {
        return Attribute::make(
            get: fn (string $value) => Utils::storageUrl($value),
        );
    }
}
