<?php

namespace App\Models;

use App\Traits\TranslateTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;

class ArtistRecReview extends Model
{
    use HasFactory, HasTranslations, TranslateTrait {
        TranslateTrait::getCasts insteadof HasTranslations;
        TranslateTrait::filterTranslations insteadof HasTranslations;
    }

    public $translatable = ['rating_content'];

    public $guarded = ['id'];

    protected $casts = [
        'rating_score' => 'float',
    ];

    public function workTask()
    {
        return $this->belongsTo(WorkTask::class);
    }

    public function service()
    {
        return $this->hasOne(Service::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function userInfo()
    {
        return $this->belongsTo(User::class, 'user_id')->select(['id', 'name', 'avatar_id']);
    }

    public function serviceInfo()
    {
        return $this->belongsTo(Service::class, 'user_id')->select(['id', 'name']);
    }
}
