<?php

namespace App\Models;

use App\Enums\ChatMessageContentType;
use App\Traits\TranslateTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;

class ChatMessage extends Model
{
    use HasFactory, HasTranslations, TranslateTrait{
        TranslateTrait::getCasts insteadof HasTranslations;
        TranslateTrait::filterTranslations insteadof HasTranslations;
    }

    const TABLE = 'chat_messages';

    protected $guarded = ['id'];

    protected $translatable = ['content'];

    protected $hidden = ['created_at', 'updated_at'];

    protected $casts = [
        'content_type' => ChatMessageContentType::class,
        'is_undo' => 'boolean',
    ];

    protected static function booted()
    {
        static::creating(function ($model) {
            $model->ts = (int) (microtime(true) * 1000);
        });
    }

    public function translate()
    {
        return $this->belongsTo(Translate::class);
    }

    public function translates()
    {
        return $this->morphMany(Translate::class, 'busable');
    }

    public function chat()
    {
        return $this->belongsTo(Chat::class);
    }

    public function attachments()
    {
        return $this->morphMany(UploadFile::class, 'busable');
    }

    public function replyMessage()
    {
        return $this->belongsTo(ChatMessage::class, 'reply_message_id');
    }
}
