<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class ArtistInviteCodeType extends Model
{
    use HasFactory, SoftDeletes;

    protected $hidden = [
        'deleted_at',
    ];

    public $casts = [
        'need_confirm' => 'boolean',
    ];

    public function inviteGifts(): HasMany
    {
        return $this->hasMany(ArtistInviteGift::class, 'invite_code_type_id');
    }
}
