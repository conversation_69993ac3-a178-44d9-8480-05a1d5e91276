<?php

namespace App\Models;

use App\Traits\TranslateTrait;
use App\Utils\Utils;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;

class ProjectRequestFile extends Model
{
    use HasFactory, HasTranslations, TranslateTrait {
        TranslateTrait::getCasts insteadof HasTranslations;
        TranslateTrait::filterTranslations insteadof HasTranslations;
    }

    protected $guarded = ['id'];

    public $translatable = ['file_name'];

    protected function fileUrl(): Attribute
    {
        return Attribute::make(
            get: fn (string $value) => Utils::storageUrl($value),
        );
    }
}
