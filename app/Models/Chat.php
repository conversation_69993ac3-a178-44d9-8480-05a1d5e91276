<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;

class Chat extends Model
{
    use HasFactory;
    use Notifiable;

    protected $guarded = ['id'];

    protected $fillable = [
        'key',
        'last_message_ts',
    ];

    protected $hidden = [
    ];

    protected $casts = [
    ];

    protected static function booted()
    {
    }

    public function messages()
    {
        return $this->hasMany(ChatMessage::class);
    }

    public function lastMessage()
    {
        return $this->hasOne(ChatMessage::class)->latest();
    }

    public function users()
    {
        return $this->belongsToMany(User::class, 'chat_user_pivot', 'chat_id', 'user_id')
            ->withPivot('last_read_cursor')
            ->withPivot('chat_visable_status')
            ->withPivot('settings')
            ->withPivot('order_at_ts')
            ->using(ChatUserPivot::class)
            ->withTimestamps();
    }

    public function notifications()
    {
        return $this->morphMany(CustomNotification::class, 'notifiable')->latest();
    }
}
