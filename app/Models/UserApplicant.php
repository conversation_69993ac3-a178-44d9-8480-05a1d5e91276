<?php

namespace App\Models;

use App\Enums\UserApplicantStatus;
use App\Traits\TranslateTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Spatie\Translatable\HasTranslations;

class UserApplicant extends Model
{
    use HasFactory;
    use HasTranslations, TranslateTrait{
        TranslateTrait::getCasts insteadof HasTranslations;
        TranslateTrait::filterTranslations insteadof HasTranslations;
    }

    public $translatable = ['reject_content'];

    protected $guarded = ['id'];

    protected $casts = [
        'status' => UserApplicantStatus::class,
    ];

    public function uploadImages(): MorphMany
    {
        return $this->morphMany(UploadImage::class, 'busable');
    }

    public function artistInviteCode(): BelongsTo
    {
        return $this->belongsTo(ArtistInviteCode::class, 'artist_invite_code_id');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
