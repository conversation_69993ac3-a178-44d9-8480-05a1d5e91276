<?php

namespace App\Models;

use App\Traits\TranslateTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;

class Style extends Model
{
    use HasFactory, HasTranslations, TranslateTrait {
        TranslateTrait::getCasts insteadof HasTranslations;
        TranslateTrait::filterTranslations insteadof HasTranslations;
    }

    public $translatable = ['name'];

    protected $guarded = ['id'];

    protected $hidden = ['created_at', 'updated_at', 'pivot'];

    public function projects()
    {
        return $this->belongsToMany(Project::class, 'project_art_style_pivot');
    }
}
