<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class ProductOrderItem extends Model
{
    use HasFactory;

    protected $guarded = ['id'];

    protected $casts = [
        'product_snap' => 'array',
    ];

    protected $hidden = [
        'pivot',
    ];

    public function product()
    {
        return $this->hasOne(Product::class, 'id', 'product_id');
    }

    public function files(): BelongsToMany
    {
        return $this->belongsToMany(ProductFile::class, 'product_order_item_file_pivot')->withTimestamps();
    }

    public function productOrder()
    {
        return $this->belongsTo(ProductOrder::class, 'order_id');
    }
}
