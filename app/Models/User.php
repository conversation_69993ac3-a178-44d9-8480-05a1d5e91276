<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;

use App\Casts\UserSettingsCast;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Cashier\Billable;
use Lara<PERSON>\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    use Billable, HasApiTokens, HasFactory, HasRoles, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'settings' => UserSettingsCast::class,
        ];
    }

    public function artist(): HasOne
    {
        return $this->hasOne(Artist::class);
    }

    public function serviceRequests()
    {
        return $this->hasMany(ServiceRequest::class);
    }

    public function workTasks()
    {
        return $this->hasMany(WorkTask::class);
    }

    public function workTaskFiles(): HasMany
    {
        return $this->hasMany(WorkTaskFile::class);
    }

    public function workTaskFileChangeRequests(): HasManyThrough
    {
        return $this->hasManyThrough(WorkTaskFileChangeRequest::class, WorkTaskFile::class);
    }

    public function uploadImages()
    {
        return $this->hasMany(UploadImage::class);
    }

    public function uploadFiles()
    {
        return $this->hasMany(UploadFile::class);
    }

    public function artistRecReviews(): HasMany
    {
        return $this->hasMany(ArtistRecReview::class);
    }

    public function projects(): HasMany
    {
        return $this->hasMany(Project::class);
    }

    public function projectRequests()
    {
        return $this->hasMany(ProjectRequest::class);
    }

    public function productOrders()
    {
        return $this->hasMany(ProductOrder::class);
    }

    public function productOrderItems()
    {
        return $this->hasMany(ProductOrderItem::class);
    }

    public function applicants()
    {
        return $this->hasMany(UserApplicant::class);
    }

    public function currency()
    {
        return $this->belongsTo(Currency::class);
    }

    public function language()
    {
        return $this->belongsTo(Language::class);
    }

    public function avatar()
    {
        return $this->belongsTo(UploadImage::class);
    }

    public function savedServices(): MorphToMany
    {
        return $this->morphedByMany(Service::class, 'user_savable')->withTimestamps();
    }

    public function savedArtworks(): MorphToMany
    {
        return $this->morphedByMany(Artwork::class, 'user_savable')->withTimestamps();
    }

    public function savedArtists(): MorphToMany
    {
        return $this->morphedByMany(Artist::class, 'user_savable')->withTimestamps();
    }

    public function savedProducts(): MorphToMany
    {
        return $this->morphedByMany(Product::class, 'user_savable')->withTimestamps();
    }

    public function chats(): BelongsToMany
    {
        return $this->belongsToMany(Chat::class, 'chat_user_pivot', 'user_id', 'chat_id')
            ->withPivot('last_read_message_id')
            ->withTimestamps();
    }

    public function groups(): BelongsToMany
    {
        return $this->belongsToMany(Group::class, 'group_user_pivot', 'user_id', 'group_id')
            ->withPivot('last_read_cursor')
            ->withPivot('role')
            ->withPivot('order_at_ts')
            ->withPivot('settings')
            ->withTimestamps();
    }

    public function notifications()
    {
        return $this->morphMany(CustomNotification::class, 'notifiable')
            ->latest();
    }

    public function wallets()
    {
        return $this->hasMany(Wallet::class);
    }

    public function walletTransactions()
    {
        return $this->hasMany(WalletTransaction::class);
    }

    public function walletHistories()
    {
        return $this->hasMany(WalletHistory::class);
    }

    public function walletWithdraws(): HasMany
    {
        return $this->hasMany(WalletWithdraw::class);
    }

    public function withdrawAccounts()
    {
        return $this->hasMany(UserWithdrawAccount::class);
    }

    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    public function workTaskPriceChanges(): HasMany
    {
        return $this->hasMany(WorkTaskPriceChange::class, 'initiator_id')
            ->where('initiator_type', 'user');
    }

    public function walletDeductionRecords()
    {
        return $this->hasMany(WalletDeductionRecord::class);
    }

    public function artistDeductionRecords()
    {
        return $this->hasMany(WalletDeductionRecord::class, 'artist_user_id');
    }
}
