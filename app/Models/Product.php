<?php

namespace App\Models;

use App\Traits\HasSavedStatusTrait;
use App\Traits\TranslateTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Translatable\HasTranslations;

class Product extends Model
{
    use HasFactory, HasTranslations, SoftDeletes, TranslateTrait{
        TranslateTrait::getCasts insteadof HasTranslations;
        TranslateTrait::filterTranslations insteadof HasTranslations;
    }
    use HasSavedStatusTrait;

    protected $translatable = ['name', 'detail'];

    protected $guarded = ['id'];

    protected $appends = [
        'is_saved',
    ];

    public function artists()
    {
        return $this->belongsTo(Artist::class);
    }

    public function showcases(): HasMany
    {
        return $this->hasMany(ProductShowcase::class)->with([
            'artwork' => [
                'uploadImage',
                'uploadVideo',
            ],
            'uploadImage:id,url_sm,url_md,url_lg',
        ])->orderBy('id');
    }

    public function showcase()
    {
        return $this->hasOne(ProductShowcase::class)->with([
            'artwork' => [
                'uploadImage',
                'uploadVideo',
            ],
            'uploadImage:id,url_sm,url_md,url_lg',
        ])->orderBy('id');
    }

    public function currency(): BelongsTo
    {
        return $this->belongsTo(Currency::class);
    }

    public function tags(): BelongsToMany
    {
        return $this->belongsToMany(Tag::class, 'product_tag_pivot')->withTimestamps();
    }

    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(Category::class, 'product_category_pivot')->withTimestamps();
    }

    public function files(): HasMany
    {
        return $this->hasMany(ProductFile::class);
    }

    public function file()
    {
        return $this->hasOne(ProductFile::class);
    }

    public function getIsSavedAttribute()
    {
        return $this->savedUsers()->where('user_id', auth()->id())->exists();
    }
}
