<?php

namespace App\Models;

use App\Traits\HasSavedStatusTrait;
use App\Traits\TranslateTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Notifications\Notifiable;
use Spatie\Translatable\HasTranslations;

class Artist extends Model
{
    use HasFactory;
    use HasSavedStatusTrait;
    use HasTranslations, TranslateTrait{
        TranslateTrait::getCasts insteadof HasTranslations;
        TranslateTrait::filterTranslations insteadof HasTranslations;
    }
    use Notifiable;
    use SoftDeletes;

    protected $translatable = ['intro', 'contract'];

    protected $guarded = ['id'];

    protected $appends = [
        'is_saved',
    ];

    protected $casts = [
        'is_open' => 'boolean',
    ];

    protected $hidden = [
        'worktask_plat_fee_ratio',
        'product_plat_fee_ratio',
        'pay_fee_ratio',
        'recommend_score',
    ];

    public function artworks()
    {
        return $this->hasMany(Artwork::class)->orderBy('sort', 'asc')->orderBy('id', 'desc');
    }

    public function langs()
    {
        return $this->belongsToMany(Language::class, 'artist_lang_pivot', 'language_id', 'artist_id');
    }

    public function services()
    {
        return $this->hasMany(Service::class);
    }

    public function showcases()
    {
        return $this->hasMany(ServiceShowcase::class);
    }

    public function serviceRequests()
    {
        return $this->hasMany(ServiceRequest::class);
    }

    public function tags()
    {
        return $this->morphToMany(Tag::class, 'taggable');
    }

    public function artistRecReviews()
    {
        return $this->hasMany(ArtistRecReview::class);
    }

    /**
     * Get the user that owns the artist.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function workTasks(): HasMany
    {
        return $this->hasMany(WorkTask::class);
    }

    public function workTaskFiles(): HasMany
    {
        return $this->hasMany(WorkTaskFile::class);
    }

    public function uploadImages(): HasMany
    {
        return $this->HasMany(UploadImage::class);
    }

    public static function getPort($artists, $image_num = 4)
    {
        foreach ($artists as $artist) {
            $artist->images = $artist->artworks()->orderBy('sort')->take($image_num)->get();
        }

        return $artists;
    }

    public function userRecReviews(): HasMany
    {
        return $this->hasMany(UserRecReview::class);
    }

    public function projectRequests(): HasMany
    {
        return $this->hasMany(ProjectRequest::class);
    }

    public function products()
    {
        return $this->hasMany(Product::class);
    }

    public function productFiles()
    {
        return $this->hasMany(ProductFile::class);
    }

    public function avatar()
    {
        return $this->belongsTo(UploadImage::class);
    }

    public function inviterArtistCodes()
    {
        return $this->hasMany(ArtistInviteCode::class, 'inviter_artist_id');
    }

    public function cover()
    {
        return $this->belongsTo(UploadImage::class);
    }

    public function translates(): MorphMany
    {
        return $this->morphMany(Translate::class, 'busable');
    }

    public function notifications()
    {
        return $this->morphMany(CustomNotification::class, 'notifiable')->latest();
    }

    public function workTaskPriceChanges(): MorphMany
    {
        return $this->morphMany(WorkTaskPriceChange::class, 'initiator');
    }
}
