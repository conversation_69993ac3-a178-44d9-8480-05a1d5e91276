<?php

namespace App\Models;

use App\Traits\TranslateTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;

class WorkTaskStage extends Model
{
    use HasFactory, HasTranslations, TranslateTrait{
        TranslateTrait::getCasts insteadof HasTranslations;
        TranslateTrait::filterTranslations insteadof HasTranslations;
    }

    const PayStatusUnpaid = 0;

    const PayStatusPaid = 1;

    const WorkStatusPending = 'pending';

    const WorkStatusWorking = 'working';

    const WorkStatusFinished = 'finished';

    const TypeService = 'service';

    const TypeProject = 'project';

    public $guarded = ['id'];

    public $translatable = ['name'];

    public function taskStages()
    {
        return $this->hasMany(WorkTask::class);
    }
}
