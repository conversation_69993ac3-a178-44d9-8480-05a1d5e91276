<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductShowcase extends Model
{
    use HasFactory;

    protected $guarded = ['id'];

    public function artwork()
    {
        return $this->belongsTo(Artwork::class, 'artwork_id');
    }

    public function uploadImage()
    {
        return $this->belongsTo(UploadImage::class, 'upload_image_id');
    }
}
