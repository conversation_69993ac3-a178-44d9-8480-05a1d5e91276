<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * 
 *
 * @property int $id
 * @property int|null $product_id
 * @property int|null $attribute_id
 * @property string|null $option_name
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @method static \Illuminate\Database\Eloquent\Builder|ProductAttributeOption newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductAttributeOption newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductAttributeOption query()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductAttributeOption whereAttributeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductAttributeOption whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductAttributeOption whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductAttributeOption whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductAttributeOption whereOptionName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductAttributeOption whereProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductAttributeOption whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class ProductAttributeOption extends Model
{
    use HasFactory;
    protected $guarded = ['id'];
}
