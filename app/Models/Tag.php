<?php

namespace App\Models;

use App\Enums\TagCategoryType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphToMany;

class Tag extends Model
{
    use HasFactory;

    protected $guarded = ['id'];

    public $casts = ['name' => 'array'];

    protected $hidden = ['pivot', 'created_at', 'updated_at', 'color', 'search', 'display'];

    public function artworks(): MorphToMany
    {
        return $this->morphedByMany(Artwork::class, 'taggable');
    }

    public function artists(): MorphToMany
    {
        return $this->morphedByMany(Artist::class, 'taggable');
    }

    public function services(): MorphToMany
    {
        return $this->morphedByMany(Service::class, 'taggable');
    }

    public function preferServices(): MorphToMany
    {
        return $this->morphedByMany(Service::class, 'taggable')
            ->withPivot('category')
            ->wherePivot('category', TagCategoryType::Prefer);
    }

    public function cantdoServices(): MorphToMany
    {
        return $this->morphedByMany(Service::class, 'taggable')
            ->withPivot('category')
            ->wherePivot('category', TagCategoryType::Cantdo);
    }

    public function tagCategories()
    {
        return $this->belongsToMany(TagCategory::class, 'tag_category_tag_pivot');
    }
}
