<?php

namespace App\Models;

use App\Traits\TranslateTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;

class ProjectManager extends Model
{
    use HasFactory, HasTranslations, TranslateTrait {
        TranslateTrait::getCasts insteadof HasTranslations;
        TranslateTrait::filterTranslations insteadof HasTranslations;
    }

    protected $guarded = ['id'];

    protected $hidden = ['created_at', 'updated_at', 'pivot'];

    public $translatable = ['desc'];

    public $casts = [
        'major' => 'array',
    ];

    public function languages()
    {
        return $this->belongsToMany(Language::class, 'project_manager_language_pivot');
    }

    public function projects()
    {
        return $this->belongsToMany(Project::class, 'project_project_manager_pivot')
            ->withTimestamps();
    }
}
