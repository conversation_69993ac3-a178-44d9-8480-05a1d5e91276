<?php

namespace App\Models;

use App\Traits\TranslateTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Translatable\HasTranslations;

class ProductFile extends Model
{
    use HasFactory,  HasTranslations, SoftDeletes, TranslateTrait{
        TranslateTrait::getCasts insteadof HasTranslations;
        TranslateTrait::filterTranslations insteadof HasTranslations;
    }

    protected $translatable = ['name'];

    protected $guarded = ['id'];

    protected $hidden = ['path', 'pivot', 'created_at', 'updated_at', 'deleted_at'];
}
