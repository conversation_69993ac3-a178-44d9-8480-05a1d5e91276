<?php

namespace App\Models;

use App\Traits\TranslateTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;

class StageTemplate extends Model
{
    use HasFactory, HasTranslations, TranslateTrait {
        TranslateTrait::getCasts insteadof HasTranslations;
        TranslateTrait::filterTranslations insteadof HasTranslations;
    }

    public $translatable = ['name'];

    protected $guarded = ['id'];

    protected $hidden = ['created_at', 'updated_at'];

    public function stages()
    {
        return $this->belongsToMany(Stage::class, 'stage_template_stage_pivot')
            ->withPivot('percent')
            ->withTimestamps();
    }

    public function stagePivots()
    {
        return $this->hasMany(StageTemplateStagePivot::class);
    }
}
