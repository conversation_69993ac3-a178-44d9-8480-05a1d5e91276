<?php

namespace App\Models;

use App\Enums\OrderStatus;
use App\Enums\WorkTaskPriceChangeStatus;
use App\Traits\TranslateTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Spatie\Translatable\HasTranslations;

class WorkTask extends Model
{
    use HasFactory, HasTranslations, TranslateTrait{
        TranslateTrait::getCasts insteadof HasTranslations;
        TranslateTrait::filterTranslations insteadof HasTranslations;
    }

    const TypeService = 'service';

    const TypeProject = 'project';

    const TypeProduct = 'product';

    const StatusPending = 'pending';

    const StatusUserCanceled = 'user_canceled';

    const StatusArtistCanceled = 'artist_canceled';

    const StatusArtistRejected = 'artist_rejected';

    const StatusWaitPay = 'wait_pay';

    const StatusWorking = 'working';

    const StatusFinished = 'finished';

    const BusableProject = 'project';

    const BusableService = 'service';

    public $guarded = ['id'];

    protected $translatable = ['name', 'reject_content'];

    protected $casts = [
        'busable_snap' => 'array',
        'reqable_snap' => 'array',
    ];

    public function reqable(): MorphTo
    {
        return $this->morphTo();
    }

    public function busable(): MorphTo
    {
        return $this->morphTo();
    }

    public function workTaskStages()
    {
        return $this->hasMany(WorkTaskStage::class)
            ->orderBy('percent', 'asc');
    }

    public function workTaskFiles(): HasMany
    {
        return $this->hasMany(WorkTaskFile::class);
    }

    public function workTaskFileChangeRequests(): HasMany
    {
        return $this->hasMany(WorkTaskFileChangeRequest::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function artist()
    {
        return $this->belongsTo(Artist::class);
    }

    public function artistRecReview(): HasOne
    {
        return $this->hasOne(ArtistRecReview::class);
    }

    public function userRecReview(): HasOne
    {
        return $this->hasOne(UserRecReview::class);
    }

    public function currency()
    {
        return $this->belongsTo(Currency::class);
    }

    public function translates(): MorphMany
    {
        return $this->morphMany(Translate::class, 'busable');
    }

    public function group(): HasOne
    {
        return $this->hasOne(Group::class);
    }

    public function stripeCheckoutSession(): HasOne
    {
        return $this->hasOne(StripeCheckoutSession::class)
            ->whereIn('process_status', [StripeCheckoutSession::STATUS_PENDING, StripeCheckoutSession::STATUS_PROCESSING]);
    }

    public function orders(): MorphMany
    {
        return $this->morphMany(Order::class, 'busable');
    }

    public function payingOrder(): MorphOne
    {
        return $this->morphOne(Order::class, 'busable')
            ->where('status', OrderStatus::Paying)
            ->latest();
    }

    public function priceChange(): HasOne
    {
        return $this->hasOne(WorkTaskPriceChange::class)
            ->whereIn('status', [WorkTaskPriceChangeStatus::PENDING, WorkTaskPriceChangeStatus::WAIT_PAY])
            ->latest();
    }

    public function priceChanges(): HasMany
    {
        return $this->hasMany(WorkTaskPriceChange::class);
    }
}
