<?php

namespace App\Models;

use Illuminate\Notifications\DatabaseNotification;

class CustomNotification extends DatabaseNotification
{
    protected static function booted()
    {
        static::creating(function ($model) {
            $model->created_at_ts = $model->freshTimestamp()->getPreciseTimestamp(3);
        });

        static::saving(function ($model) {
            $model->updated_at_ts = $model->freshTimestamp()->getPreciseTimestamp(3);
        });
    }

    public function markAsRead()
    {
        if (is_null($this->read_at)) {
            $this->forceFill([
                'read_at' => $this->freshTimestamp(),
                'read_at_ts' => $this->freshTimestamp()->getPreciseTimestamp(3),
            ])->save();
        }
    }
}
