<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;

class Group extends Model
{
    use HasFactory;
    use Notifiable;

    protected $guarded = ['id'];

    protected $hidden = [
    ];

    protected $casts = [
        'request_admin_join' => 'boolean',
    ];

    public function messages()
    {
        return $this->hasMany(GroupMessage::class);
    }

    public function lastMessage()
    {
        return $this->hasOne(GroupMessage::class)->latest();
    }

    public function users()
    {
        return $this->belongsToMany(User::class, 'group_user_pivot', 'group_id', 'user_id')
            ->withPivot('role')
            ->withPivot('last_read_cursor')
            ->withPivot('settings')
            ->withPivot('order_at_ts')
            ->using(GroupUserPivot::class)
            ->withTimestamps();
    }

    public function workTask()
    {
        return $this->belongsTo(WorkTask::class);
    }

    public function notifications()
    {
        return $this->morphMany(CustomNotification::class, 'notifiable')->latest();
    }
}
