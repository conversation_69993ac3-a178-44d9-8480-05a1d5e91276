<?php

namespace App\Models;

use App\Traits\HasSavedStatusTrait;
use App\Traits\TranslateTrait;
use App\Utils\Utils;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Translatable\HasTranslations;

class Artwork extends Model
{
    use HasFactory, SoftDeletes;
    use HasSavedStatusTrait;
    use HasTranslations;
    use TranslateTrait{
        TranslateTrait::getCasts insteadof HasTranslations;
        TranslateTrait::filterTranslations insteadof HasTranslations;
    }

    protected $translatable = ['title', 'detail'];

    protected $casts = [
        'is_show' => 'boolean',
    ];

    protected $guarded = ['id'];

    protected $hidden = [
        'pivot',
        'deleted_at',
        'recommend_score',
    ];

    protected $appends = [
        'is_saved',
    ];

    public function showcases()
    {
        return $this->hasMany(ServiceShowcase::class);
    }

    public function tags(): MorphToMany
    {
        return $this->morphToMany(Tag::class, 'taggable');
    }

    public function artist()
    {
        return $this->belongsTo(Artist::class);
    }

    protected function urlSm(): Attribute
    {
        return Attribute::make(
            get: fn (string $value) => Utils::storageUrl($value),
        );
    }

    protected function urlMd(): Attribute
    {
        return Attribute::make(
            get: fn (string $value) => Utils::storageUrl($value),
        );
    }

    protected function urlLg(): Attribute
    {
        return Attribute::make(
            get: fn (string $value) => Utils::storageUrl($value),
        );
    }

    public function uploadImage()
    {
        return $this->belongsTo(UploadImage::class);
    }

    public function uploadVideo()
    {
        return $this->belongsTo(UploadVideo::class);
    }

    public function categories()
    {
        return $this->belongsToMany(Category::class, 'artwork_category_pivot');
    }

    public function artStyles()
    {
        return $this->belongsToMany(ArtStyle::class, 'artwork_art_style_pivot');
    }
}
