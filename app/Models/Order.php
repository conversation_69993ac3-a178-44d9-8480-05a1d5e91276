<?php

namespace App\Models;

use App\Enums\OrderStatus;
use App\Service\Stripe\Object\FeeData;
use App\Service\Stripe\Object\PaymentInfoData;
use App\Service\Stripe\Object\StripeFeeData;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Order extends Model
{
    use HasFactory;

    protected $guarded = ['id'];

    protected $casts = [
        'status' => OrderStatus::class,
        'busable_info' => 'array',
        'amount_calc_process' => 'array',
        'payment_info' => PaymentInfoData::class,
        'stripe_fee' => StripeFeeData::class,
        'fee' => FeeData::class,
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function artist(): BelongsTo
    {
        return $this->belongsTo(Artist::class);
    }

    public function currency(): BelongsTo
    {
        return $this->belongsTo(Currency::class);
    }

    public function busable(): MorphTo
    {
        return $this->morphTo();
    }

    public function stripeCheckoutSessions(): HasMany
    {
        return $this->hasMany(StripeCheckoutSession::class);
    }
}
