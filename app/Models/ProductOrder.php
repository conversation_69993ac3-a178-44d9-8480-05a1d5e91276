<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProductOrder extends Model
{
    use HasFactory, SoftDeletes;

    protected $guarded = ['id'];

    protected $hidden = [
        'id',
        'deleted_at',
    ];

    const STATUS_PENDING = 'pending';

    const STATUS_PAID = 'paid';

    public function items()
    {
        return $this->hasMany(ProductOrderItem::class, 'order_id');
    }

    public function item()
    {
        return $this->hasOne(ProductOrderItem::class, 'order_id');
    }

    public function currency()
    {
        return $this->belongsTo(Currency::class, 'currency_id');
    }
}
