<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Translation extends Model
{
    use HasFactory;

    protected $hidden = ['created_at', 'updated_at', 'pivot'];

    public function projects()
    {
        return $this->belongsToMany(Project::class, 'project_translation_pivot');
    }

    public function targetLanguage()
    {
        return $this->hasOne(Language::class, 'id', 'target_language_id');
    }
}
