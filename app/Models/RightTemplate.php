<?php

namespace App\Models;

use App\Traits\TranslateTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;
use <PERSON><PERSON><PERSON>meir\LaravelAdjacencyList\Eloquent\HasRecursiveRelationships;

class RightTemplate extends Model
{
    use HasFactory, HasTranslations, TranslateTrait {
        TranslateTrait::getCasts insteadof HasTranslations;
        TranslateTrait::filterTranslations insteadof HasTranslations;
    }
    use HasRecursiveRelationships;

    public $translatable = [];

    public $casts = ['name' => 'array'];

    protected $guarded = ['id'];

    protected $hidden = [
        'created_at', 'updated_at',
    ];

    public function rights()
    {
        return $this->belongsToMany(Right::class, 'right_template_right_pivot')
            ->withPivot('state');
    }

    public function rightPivots()
    {
        return $this->hasMany(RightTemplateRightPivot::class, 'right_template_id');
    }

    public function services()
    {
        return $this->hasMany(Service::class);
    }

    public function projects()
    {
        return $this->hasMany(Project::class);
    }

    public function getRightTree()
    {
        $rights = Right::query()->tree()->get()->toTree();
        $rightPivots = $this->rightPivots;

        $rightTree = $rights->map(function ($right) use ($rightPivots) {
            $right->state = $rightPivots->where('right_id', $right->id)
                ->where('right_template_id', $this->id)
                ->first()->state;

            $right->children = $right->children->map(function ($child) use ($rightPivots) {
                $child->state = $rightPivots->where('right_id', $child->id)
                    ->where('right_template_id', $this->id)
                    ->first()->state;

                return $child;
            });

            return $right;
        });

        return $rightTree;
    }
}
