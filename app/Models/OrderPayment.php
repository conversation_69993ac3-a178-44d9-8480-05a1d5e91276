<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;


/**
 * 
 *
 * @property int $id
 * @property int|null $order_id
 * @property int|null $user_id
 * @property string|null $name
 * @property string|null $third_party_code
 * @property string|null $pay_amount
 * @property string|null $pay_type
 * @property int|null $status
 * @property int|null $pay_status
 * @property string|null $pay_note
 * @property string|null $paid_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @method static \Illuminate\Database\Eloquent\Builder|OrderPayment newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OrderPayment newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OrderPayment onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|OrderPayment query()
 * @method static \Illuminate\Database\Eloquent\Builder|OrderPayment whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderPayment whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderPayment whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderPayment whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderPayment whereOrderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderPayment wherePaidAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderPayment wherePayAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderPayment wherePayNote($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderPayment wherePayStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderPayment wherePayType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderPayment whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderPayment whereThirdPartyCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderPayment whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderPayment whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderPayment withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|OrderPayment withoutTrashed()
 * @mixin \Eloquent
 */
class OrderPayment extends Model
{
    use HasFactory,SoftDeletes;
    protected $guarded = ['id'];

    public static function statusRead($payments,$collection=1){
      $read_array = array(
        1 => 'No action required',
        2 => 'Waiting for payment',
        3 => 'Paid, thank you',
        4 => 'Payment Confirm',
      );

      if($collection == 1){
        foreach ($payments as $payment){
          $payment->status_text = $read_array[$payment->status];
        }
        return $payments;
      }
      else{
        $payment = $payments;
        $payment->status_text = $read_array[$payment->status];
        return $commission; 
      }
    }
}
