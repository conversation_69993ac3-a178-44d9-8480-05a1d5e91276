<?php

namespace App\Models;

use App\Enums\ChatVisableStatus;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Casts\Json;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\Pivot;

class ChatUserPivot extends Pivot
{
    use HasFactory;

    protected $guarded = ['id'];

    protected $table = 'chat_user_pivot';

    protected $fillable = [
    ];

    protected $hidden = [

    ];

    protected $casts = [
        'chat_visable_status' => ChatVisableStatus::class,
    ];

    protected $defaultSettings = [
        'auto_translate' => false,
    ];

    protected function settings(): Attribute
    {
        return Attribute::make(
            get: function ($value) {
                $settings = Json::decode($value, true) ?: [];

                return array_merge($this->defaultSettings, $settings);
            },
            set: function ($value, $attributes) {
                $settings = Json::decode($attributes['settings'], true) ?: [];
                $merged = array_merge($settings, $value);
                $attributes['settings'] = Json::encode($merged);

                return $attributes;
            }
        );
    }
}
