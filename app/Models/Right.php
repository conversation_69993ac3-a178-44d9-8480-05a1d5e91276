<?php

namespace App\Models;

use App\Traits\TranslateTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;
use <PERSON><PERSON><PERSON>meir\LaravelAdjacencyList\Eloquent\HasRecursiveRelationships;

class Right extends Model
{
    use HasFactory, HasTranslations, TranslateTrait {
        TranslateTrait::getCasts insteadof HasTranslations;
        TranslateTrait::filterTranslations insteadof HasTranslations;
    }
    use HasRecursiveRelationships;

    public $translatable = [];

    public $casts = ['name' => 'array', 'description' => 'array'];

    protected $guarded = ['id'];

    protected $hidden = [
        'created_at', 'updated_at',
    ];

    public function rightTemplates()
    {
        return $this->belongsToMany(RightTemplate::class, 'right_template_right_pivot')->withPivot('state');
    }
}
