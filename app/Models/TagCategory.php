<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TagCategory extends Model
{
    use HasFactory;

    public $hidden = [
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public $casts = [
        'name' => 'array',
    ];

    public function tags()
    {
        return $this->belongsToMany(Tag::class, 'tag_category_tag_pivot', 'tag_category_id', 'tag_id');
    }
}
