<?php

namespace App\Models;

use App\Traits\TranslateTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;

class Stage extends Model
{
    use HasFactory, HasTranslations, TranslateTrait {
        TranslateTrait::getCasts insteadof HasTranslations;
        TranslateTrait::filterTranslations insteadof HasTranslations;
    }

    public $translatable = ['name'];

    protected $guarded = ['id'];

    protected $hidden = ['created_at', 'updated_at'];

    protected $casts = [
        'is_end_stage' => 'boolean',
    ];

    public function projects()
    {
        return $this->belongsToMany(Project::class, 'project_stage_pivot')
            ->withPivot('percent')
            ->withTimestamps();
    }

    public function services()
    {
        return $this->belongsToMany(Service::class, 'service_stage_pivot')
            ->withPivot('percent')
            ->withTimestamps();
    }

    public function templates()
    {
        return $this->belongsToMany(StageTemplate::class, 'stage_template_stage_pivot')
            ->withPivot('percent')
            ->withTimestamps();
    }
}
