<?php

namespace App\Services;

use App\Enums\ErrorCode;
use App\Enums\GroupReminderStatus;
use App\Enums\SystemNotificationScene;
use App\Exceptions\BusinessException;
use App\Jobs\SendReminderEmail;
use App\Models\Group;
use App\Models\GroupReminder;
use App\Models\User;
use App\Notifications\SystemNotification;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class GroupReminderService
{
    const HOURS_24_LIMIT = 3;

    public static function New()
    {
        return new self;
    }

    public function createReminder(
        User $sender,
        Group $group,
        int $templateId,
        array $mentionedUserIds,
        ?Carbon $remindAt = null
    ): ?GroupReminder {
        // 检查24小时内发送限制
        if (! $this->checkHoursLimit($sender)) {
            throw new BusinessException(ErrorCode::GroupReminderLimit, "超出24小时内提醒次数 " . self::HOURS_24_LIMIT . " 次限制");
        }

        // 验证被提醒用户是否在群组中
        $groupUserIds = $group->users()->pluck('users.id')->toArray();
        $invalidUserIds = array_diff($mentionedUserIds, $groupUserIds);
        if (! empty($invalidUserIds)) {
            throw new BusinessException(ErrorCode::GroupReminderExistInvalidUser, '存在非群组成员的用户');
        }

        return DB::transaction(function () use ($sender, $group, $templateId, $mentionedUserIds, $remindAt) {
            // 创建提醒记录
            $reminder = GroupReminder::create([
                'group_id' => $group->id,
                'sender_id' => $sender->id,
                'template_id' => $templateId,
                'mentioned_user_ids' => $mentionedUserIds,
                'remind_at' => $remindAt ?? now(),
                'status' => GroupReminderStatus::Pending,
            ]);

            // 分发发送邮件任务
            foreach ($mentionedUserIds as $userId) {
                SendReminderEmail::dispatch($reminder, $userId);
            }

            // 发送通知
            $reciver_names = collect([]);
            foreach ($mentionedUserIds as $userId) {
                $reciver = User::find($userId);
                $reciver_name = $reciver->name;
                if ($reciver->hasRole('artist')) {
                    $reciver_name = $reciver->artist->name;
                }
                $reciver_names->push($reciver_name);
            }
            $reciver_names_str = $reciver_names->implode(',');

            $notify = SystemNotification::make()
                ->setScene(SystemNotificationScene::GroupReminder)
                ->setMeta([
                    'group_id' => $group->id,
                    'reminder_id' => $reminder->id,
                    'sender_name' => $sender->name,
                    'reciver_names' => $reciver_names_str,
                    'reminder_msg' => $reminder->template->getOriginal('title'),
                ]);

            $group->notify($notify);

            return $reminder;
        });
    }

    private function checkHoursLimit(User $user): bool
    {
        $last24Hours = Carbon::now()->subHours(24);

        $count = GroupReminder::where('sender_id', $user->id)
            ->where('created_at', '>=', $last24Hours)
            ->count();

        return $count < self::HOURS_24_LIMIT;
    }
}
