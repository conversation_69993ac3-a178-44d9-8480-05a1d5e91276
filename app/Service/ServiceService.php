<?php

namespace App\Service;

use App\Models\Service;

class ServiceService
{
    public static function updateServiceBasePrice(Service $service): Service
    {
        $price = $service->price;
        $currency_code = $service->currency->code;
        $base_currency = Service::BASE_PRICE_CURRENCY_CODE;

        $base_currency_price = RateService::new()->amountExchange($price, $currency_code, $base_currency);

        $service->base_currency_price = $base_currency_price;
        $service->timestamps = false;
        $service->save();

        return $service;
    }
}
