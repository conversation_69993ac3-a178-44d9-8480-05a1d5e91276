<?php

namespace App\Service;

use App\Models\Artist;
use Cache;

class ArtistService
{
    public static function getRatingScoreKey($id): string
    {
        return 'artist_service:artist_rating_score_'.$id;
    }

    public static function getRatingScore(Artist $artist): float
    {
        $rating_score = Cache::remember(self::getRatingScoreKey($artist->id), 60 * 60, function () use ($artist) {
            return $artist->artistRecReviews()->where('is_deleted', 0)->avg('rating_score');
        });

        return (float) sprintf('%.2f', $rating_score);
    }
}
