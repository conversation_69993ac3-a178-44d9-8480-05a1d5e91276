<?php

namespace App\Service;

use App\Enums\ArtistInviteCodeStatus;
use App\Enums\ArtistInviteGiftType;
use App\Enums\UserApplicantStatus;
use App\Enums\WalletTransactionAction;
use App\Enums\WalletTransactionBizType;
use App\Enums\WalletTransactionStatus;
use App\Enums\WalletUsage;
use App\Models\Artist;
use App\Models\ArtistInviteCode;
use App\Models\ArtistInviteGift;
use App\Models\Currency;
use App\Models\ModelHasRoles;
use App\Models\Role;
use App\Models\User;
use App\Models\UserApplicant;
use App\Models\Wallet;
use App\Service\Dto\InviteGiftData;
use App\Service\DTO\InviteGiftData\InviteeInviteCode;
use Str;

class UserApplicantService
{
    public static function becomeArtist(User $user, ?ArtistInviteCode $inviteCode = null, ?UserApplicant $userApplicant = null)
    {
        if ($inviteCode && $userApplicant) {
            if ($userApplicant->artist_invite_code_id !== $inviteCode->id) {
                return abort(403, 'Invite code not match');
            }
        }

        // update user applicant status
        if ($userApplicant) {
            $userApplicant->update([
                'status' => UserApplicantStatus::Accepted,
            ]);
        }

        // create artist
        $langCode = $user->language->code ?? 'en';
        $artist = Artist::query()->firstOrCreate([
            'user_id' => $user->id,
        ], [
            'intro' => [
                'en' => '',
                'ja' => '',
                'zh' => '',
                '_lang' => $langCode,
            ],
            'contract' => [
                'en' => '',
                'ja' => '',
                'zh' => '',
                '_lang' => $langCode,
            ],
        ]);

        $artist_id = $artist->id;
        $artist->name = "pipi{$artist_id}";
        $artist->save();

        // create wallet
        foreach (Currency::CanWithdrawCurrencies as $code) {
            $currency = Currency::query()->where('code', $code)->first();
            if (! $currency) {
                continue;
            }
            Wallet::query()->firstOrCreate([
                'user_id' => $user->id,
                'currency_id' => $currency->id,
                'usage' => WalletUsage::Deposit,
            ]);
        }

        // assign artist role
        $role = Role::query()->where('name', 'artist')->first();
        ModelHasRoles::query()->firstOrCreate([
            'role_id' => $role->id,
            'model_type' => 'App\Models\User',
            'model_id' => $user->id,
        ]);

        if ($userApplicant) {
            // create artist artworks
            $uploadImageIds = $userApplicant->uploadImages->pluck('id');
            foreach ($uploadImageIds as $uploadImageId) {
                $artist->artworks()->create([
                    'title' => [
                        'en' => '',
                        'ja' => '',
                        'zh' => '',
                        '_lang' => $langCode,
                    ],
                    'detail' => [
                        'en' => '',
                        'ja' => '',
                        'zh' => '',
                        '_lang' => $langCode,
                    ],
                    'upload_type' => 'upload_image',
                    'upload_image_id' => $uploadImageId,
                ]);
            }
        }

        if ($inviteCode) {
            // update invite code status
            $inviteCode->update([
                'invitee_user_id' => $user->id,
                'invitee_artist_id' => $artist->id,
                'status' => ArtistInviteCodeStatus::Used,
                'used_at' => now(),
            ]);

            // send gifts
            $inviteCodeType = $inviteCode->inviteCodeType;
            $inviteGifts = $inviteCodeType->inviteGifts;

            foreach ($inviteGifts as $inviteGift) {
                if ($inviteGift->type === ArtistInviteGiftType::INVITEE_CREDIT) {
                    self::sendGiftToWallet($inviteCode->inviteeUser, $inviteGift, $inviteCode, WalletUsage::Credit);
                }

                if ($inviteGift->type === ArtistInviteGiftType::INVITER_CREDIT) {
                    self::sendGiftToWallet($inviteCode->inviterUser, $inviteGift, $inviteCode, WalletUsage::Credit);
                }

                if ($inviteGift->type === ArtistInviteGiftType::INVITEE_DEPOSIT) {
                    self::sendGiftToWallet($inviteCode->inviteeUser, $inviteGift, $inviteCode, WalletUsage::Deposit);
                }

                if ($inviteGift->type === ArtistInviteGiftType::INVITER_DEPOSIT) {
                    self::sendGiftToWallet($inviteCode->inviterUser, $inviteGift, $inviteCode, WalletUsage::Deposit);
                }
                if ($inviteGift->type === ArtistInviteGiftType::INVITEE_INVITE_CODE) {
                    self::sendGiftWithInviteCode($inviteCode->inviteeUser, $artist, $inviteGift);
                }
                if ($inviteGift->type === ArtistInviteGiftType::INVITEE_PLAT_FEE_DEDUCT) {
                    self::sendGiftToWallet($inviteCode->inviteeUser, $inviteGift, $inviteCode, WalletUsage::PlatFee);
                }
                if ($inviteGift->type === ArtistInviteGiftType::INVITER_PLAT_FEE_DEDUCT) {
                    self::sendGiftToWallet($inviteCode->inviterUser, $inviteGift, $inviteCode, WalletUsage::PlatFee);
                }
            }

        }

    }

    private static function sendGiftToWallet(User $user, ArtistInviteGift $inviteGift, ArtistInviteCode $inviteCode, WalletUsage $usage)
    {
        $wallet = Wallet::query()->firstOrCreate([
            'user_id' => $user->id,
            'usage' => $usage,
            'currency_id' => $inviteGift->currency_id,
        ]);

        $beforeBalance = $wallet->balance;
        $afterBalance = $beforeBalance + $inviteGift->amount;
        $wallet->balance = $afterBalance;
        $wallet->save();

        $wallet->transactions()->create([
            'user_id' => $user->id,
            'currency_id' => $inviteGift->currency_id,
            'action' => WalletTransactionAction::Plus,
            'biz_type' => WalletTransactionBizType::InviteGift,
            'status' => WalletTransactionStatus::Successed,
            'amount' => $inviteGift->amount,
            'before_balance' => $beforeBalance,
            'after_balance' => $afterBalance,
            'detail' => [
                'invite_code_id' => $inviteCode->id,
                'invite_code_type_id' => $inviteCode->inviteCodeType->id,
            ],
        ]);
    }

    public static function sendGiftWithInviteCode(User $user, Artist $artist, ArtistInviteGift $inviteGift)
    {
        if ($inviteGift->type !== ArtistInviteGiftType::INVITEE_INVITE_CODE) {
            return;
        }

        $config = InviteeInviteCode::from($inviteGift->config);
        $inviter_artist_id = $artist->id;
        $inviter_user_id = $user->id;

        for ($i = 0; $i < $config->generate_count; $i++) {
            $code = self::generateUniqueCode();

            ArtistInviteCode::create([
                'inviter_user_id' => $inviter_user_id,
                'inviter_artist_id' => $inviter_artist_id,
                'status' => ArtistInviteCodeStatus::CanUse->value,
                'invite_code_type_id' => $config->invite_code_type_id,
                'code' => $code,
            ]);
        }

        return;
    }

    protected static function generateUniqueCode(): string
    {
        $attempts = 0;
        do {
            $code = strtolower(Str::random(6));
            $exists = ArtistInviteCode::query()->whereCode($code)->exists();
            $attempts++;
        } while ($exists && $attempts < 100);

        if ($attempts >= 100) {
            throw new \RuntimeException('Could not generate unique artist invite code after 100 attempts');
        }

        return $code;
    }
}
