<?php

namespace App\Service;

use App\Enums\TranslateStatus;
use App\Models\GptPrompt;
use App\Models\Translate;
use Auth;
use DB;
use GuzzleHttp\Client;
use GuzzleHttp\Promise;
use Log;
use OpenAI;
use OpenAI\Laravel\Exceptions\ApiKeyIsMissing;
use OpenAI\Responses\Chat\CreateResponse;
use OpenAI\Responses\Meta\MetaInformation;
use Rajentrivedi\TokenizerX\TokenizerX;

class TransService
{
    public static function translateColumnWithAI($key, $source_lang, $target_lang)
    {
        [$table, $id, $column, $keyStr] = self::parseKey($key);

        $data = DB::table($table)->where('id', $id)->first();
        $source = $data->$column;
        $sourceJson = json_decode($source, true);

        // get promt
        $table_column = "$table.$column";
        $GptPrompt = GptPrompt::where('table_column', $table_column)->first();
        $lang = locale_get_display_name($target_lang, 'en');
        $defaultPromt = "You are a project manager who translate the client requirement for art commission to language {$lang} for the artist";
        $promt = $GptPrompt->promt ?? $defaultPromt;

        $sourceText = $sourceJson[$source_lang];
        $result = self::translate($sourceText, $promt);
        $targetText = $result->choices[0]->message->content;
        $totalToken = $result->usage->totalTokens;

        // update target
        $sourceJson[$target_lang] = $targetText;
        DB::table($table)->where('id', $id)->update([$column => $sourceJson]);

        return [$source, $targetText, $totalToken];
    }

    public static function translate($text, $promt)
    {
        $data = [
            'model' => 'gpt-3.5-turbo',
            'messages' => [
                ['role' => 'system', 'content' => $promt],
                ['role' => 'user', 'content' => $text],

            ],
        ];

        $ret = self::client()->chat()->create($data);

        return $ret;
    }

    public static function handleHumanTranslation($keys, $source_lang, $target_langs)
    {
        $translates = [];
        foreach ($keys as $key) {
            [$table, $id, $column, $keyDecode] = TransService::parseKey($key);
            $data = DB::table($table)->where('id', $id)->first();
            $source = $data->$column;
            $sourceJson = json_decode($source, true);

            foreach ($target_langs as $target_lang) {
                $translate = Translate::create([
                    'user_id' => Auth::id(),
                    'key' => $key,
                    'key_decode' => $keyDecode,
                    'translate_type' => 'human',
                    'source_lang' => $source_lang,
                    'target_lang' => $target_lang,
                    'source' => $sourceJson,
                    'target_text' => '',
                    'status' => TranslateStatus::Pending,
                ]);

                $translates[] = $translate;
            }
        }

        return $translates;
    }

    public static function translateColumnByIds($ids)
    {
        $startTimes = microtime(true);

        $translationRequests = self::generateTranslationRequestsByIds($ids);
        $translationResponses = self::translateMultiByOPenAI($translationRequests, config('openai.model'));

        $elapsedTime = microtime(true) - $startTimes;
        Log::info([
            'elapsedTime' => $elapsedTime,
            'translationCount' => count($translationRequests),
        ]);

        $translations = [];
        foreach ($translationResponses as $translationResponse) {
            /** @var CreateResponse $data */
            $data = $translationResponse['data'];
            $meta = $translationResponse['meta'];

            $targetText = $data->choices[0]->message->content;
            $totalToken = $data->usage->totalTokens;

            // update target
            $table = $meta['table'];
            $id = $meta['id'];
            $column = $meta['column'];
            $target_lang = $meta['target_lang'];
            $translate_id = $meta['translate_id'];

            DB::table($table)->where('id', $id)->update(["$column->$target_lang" => $targetText]);

            // update translate
            $translate = Translate::where('id', $translate_id)
                ->update([
                    'target_text' => $targetText,
                    'batteries' => self::calcBatteries($totalToken),
                    'total_token' => $totalToken,
                    'status' => TranslateStatus::Successed,
                ]);

            $translations[] = $translate;
        }

        return $translations;
    }

    public static function translateColumnByKeys($keys, $source_lang, $target_langs, $user_id)
    {
        $startTimes = microtime(true);

        $translationRequests = self::generateTranslationRequests($keys, $source_lang, $target_langs);
        $translationResponses = self::translateMultiByOPenAI($translationRequests, config('openai.model'));

        $elapsedTime = microtime(true) - $startTimes;
        Log::info([
            'elapsedTime' => $elapsedTime,
            'translationCount' => count($translationRequests),
        ]);

        $translations = [];
        foreach ($translationResponses as $translationResponse) {
            /** @var CreateResponse $data */
            $data = $translationResponse['data'];
            $meta = $translationResponse['meta'];

            $targetText = $data->choices[0]->message->content;
            $totalToken = $data->usage->totalTokens;

            // update target
            $sourceJson = $meta['source_json'];
            $table = $meta['table'];
            $id = $meta['id'];
            $column = $meta['column'];
            $target_lang = $meta['target_lang'];
            $key = $meta['key'];
            $keyDecode = $meta['key_decode'];

            DB::table($table)->where('id', $id)->update(["$column->$target_lang" => $targetText]);

            // create
            $translate = Translate::updateOrCreate([
                'key' => $key,
            ], [
                'user_id' => $user_id,
                'key' => $key,
                'key_decode' => $keyDecode,
                'translate_type' => 'gpt',
                'source_lang' => $source_lang,
                'target_lang' => $target_lang,
                'source' => $sourceJson,
                'target_text' => $targetText,
                'batteries' => self::calcBatteries($totalToken),
                'total_token' => $totalToken,
                'status' => TranslateStatus::Successed,
            ]);

            $translations[] = $translate;
        }

        return $translations;
    }

    public static function translateAsyncHandle($id)
    {
        $translate = Translate::find($id);

        $keys = [$translate->key];
        $source_lang = $translate->source_lang;
        $target_langs = [$translate->target_lang];
        $user_id = $translate->user_id;

        self::translateColumnByKeys($keys, $source_lang, $target_langs, $user_id);
    }

    public static function generateKeyStr($table, $id, $column)
    {
        return "{$table}:{$id}:{$column}";
    }

    public static function generateKeyByStr($keyStr)
    {
        return aes128Encrypt($keyStr);
    }

    public static function generateKey($table, $id, $column)
    {
        return aes128Encrypt(self::generateKeyStr($table, $id, $column));
    }

    public static function createTranslateByModel($model, $field, $source_lang, $target_lang, $user_id)
    {
        $keyStr = TransService::generateKeyStr($model->getTable(), $model->getKey(), $field);
        $key = TransService::generateKeyByStr($keyStr);

        $translate = Translate::create([
            'user_id' => $user_id,
            'key' => $key,
            'key_decode' => $keyStr,
            'translate_type' => 'gpt',
            'source_lang' => $source_lang,
            'target_lang' => $target_lang,
            'source' => json_decode($model->getRawOriginal($field)),
            'status' => TranslateStatus::Pending,
        ]);

        return $translate;
    }

    public static function generateTranslationRequestsByIds($translateIds)
    {
        $translates = Translate::whereIn('id', $translateIds)->get();

        return self::generateTranslationRequestsByTranslateModels($translates);
    }

    public static function generateTranslationRequestsByTranslateModels($translates)
    {
        $translationRequests = [];
        foreach ($translates as $translate) {
            $translationRequests[] = self::generateTranslationRequestsByTranslateModel($translate);
        }

        return $translationRequests;
    }

    public static function generateTranslationRequestsByTranslateModel($translate)
    {
        $key = $translate->key;
        $source_lang = $translate->source_lang;
        $target_lang = $translate->target_lang;

        [$table, $id, $column, $keyDecode] = self::parseKey($key);
        $data = DB::table($table)->where('id', $id)->first();
        $source = $data->$column;
        $sourceJson = json_decode($source, true);

        // get promt
        $table_column = "$table.$column";

        $GptPrompt = GptPrompt::where('table_column', $table_column)->first();

        if (! $GptPrompt) {
            $GptPrompt = GptPrompt::where('table_column', 'default')->first();
        }

        $lang = locale_get_display_name($target_lang, 'en');
        $lang_prompt = 'Target language : '.$lang.'.';
        // $defaultPromt = "You are a project manager who translate the client requirement for art commission to language {$lang} for the artist";
        $promt = $GptPrompt.$lang_prompt;

        return [
            'text' => $sourceJson[$source_lang],
            'promt' => $promt,
            'meta' => [
                'translate_id' => $translate->id,
                'table' => $table,
                'id' => $id,
                'column' => $column,
                'source_json' => $sourceJson,
                'target_lang' => $target_lang,
                'source_lang' => $source_lang,
                'key' => $key,
                'key_decode' => $keyDecode,
            ],
        ];

    }

    public static function generateTranslationRequests($keys, $source_lang, $target_langs)
    {
        $translationRequests = [];
        foreach ($keys as $key) {
            [$table, $id, $column, $keyDecode] = self::parseKey($key);
            $data = DB::table($table)->where('id', $id)->first();
            $source = $data->$column;
            $sourceJson = json_decode($source, true);

            // get promt
            $table_column = "$table.$column";

            $GptPrompt = GptPrompt::where('table_column', $table_column)->first();

            if (! $GptPrompt) {
                $GptPrompt = GptPrompt::where('table_column', 'default')->first();
            }

            foreach ($target_langs as $target_lang) {
                $lang = locale_get_display_name($target_lang, 'en');
                $lang_prompt = 'Target language : '.$lang.'.';
                // $defaultPromt = "You are a project manager who translate the client requirement for art commission to language {$lang} for the artist";
                $promt = $GptPrompt.$lang_prompt;

                $translationRequests[] = [
                    'text' => $sourceJson[$source_lang],
                    'promt' => $promt,
                    'meta' => [
                        'table' => $table,
                        'id' => $id,
                        'column' => $column,
                        'source_json' => $sourceJson,
                        'target_lang' => $target_lang,
                        'source_lang' => $source_lang,
                        'key' => $key,
                        'key_decode' => $keyDecode,
                    ],
                ];
            }
        }

        return $translationRequests;
    }

    public static function translateMultiByOPenAI($translationRequests, $model = 'gpt-3.5-turbo')
    {
        $client = self::GuzzleHttpClient();
        $promises = [];

        foreach ($translationRequests as $request) {
            $text = $request['text'];
            $promt = $request['promt'];

            $data = [
                'model' => $model,
                'messages' => [
                    ['role' => 'system', 'content' => $promt],
                    ['role' => 'user', 'content' => $text],
                ],
            ];

            $promises[] = $client->postAsync('/v1/chat/completions', [
                'json' => $data,
            ])->then(function ($result) use ($request) {
                $respBody = json_decode($result->getBody(), true);
                $data = CreateResponse::from($respBody, MetaInformation::from($result->getHeaders()));

                $res['data'] = $data;
                $res['meta'] = $request['meta'];

                return $res;
            });
        }

        $results = Promise\Utils::unwrap($promises);

        return $results;
    }

    public static function GuzzleHttpClient()
    {
        $apiKey = config('openai.api_key');
        $organization = config('openai.organization');

        if (! is_string($apiKey) || ($organization !== null && ! is_string($organization))) {
            throw ApiKeyIsMissing::create();
        }

        return new Client([
            'base_uri' => 'https://api.openai.com',
            'headers' => [
                'Authorization' => 'Bearer '.$apiKey,
                'OpenAI-Beta' => 'assistants=v2',
                'Content-Type' => 'application/json',
                'Organization' => $organization,
            ],
            'timeout' => config('openai.request_timeout', 2),
            'verify' => false,
            'proxy' => config('openai.proxy', ''),
        ]);
    }

    public static function client()
    {
        $apiKey = config('openai.api_key');
        $organization = config('openai.organization');

        if (! is_string($apiKey) || ($organization !== null && ! is_string($organization))) {
            throw ApiKeyIsMissing::create();
        }

        return OpenAI::factory()
            ->withApiKey($apiKey)
            ->withOrganization($organization)
            ->withHttpHeader('OpenAI-Beta', 'assistants=v2')
            ->withHttpClient(new \GuzzleHttp\Client([
                'timeout' => config('openai.request_timeout', 2),
                'verify' => false,
                'proxy' => config('openai.proxy', ''),
            ]))
            ->make();
    }

    public static function parseKey($key)
    {
        $keyStr = aes128Decrypt($key);
        $columns = explode(':', $keyStr);
        $table = $columns[0];
        $id = $columns[1];
        $column = $columns[2];

        return [$table, $id, $column, $keyStr];
    }

    public static function preCalcBatteries($text)
    {
        $tokenCount = self::countTokens($text);
        $batteries = self::calcBatteries($tokenCount);

        return $batteries;
    }

    public static function calcBatteries($tokenCount)
    {
        $batteries = ceil(($tokenCount + 50) * 2 / 1000);
        if ($batteries < 1) {
            $batteries = 1;
        }

        return $batteries;
    }

    public static function countTokens($text, $model = 'gpt-4')
    {
        $count = TokenizerX::count($text, $model);

        return $count;
    }
}
