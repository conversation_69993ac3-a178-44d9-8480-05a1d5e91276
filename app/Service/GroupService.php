<?php

namespace App\Service;

use App\Enums\GroupUserPivotRole;
use App\Models\Group;
use App\Models\User;
use App\Models\WorkTask;

class GroupService
{
    public function createGroupByWorkTask(WorkTask $workTask)
    {
        $group = new Group();
        $group->name = "Creation-{$workTask->id}";
        $group->work_task_id = $workTask->id;
        $group->save();

        $group->users()->attach($workTask->user_id, [
            'role' => GroupUserPivotRole::User,
            'order_at_ts' => now()->getTimestampMs(),
        ]);
        $group->users()->attach($workTask->artist->user_id, [
            'role' => GroupUserPivotRole::Artist,
            'order_at_ts' => now()->getTimestampMs(),
        ]);

        return $group;
    }

    public function attachUserToGroup(Group $group, User $user, GroupUserPivotRole $role = GroupUserPivotRole::User)
    {
        $group->users()->attach($user->id, [
            'role' => $role,
        ]);
    }

    public function detachUserFromGroup(Group $group, User $user)
    {
        $group->users()->detach($user->id);
    }

    public function triggerUserOrder(Group $group, User $user)
    {
        $group->users()->where('user_id', $user->id)->update([
            'order_at_ts' => now()->getTimestampMs(),
        ]);
    }

    public function triggerOrder(Group $group)
    {
        $group->users()->update([
            'order_at_ts' => now()->getTimestampMs(),
        ]);
    }
}
