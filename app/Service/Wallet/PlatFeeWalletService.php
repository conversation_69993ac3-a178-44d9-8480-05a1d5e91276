<?php

namespace App\Service\Wallet;

use App\Enums\WalletUsage;
use App\Models\User;
use App\Models\Wallet;
use App\Service\RateService;
use App\Service\Stripe\Amount;
use App\Service\Stripe\ProcessStage\WalletBalanceDeductionStage;

class PlatFeeWalletService
{
    public function minus(User $user, Amount $amount): ?WalletBalanceDeductionStage
    {
        $wallet = Wallet::query()
            ->where('user_id', $user->id)
            ->where('usage', WalletUsage::PlatFee)
            ->whereNot('balance', 0)
            ->first();

        if (! $wallet) {
            return null;
        }

        if ($wallet->balance == 0) {
            return null;
        }

        $amount_before = $amount->clone();
        $amount_after = $amount->clone();
        $exchange_currency = $amount->currency;
        $wallet_balance = $wallet->balance;
        $wallet_balance_currency = $wallet->currency;
        if ($wallet_balance <= 0) {
            return null;
        }

        $wallet_exchange_balance = RateService::new()->amountExchange($wallet_balance, $wallet_balance_currency->code, $exchange_currency->code, 'floor');

        if ($amount_before->amount - $wallet_exchange_balance > 0) {
            $amount_minus = $wallet_exchange_balance;
            $amount_after->amount = $amount_before->amount - $amount_minus;
            $wallet_minus = $wallet_balance;
            $wallet_balance_after = 0;
        } else {
            $amount_minus = $amount_before->amount;
            $amount_after->amount = 0;
            $wallet_minus = RateService::new()->amountExchange($amount_minus, $exchange_currency->code, $wallet_balance_currency->code, 'ceil');
            $wallet_balance_after = $wallet_balance - $wallet_minus;
        }

        $processStage = WalletBalanceDeductionStage::new($amount_before, $amount_after);
        $processStage->wallet_id = $wallet->id;
        $processStage->wallet_balance = Amount::new($wallet_balance, $wallet_balance_currency);
        $processStage->wallet_exchange_balance = Amount::new($wallet_exchange_balance, $exchange_currency);
        $processStage->wallet_minus = Amount::new($wallet_minus, $wallet_balance_currency);
        $processStage->wallet_balance_after = Amount::new($wallet_balance_after, $wallet_balance_currency);
        $processStage->amount_minus = Amount::new($amount_minus, $exchange_currency);

        return $processStage;
    }
}
