<?php

namespace App\Service;

use App\Models\CurrencyRate;

class RateService
{
    public function __construct()
    {
    }

    public static function new()
    {
        return new self();
    }

    public function all()
    {
        $data = CurrencyRate::get();

        foreach ($data as $item) {
            if ($item->base_code === 'USD' && $item->target_code === 'CNY') {
                $item->rate = $item->rate / 1.02;
            }
        }

        return $data;
    }

    private function getRateBaseUsd($target_code)
    {
        if ($target_code === 'USD') {
            return 1;
        }

        $rate = CurrencyRate::where('base_code', 'USD')->where('target_code', $target_code)->first()->rate;
        if (! $rate) {
            abort(400, "Currency rate USD to $target_code not found");
        }

        if ($target_code === 'CNY') {
            return $rate / 1.02;
        }

        return $rate;
    }

    public function getRate($source_code, $target_code)
    {
        if ($source_code === $target_code) {
            return 1;
        }

        // transfer amount to usd
        $to_usd_rate = $this->getRateBaseUsd($source_code);
        // transfer usd to target currency
        $to_target_rate = $this->getRateBaseUsd($target_code);

        $rate = $to_target_rate / $to_usd_rate;

        return $rate;
    }

    public function amountExchange($amount, $source_code, $target_code, $type = 'ceil'): int
    {
        if ($source_code === $target_code) {
            return $amount;
        }

        $rate = $this->getRate($source_code, $target_code);

        $amount = $this->amountExchangeByRate($amount, $rate, $type);

        return $amount;
    }

    public function amountExchangeByRate($amount, $rate, $type = 'ceil'): int
    {
        $amount = $amount * $rate;

        // $type ceil, floor, round
        if ($type == 'ceil') {
            return (int) ceil($amount);
        }
        if ($type == 'floor') {
            return (int) floor($amount);
        }

        return (int) round($amount);
    }
}
