<?php

namespace App\Service;

use App\Utils\Captcha;
use Cache;

class CaptchaSservice
{
    public static function Generate(): array
    {
        $captcha = new Captcha;

        $captcha_img = $captcha->showImg();
        $key = uniqid('captcha:');

        Cache::put($key, $captcha->getCaptcha(), 600);

        return [
            'captcha_img' => $captcha_img,
            'key' => $key,
        ];
    }

    public static function Verify(string $key, string $captcha_value): bool
    {
        if (strtolower(Cache::get($key)) != strtolower($captcha_value)) {
            return false;
        }

        Cache::delete($key);

        return true;
    }
}
