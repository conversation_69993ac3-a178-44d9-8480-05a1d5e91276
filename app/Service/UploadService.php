<?php

namespace App\Service;

use App\Jobs\CompressImage;
use App\Jobs\DeleteUploadFile;
use App\Models\UploadFile;
use App\Models\UploadImage;
use App\Models\UploadVideo;
use Carbon\Carbon;
use Illuminate\Http\UploadedFile;
use Image;
use Log;
use Storage;

class UploadService
{
    private $disk;

    private $user_id;

    public static function new()
    {
        $instance = new UploadService();
        $instance->setUserId(auth()->user()->id);

        return $instance;
    }

    public function __construct()
    {
        $this->disk = config('filesystems.default');

        return $this;
    }

    public function getDisk()
    {
        return $this->disk;

        return $this;
    }

    public function setDisk(string $disk)
    {
        $this->disk = $disk;

        return $this;
    }

    public function setUserId(int $userId)
    {
        $this->user_id = $userId;
    }

    public function getUserId()
    {
        return $this->user_id;
    }

    public function processUploadImage(UploadedFile $uploadedFile, string $imageFolder, UploadImage $uploadImage)
    {
        $imageName = uniqid();

        $imgPath = $this->storeOriginalFile($uploadedFile, $imageFolder, $imageName);
        // $image = Image::read($uploadedFile);

        $uploadImage->url_og = $imgPath;
        $uploadImage->url_sm = $imgPath;
        $uploadImage->url_md = $imgPath;
        $uploadImage->url_lg = $imgPath;
        $uploadImage->width = 0;
        $uploadImage->height = 0;
        $uploadImage->save();
        Log::info("Original image stored: {$imgPath}");

        CompressImage::dispatch($uploadImage->id)->onQueue('compress_image');
    }

    public function processUploadVideo(UploadedFile $uploadedFile, string $imageFolder, UploadVideo $uploadVideo)
    {
        $imageName = uniqid();

        $imgPath = $this->storeOriginalFile($uploadedFile, $imageFolder, $imageName);

        $uploadVideo->url_og = $imgPath;
        $uploadVideo->url_sm = $imgPath;
        $uploadVideo->url_md = $imgPath;
        $uploadVideo->url_lg = $imgPath;
        $uploadVideo->save();
        Log::info("Original image stored: {$imgPath}");
    }

    public function processUploadFile(UploadedFile $uploadedFile, UploadFile $uploadFile)
    {
        $fileName = uniqid();

        $folder = $this->generateFolderPath($uploadFile->scene);

        $filePath = $this->storeOriginalFile($uploadedFile, $folder, $fileName);

        $uploadFile->url_og = $filePath;
        $uploadFile->save();

        Log::info("Original file stored: {$filePath}");
    }

    public function generateFolderPath(string $scene = 'default', string $topFolder = 'upload_files')
    {
        $date = Carbon::now();
        $formattedDate = $date->format('Y-m-d');

        $user_id = $this->getUserId();

        $imageFolder = "{$topFolder}/user_{$user_id}/{$scene}/{$formattedDate}/";

        return $imageFolder;
    }

    private function storeOriginalFile(UploadedFile $uploadedFile, string $folder, string $name): string
    {
        $extension = $uploadedFile->extension();
        $fileName = "{$name}_og.{$extension}";
        $filePath = "{$folder}{$fileName}";
        Storage::disk($this->disk)->putFileAs($folder, $uploadedFile, $fileName);

        return $filePath;
    }

    public static function deleteUploadImage($uploadImageId)
    {
        $uploadImage = UploadImage::find($uploadImageId);
        if (! $uploadImage) {
            return;
        }

        $uploadImage->delete();

        DeleteUploadFile::dispatch([
            $uploadImage->url_og,
            $uploadImage->getRawOriginal('url_sm'),
            $uploadImage->getRawOriginal('url_md'),
            $uploadImage->getRawOriginal('url_lg'),
        ]);
    }

    public static function deleteUploadVideo($uploadVideoId)
    {
        $uploadVideo = UploadVideo::find($uploadVideoId);
        if (! $uploadVideo) {
            return;
        }

        $uploadVideo->delete();

        DeleteUploadFile::dispatch([
            $uploadVideo->url_og,
            $uploadVideo->getRawOriginal('url_sm'),
            $uploadVideo->getRawOriginal('url_md'),
            $uploadVideo->getRawOriginal('url_lg'),
        ]);
    }

    public static function deleteUploadFile($uploadFileId)
    {
        $uploadFile = UploadFile::find($uploadFileId);
        if (! $uploadFile) {
            return;
        }

        $uploadFile->delete();

        DeleteUploadFile::dispatch([
            $uploadFile->url_og,
        ]);
    }
}
