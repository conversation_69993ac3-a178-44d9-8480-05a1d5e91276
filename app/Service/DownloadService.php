<?php

namespace App\Service;

class DownloadService
{
    public static function generateDownloadKey($filePath, $expire = 3600): string
    {
        $data = [
            'file_path' => $filePath,
            'expire_at' => time() + $expire,
        ];

        $dataStr = json_encode($data);
        $key = encrypt($dataStr);

        return $key;
    }

    public static function parseDownloadKey(string $key): array
    {
        $dataStr = decrypt($key);
        $data = json_decode($dataStr, true);

        return $data;
    }
}
