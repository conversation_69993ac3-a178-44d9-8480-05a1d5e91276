<?php

namespace App\Service\Admin;

use App\Enums\TranslateFrom;
use App\Service\Translate\TranslateDB;

class AdminService
{
    public static function updateTranslate($model, $request, $field)
    {
        $model->setTransByReq($field, $request);

        TranslateDB::updateTargetTextByModelAndRequest(
            $model,
            $request,
            $field,
            TranslateFrom::VERIFIED
        );

        if ($request->is_finish_human_translates) {
            TranslateDB::finishHumanTranslates(
                $model->getMorphClass(),
                $model->id,
                $field,
                $request->{$field.'_lang'},
                $request->{$field}
            );
        }

        $model->refresh();
    }
}
