<?php

namespace App\Service;

use App\Enums\OrderStatus;
use App\Enums\WalletTransactionAction;
use App\Enums\WalletTransactionBizType;
use App\Enums\WalletTransactionStatus;
use App\Models\Order;

class OrderService
{
    public static function cancelOrder(Order $order)
    {
        $user = $order->user;

        $order->update([
            'status' => OrderStatus::Cancelled,
        ]);

        // set status to cancelled
        $order->update([
            'status' => OrderStatus::Cancelled,
        ]);

        // refund wallet balance
        foreach ($order->amount_calc_process['process'] as $process) {
            if ($process['type'] != 'wallet_balance_deduction') {
                continue;
            }

            $wallet = $user->wallets()->find($process['wallet_id']);
            if (! $wallet) {
                continue;
            }

            // wallet blance plus amount
            $before_balance = $wallet->balance;
            $after_balance = $before_balance + $process['wallet_minus']['amount'];
            $wallet->balance = $after_balance;
            $wallet->save();

            $user->walletTransactions()->create([
                'wallet_id' => $process['wallet_id'],
                'currency_id' => $process['wallet_balance']['currency']['id'],
                'action' => WalletTransactionAction::Plus,
                'amount' => $process['wallet_minus']['amount'],
                'before_balance' => $before_balance,
                'after_balance' => $after_balance,
                'biz_type' => WalletTransactionBizType::Refund,
                'status' => WalletTransactionStatus::Successed,
            ]);
        }
    }
}
