<?php

namespace App\Service;

use App\Models\Stage;

class StageService
{
    public static function convertToSyncStages($reqStages, $key)
    {
        $stages = collect($reqStages)->filter(function ($item) {
            return $item['percent'] !== 100;
        });

        $endStage = Stage::Where('is_end_stage', 1)->first();
        $exists = $stages->contains(function ($item) use ($endStage, $key) {
            return $item[$key] == $endStage->id;
        });
        if (! $exists) {
            $stages->push([
                $key => $endStage->id,
                'percent' => 100,
            ]);
        }

        return $stages->keyBy($key)->toArray();
    }
}
