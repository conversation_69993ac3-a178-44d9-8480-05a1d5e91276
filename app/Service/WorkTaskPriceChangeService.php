<?php

namespace App\Service;

use App\Enums\SystemNotificationScene;
use App\Enums\WorkTaskPriceChangeStatus;
use App\Models\Artist;
use App\Models\WorkTask;
use App\Models\WorkTaskPriceChange;
use App\Notifications\SystemNotification;

class WorkTaskPriceChangeService
{
    public static function Create(WorkTask $workTask, $newPrice, $initiator)
    {
        $oldPrice = $workTask->price;
        $initiator_id = $initiator->id;
        $initiator_type = $initiator instanceof Artist ? 'artist' : 'user';

        $paid_amount = $workTask->paid_amount ?? 0;
        $need_pay_amount = 0;
        if ($newPrice > $oldPrice) {
            $need_pay_amount = (int) round($paid_amount * ($newPrice / $oldPrice - 1));
        }

        $priceChange = WorkTaskPriceChange::create([
            'currency_id' => $workTask->currency_id,
            'work_task_id' => $workTask->id,
            'old_price' => $oldPrice,
            'new_price' => $newPrice,
            'status' => WorkTaskPriceChangeStatus::PENDING,
            'initiator_id' => $initiator_id,
            'initiator_type' => $initiator_type,
            'need_pay_amount' => $need_pay_amount,
            'work_task_paid_amount' => $paid_amount,
        ]);

        return $priceChange;
    }

    public static function Approve(WorkTaskPriceChange $priceChange, $approver)
    {
        if ($priceChange->status !== WorkTaskPriceChangeStatus::PENDING) {
            abort(400, 'Price change status is not pending');

            return;
        }

        $priceChange->status = WorkTaskPriceChangeStatus::WAIT_PAY;
        $priceChange->approved_at = now();
        $priceChange->approver_id = $approver->id;
        $priceChange->approver_type = $approver instanceof Artist ? 'artist' : 'user';
        $priceChange->save();

        return $priceChange;
    }

    public static function Reject(WorkTaskPriceChange $priceChange)
    {
        if (! in_array($priceChange->status, [WorkTaskPriceChangeStatus::PENDING, WorkTaskPriceChangeStatus::WAIT_PAY])) {
            abort(400, 'Price change status is not pending or wait pay');

            return;
        }

        $priceChange->status = WorkTaskPriceChangeStatus::REJECTED;
        $priceChange->save();

        return $priceChange;
    }

    public static function Cancel(WorkTaskPriceChange $priceChange)
    {
        if (! in_array($priceChange->status, [WorkTaskPriceChangeStatus::PENDING, WorkTaskPriceChangeStatus::WAIT_PAY])) {
            abort(400, 'Price change status is not pending or wait pay');

            return;
        }

        $priceChange->status = WorkTaskPriceChangeStatus::CANCELED;
        $priceChange->save();

        return $priceChange;
    }

    public static function Paid(WorkTaskPriceChange $priceChange, $paidAmount)
    {
        if ($priceChange->status !== WorkTaskPriceChangeStatus::WAIT_PAY) {
            abort(400, 'Price change status is not wait pay');

            return;
        }

        // update workTask stage
        /** @var WorkTask $workTask */
        $workTask = $priceChange->workTask;
        $stages = $workTask->workTaskStages()->orderBy('percent', 'asc')->get();

        $ps = new PaymentService;
        $newStages = $ps->calcStageAmounts($stages, $priceChange->new_price);
        $ps->refreshStageIsPaid($newStages, $paidAmount);

        // update workTask
        $newPrice = $priceChange->new_price;
        $workTask->price = $newPrice;
        $workTask->save();

        // update priceChange status
        $priceChange->status = WorkTaskPriceChangeStatus::PAID;
        $priceChange->save();

        // notify
        $workTask = $priceChange->workTask;
        $notify = SystemNotification::make(SystemNotificationScene::WorkTaskPriceChangePaid)
            ->setMeta([
                'work_task_id' => $priceChange->workTask->id,
                'price_change_id' => $priceChange->id,
            ]);
        $workTask->artist->notify($notify);
        $workTask->user->notify($notify);
        $workTask->group->notify($notify);

        return $priceChange;
    }
}
