<?php

namespace App\Service;

use Illuminate\Database\Eloquent\Collection;

class PaymentService
{
    public function calcStageAmounts(Collection $stages, int $total_price)
    {
        if (count($stages) == 0) {
            return [];
        }

        $stages = $stages->transform(function ($item, $index) use ($total_price, $stages) {
            if ($item?->pivot?->percent) {
                $item->percent = $item->pivot->percent;
            }

            if ($index == 0) {
                $amount = round($total_price * $item->percent / 100);
            } else {
                $pre = $stages[$index - 1];
                $amount = round($total_price * ($item->percent - $pre->percent) / 100);
            }

            $item->amount = intval($amount);

            return $item;
        })->sortBy('percent');

        return $stages;
    }

    public function refreshStageIsPaid(Collection $newStages, $paidAmount)
    {
        $tmp = $paidAmount;
        foreach ($newStages as $stage) {
            $isPaid = $tmp >= $stage->amount;
            $stage->is_paid = $isPaid ? 1 : 0;
            $stage->save();

            $tmp -= $stage->amount;
        }
    }
}
