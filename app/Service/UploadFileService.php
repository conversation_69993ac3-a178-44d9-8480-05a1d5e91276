<?php

namespace App\Service;

use App\Models\UploadFile;
use Storage;
use Str;
use Symfony\Component\HttpFoundation\HeaderUtils;

class UploadFileService
{
    public static function downloadTemporaryUrl(UploadFile $uploadFile): string
    {
        $filePath = $uploadFile->getRawOriginal('url_og');
        $fileName = $uploadFile->name;

        $disposition = HeaderUtils::makeDisposition(
            HeaderUtils::DISPOSITION_ATTACHMENT,
            $fileName,
            self::fallbackName($fileName)
        );
        $uri = Storage::temporaryUrl($filePath, now()->addHours(1), [
            'ResponseContentDisposition' => $disposition,
            'response_content_disposition' => $disposition,
        ]);

        return $uri;
    }

    public static function fallbackName($name)
    {
        return str_replace('%', '', Str::ascii($name));
    }
}
