<?php

namespace App\Service\Translate;

use App\Enums\TranslateStatus;
use App\Enums\TranslateType;
use App\Models\Translate;
use DB;
use Str;

class TranslateDB
{
    public static function getSourceText(KeyInfo $keyInfo)
    {
        $data = DB::table($keyInfo->table)->where('id', $keyInfo->id)->first();

        return json_decode($data->{$keyInfo->column}, true);
    }

    public static function updateTargetText($table, $id, $field, $target_lang, $targetText, $translate_from)
    {
        DB::table($table)->where('id', $id)->update([
            "{$field}->$target_lang" => $targetText,
            "{$field}->_{$target_lang}_from" => $translate_from,
        ]);
    }

    public static function updateTargetTextByModelAndRequest($model, $request, $field, $translate_from)
    {
        $table = $model->getTable();
        $id = $model->id;
        $target_lang = $request->{$field.'_lang'};
        $targetText = $request->{$field};

        self::updateTargetText($table, $id, $field, $target_lang, $targetText, $translate_from);
    }

    public static function updateTranslate($translate_id, $targetText, $totalToken)
    {
        Translate::where('id', $translate_id)->update([
            'target_text' => $targetText,
            'batteries' => TokenCalculator::calcBatteries($totalToken),
            'total_token' => $totalToken,
            'status' => TranslateStatus::Successed,
        ]);
    }

    public static function createTranslatesForMultipleTargetLangs($model, $field, $source_lang, $target_langs, $user_id)
    {
        $translates = [];

        foreach ($target_langs as $target_lang) {
            $translates[] = self::createTranslateByModel($model, $field, $source_lang, $target_lang, $user_id);
        }

        return $translates;
    }

    public static function createTranslateByModel($model, $field, $source_lang, $target_lang, $user_id)
    {
        $keyInfo = new KeyInfo($model->getTable(), $model->getKey(), $field);

        $translate = Translate::create([
            'user_id' => $user_id,
            'busable_type' => Str::singular($keyInfo->table),
            'busable_id' => $keyInfo->id,
            'busable_field' => $keyInfo->column,
            'busable_table' => $keyInfo->table,
            'key' => $keyInfo->encrypt(),
            'key_decode' => $keyInfo->encode(),
            'translate_type' => TranslateType::Gpt,
            'source_lang' => $source_lang,
            'target_lang' => $target_lang,
            'source' => json_decode($model->getRawOriginal($field)),
            'status' => TranslateStatus::Pending,
        ]);

        return $translate;
    }

    public static function createGptTranslateByContents($contents, $source_lang, $target_langs, $user_id)
    {
        $translates = [];
        foreach ($contents as $content) {
            foreach ($target_langs as $target_lang) {
                $table = $content['busable_table'] ?? '';
                $column = $content['busable_field'] ?? '';
                $keyInfo = new KeyInfo($table, 0, $column);

                $translates[] = Translate::create([
                    'user_id' => $user_id,
                    'busable_type' => Str::singular($keyInfo->table),
                    'busable_id' => $keyInfo->id,
                    'busable_field' => $keyInfo->column,
                    'busable_table' => $keyInfo->table,
                    'key' => $keyInfo->encrypt(),
                    'key_decode' => $keyInfo->encode(),
                    'translate_type' => TranslateType::Gpt,
                    'source_lang' => $source_lang,
                    'target_lang' => $target_lang,
                    'source' => [$source_lang => $content['content'] ?? ''],
                    'target_text' => '',
                    'status' => TranslateStatus::Pending,
                ]);
            }
        }

        return $translates;
    }

    public static function createGptTranslates($keys, $source_lang, $target_langs, $user_id)
    {
        return self::createTranslates($keys, $source_lang, $target_langs, $user_id, TranslateType::Gpt);
    }

    public static function createHumanTranslates($keys, $source_lang, $target_langs, $user_id)
    {
        return self::createTranslates($keys, $source_lang, $target_langs, $user_id, TranslateType::Human);
    }

    private static function createTranslates($keys, $source_lang, $target_langs, $user_id, $translate_type)
    {
        $translates = [];
        foreach ($keys as $key) {
            $keyInfo = KeyInfo::decrypt($key);
            $source = self::getSourceText($keyInfo);

            foreach ($target_langs as $target_lang) {
                $translates[] = self::createSingleTranslate($key, $keyInfo, $source_lang, $target_lang, $source, $user_id, $translate_type);
            }
        }

        return $translates;
    }

    private static function createSingleTranslate($key, KeyInfo $keyInfo, $source_lang, $target_lang, $source, $user_id, $translate_type)
    {
        return Translate::create([
            'user_id' => $user_id,
            'busable_type' => Str::singular($keyInfo->table),
            'busable_id' => $keyInfo->id,
            'busable_field' => $keyInfo->column,
            'busable_table' => $keyInfo->table,
            'key' => $key,
            'key_decode' => $keyInfo->encode(),
            'translate_type' => $translate_type,
            'source_lang' => $source_lang,
            'target_lang' => $target_lang,
            'source' => $source,
            'target_text' => '',
            'status' => TranslateStatus::Pending,
        ]);
    }

    public static function queryByTypeAndStatus($query, $translate_type, $translate_status, $has_translate = true)
    {
        if ($has_translate === null) {
            return;
        }

        $method = $has_translate ? 'whereHas' : 'whereDoesntHave';

        $query->{$method}('translates', function ($query) use ($translate_type, $translate_status) {
            if ($translate_type) {
                $query->where('translate_type', TranslateType::from($translate_type));
            }

            if ($translate_status) {
                $query->where('status', TranslateStatus::from($translate_status));
            }
        });
    }

    public static function finishHumanTranslates($busable_type, $busable_id, $busable_field, $target_lang, $target_text)
    {
        Translate::where('busable_type', $busable_type)
            ->where('busable_id', $busable_id)
            ->where('busable_field', $busable_field)
            ->where('target_lang', $target_lang)
            ->where('translate_type', TranslateType::Human)
            ->update(['status' => TranslateStatus::Successed, 'target_text' => $target_text]);
    }

    public static function cancelHumanTranslates($user_id, array $ids): void
    {
        Translate::whereIn('id', $ids)
            ->where('user_id', $user_id)
            ->where('translate_type', TranslateType::Human)
            ->update(['status' => TranslateStatus::Cancelled]);
    }
}
