<?php

namespace App\Service\Translate;

class KeyManager
{
    public static function generateKeyStr($table, $id, $column)
    {
        return (new KeyInfo($table, $id, $column))->encode();
    }

    public static function generateKeyByStr($keyStr)
    {
        return aes128Encrypt($keyStr);
    }

    public static function generateKey($table, $id, $column)
    {
        return aes128Encrypt(self::generateKeyStr($table, $id, $column));
    }

    public static function parseKey($key): KeyInfo
    {
        $keyStr = aes128Decrypt($key);

        return KeyInfo::decode($keyStr);
    }
}
