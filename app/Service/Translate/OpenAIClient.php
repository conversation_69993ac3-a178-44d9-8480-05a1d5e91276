<?php

namespace App\Service\Translate;

use App\Enums\TranslateFrom;
use App\Events\Echo\ChatMessageTranslateSuccessed;
use App\Models\ChatMessage;
use App\Models\GptPrompt;
use App\Models\Translate;
use GuzzleHttp\Client;
use GuzzleHttp\Promise;
use Illuminate\Support\Facades\URL;
use Log;
use OpenAI\Laravel\Exceptions\ApiKeyIsMissing;
use OpenAI\Responses\Chat\CreateResponse;
use OpenAI\Responses\Meta\MetaInformation;
use Str;

class OpenAIClient
{
    public static function doRequest($translationRequests, $model = 'gpt-3.5-turbo')
    {
        $client = self::GuzzleHttpClient();
        $promises = [];
        $results = [];

        foreach ($translationRequests as $request) {
            $text = $request['text'];
            $promt = $request['promt'];

            // if the text is emoji or url, don't translate
            if (self::checkMessageIsPureEmoji($text) || self::checkMessageIsPureUrl($text)) {
                $results []= [
                    'data' => [
                        'target_text' => $text,
                        'total_token' => 0,
                    ],
                    'meta' => $request['meta'],
                ];
                continue;
            }

            $data = [
                'model' => $model,
                'messages' => [
                    ['role' => 'system', 'content' => $promt],
                    ['role' => 'user', 'content' => $text],
                ],
            ];

            $promises[] = $client->postAsync('/v1/chat/completions', [
                'json' => $data,
            ])->then(function ($result) use ($request) {
                $respBody = json_decode($result->getBody(), true);
                $respData = CreateResponse::from($respBody, MetaInformation::from($result->getHeaders()));

                $targetText = $respData->choices[0]->message->content;
                $totalToken = $respData->usage->totalTokens;

                $res['data'] = [
                    'target_text' => $targetText,
                    'total_token' => $totalToken,
                ];
                $res['meta'] = $request['meta'];

                return $res;
            });
        }

        $primisesRet =  Promise\Utils::unwrap($promises);
        $results = array_merge($results, $primisesRet);

        return $results;
    }

    public static function checkMessageIsPureUrl(string $data){
        $data = trim($data);
        return Str::isUrl($data);
    }

    public static function checkMessageIsPureEmoji(string $data) {
        $data = trim($data);
        
        // Match emoji pattern
        $emoji_pattern = '/^(?:[\x{1F300}-\x{1F9FF}]|[\x{2600}-\x{26FF}]|[\x{2700}-\x{27BF}]|[\x{1F000}-\x{1F02F}]|[\x{1F0A0}-\x{1F0FF}]|[\x{1F100}-\x{1F64F}]|[\x{1F680}-\x{1F6FF}]|[\x{1F900}-\x{1F9FF}]|[\x{1FA70}-\x{1FAFF}]|[\x{2194}-\x{2199}]|[\x{21A9}-\x{21AA}]|[\x{231A}-\x{231B}]|[\x{23E9}-\x{23F3}]|[\x{23F8}-\x{23FA}]|[\x{25AA}-\x{25AB}]|[\x{25B6}]|[\x{25C0}]|[\x{25FB}-\x{25FE}]|[\x{2600}-\x{2604}]|[\x{260E}]|[\x{2611}]|[\x{2614}-\x{2615}]|[\x{2618}]|[\x{261D}]|[\x{2620}]|[\x{2622}-\x{2623}]|[\x{2626}]|[\x{262A}]|[\x{262E}-\x{262F}]|[\x{2638}-\x{263A}]|[\x{2640}]|[\x{2642}]|[\x{2648}-\x{2653}]|[\x{265F}-\x{2660}]|[\x{2663}]|[\x{2665}-\x{2666}]|[\x{2668}]|[\x{267B}]|[\x{267E}-\x{267F}]|[\x{2692}-\x{2697}]|[\x{2699}]|[\x{269B}-\x{269C}]|[\x{26A0}-\x{26A1}]|[\x{26A7}]|[\x{26AA}-\x{26AB}]|[\x{26B0}-\x{26B1}]|[\x{26BD}-\x{26BE}]|[\x{26C4}-\x{26C5}]|[\x{26C8}]|[\x{26CE}-\x{26CF}]|[\x{26D1}]|[\x{26D3}-\x{26D4}]|[\x{26E9}-\x{26EA}]|[\x{26F0}-\x{26F5}]|[\x{26F7}-\x{26FA}]|[\x{26FD}]|[\x{2702}]|[\x{2705}]|[\x{2708}-\x{270D}]|[\x{270F}])+$/u';
        
        return preg_match($emoji_pattern, $data) === 1;
    }

    public static function GuzzleHttpClient()
    {
        $apiKey = config('openai.api_key');
        $organization = config('openai.organization');

        if (! is_string($apiKey) || ($organization !== null && ! is_string($organization))) {
            throw ApiKeyIsMissing::create();
        }

        return new Client([
            'base_uri' => 'https://api.openai.com',
            'headers' => [
                'Authorization' => 'Bearer '.$apiKey,
                'OpenAI-Beta' => 'assistants=v2',
                'Content-Type' => 'application/json',
                'Organization' => $organization,
            ],
            'timeout' => config('openai.request_timeout', 2),
            'verify' => false,
            'proxy' => config('openai.proxy', ''),
        ]);
    }

    public static function generateRequestsByIds($translateIds)
    {
        $translates = Translate::whereIn('id', $translateIds)->get();

        return self::generateRequestsByModels($translates);
    }

    public static function generateRequestsByModels($translates)
    {
        $translationRequests = [];
        foreach ($translates as $translate) {
            $translationRequests[] = self::generateRequestsByModel($translate);
        }

        return $translationRequests;
    }

    public static function generateRequestsByModel($translate)
    {
        $source_lang = $translate->source_lang;
        $target_lang = $translate->target_lang;
        $busable_type = $translate->busable_type;
        $busable_id = $translate->busable_id;
        $busable_field = $translate->busable_field;
        $busable_table = $translate->busable_table;
        $sourceJson = $translate->source;

        $promt = self::getPrompt("{$busable_table}.{$busable_field}", $target_lang);
        Log::info('promt: '.$promt);

        return [
            'text' => $sourceJson[$source_lang] ?? '',
            'promt' => $promt,
            'meta' => [
                'translate_id' => $translate->id,
                'busable_type' => $busable_type,
                'busable_id' => $busable_id,
                'busable_field' => $busable_field,
                'busable_table' => $busable_table,
                'source_json' => $sourceJson,
                'target_lang' => $target_lang,
                'source_lang' => $source_lang,
            ],
        ];

    }

    private static function getPrompt($table_column, $target_lang)
    {
        $GptPrompt = GptPrompt::where('table_column', $table_column)->first();

        if (! $GptPrompt) {
            $GptPrompt = GptPrompt::where('table_column', 'default')->first();
        }

        $prompt = $GptPrompt->prompt;
        $prompt = str_ends_with($prompt, '.') ? $prompt : $prompt.'.';

        $lang = locale_get_display_name($target_lang, 'en');
        $lang_prompt = "The target language is {$lang}.";

        $emoji_url_prompt = 'Do not translate the emoji and url.'; // emoji and url don't translate prompt

        return $prompt.$lang_prompt.$emoji_url_prompt;
    }

    public static function processResponses($translationResponses)
    {
        $translations = [];
        foreach ($translationResponses as $response) {
            $data = $response['data'];
            $meta = $response['meta'];

            $targetText = $data['target_text'];
            $totalToken = $data['total_token'];

            $table = $meta['busable_table'];
            $id = $meta['busable_id'];
            $field = $meta['busable_field'];
            $source_lang = $meta['source_lang'];
            $source_json = $meta['source_json'];
            $source_text = $source_json[$source_lang] ?? '';
            if ($source_text === '') {
                $targetText = '';
            }

            if ($id != 0) {
                TranslateDB::updateTargetText($table, $id, $field, $meta['target_lang'], $targetText, TranslateFrom::AI);
                if ($table === ChatMessage::TABLE) {
                    // event(new ChatMessageTranslateSuccessed($id));
                }
            }

            TranslateDB::updateTranslate($meta['translate_id'], $targetText, $totalToken);
            $translations[] = Translate::find($meta['translate_id']);
        }

        return $translations;
    }

    public static function generateRequestsByContents($contents, $target_lang)
    {
        $translationRequests = [];
        foreach ($contents as $content) {
            $translationRequests[] = self::generateRequestsByContent($content, $target_lang);
        }

        return $translationRequests;
    }

    public static function generateRequestsByContent($content, $target_lang)
    {
        $prompt = self::getPrompt($content['prompt_scene'], $target_lang);

        return [
            'text' => $content['content'],
            'promt' => $prompt,
        ];
    }
}
