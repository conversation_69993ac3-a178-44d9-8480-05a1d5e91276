<?php

namespace App\Service\Translate;

use Raj<PERSON>rivedi\TokenizerX\TokenizerX;

class TokenCalculator
{
    public static function preCalcBatteries($text)
    {
        $tokenCount = self::countTokens($text);
        $batteries = self::calcBatteries($tokenCount);

        return $batteries;
    }

    public static function calcBatteries($tokenCount)
    {
        $batteries = ceil(($tokenCount + 50) * 2 / 1000);
        if ($batteries < 1) {
            $batteries = 1;
        }

        return $batteries;
    }

    public static function countTokens($text, $model = 'gpt-4')
    {
        $count = TokenizerX::count($text, $model);

        return $count;
    }
}
