<?php

namespace App\Service\Translate;

class KeyInfo
{
    public string $table;

    public string $id;

    public string $column;

    public function __construct(?string $table, string $id, ?string $column)
    {
        $this->table = $table ?? '';
        $this->id = $id ?? 0;
        $this->column = $column ?? '';
    }

    public function encode(): string
    {
        return "{$this->table}:{$this->id}:{$this->column}";
    }

    public static function decode(string $keyStr): self
    {
        $columns = explode(':', $keyStr);

        return new self($columns[0], $columns[1], $columns[2]);
    }

    public function encrypt(): string
    {
        return aes128Encrypt($this->encode());
    }

    public static function decrypt(string $key): self
    {
        return self::decode(aes128Decrypt($key));
    }
}
