<?php

namespace App\Service\Translate;

use Log;

class TranslationManager
{
    public static function translateColumnByIds($ids)
    {
        $startTime = microtime(true);

        $translationRequests = OpenAIClient::generateRequestsByIds($ids);
        $translationResponses = OpenAIClient::doRequest($translationRequests);

        $elapsedTime = microtime(true) - $startTime;
        Log::info([
            'elapsedTime' => $elapsedTime,
            'translationCount' => count($translationRequests),
        ]);

        return OpenAIClient::processResponses($translationResponses);
    }
}
