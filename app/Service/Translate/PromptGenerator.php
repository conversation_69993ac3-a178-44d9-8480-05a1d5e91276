<?php

namespace App\Service\Translate;

use App\Models\GptPrompt;

class PromptGenerator
{
    public static function getPrompt($keyInfo, $target_lang)
    {
        $table = $keyInfo['table'];
        $column = $keyInfo['column'];
        $table_column = "$table.$column";
        $GptPrompt = GptPrompt::where('table_column', $table_column)->first();

        if (! $GptPrompt) {
            $GptPrompt = GptPrompt::where('table_column', 'default')->first();
        }

        $lang = locale_get_display_name($target_lang, 'en');
        $lang_prompt = 'Target language : '.$lang.'.';

        return $GptPrompt.$lang_prompt;
    }
}
