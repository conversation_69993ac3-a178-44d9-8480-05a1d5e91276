<?php

namespace App\Service\Stripe;

use App\Models\Currency;
use App\Models\WorkTaskStage;
use App\Service\RateService;
use App\Service\Stripe\ProcessStage\CnyExchangeStage;
use App\Service\Stripe\ProcessStage\InitFromWorkTaskStage;
use App\Service\Stripe\ProcessStage\WalletBalanceDeductionStage;
use App\Service\Stripe\ProcessStage\ZeroDecimalCurrencyCeilStage;

class StripeCalcAmount
{
    protected $stage = [
        'init_from_work_task_stage', // 工作任务阶段
        'cny_currency_conversion', // cny 转换为 usd
        'wallet_balance_deduction', // 钱包余额抵扣
        'zero_decimal1_currency_ceil', // 必须放在最后一步，不能放在前面，零位十进制货币向上取整
    ];

    protected Amount $init_amount;

    protected Amount $ret_amount;

    protected array $process = [];

    public function __construct()
    {
        $this->init_amount = Amount::new();
        $this->ret_amount = Amount::new();
    }

    public function InitFromWorkTaskStage($work_task, $type, $priceChange = null)
    {
        $amount = 0;
        $paidAmount = $work_task->paid_amount;

        if ($type == 'full_pay') {
            $amount = $work_task->price - $paidAmount;
        }
        if ($type == 'stage_pay') {
            $amount = $this->calcStagePayAmount($work_task);
        }
        if ($type == 'price_change') {
            $amount = $priceChange->need_pay_amount;
        }

        if ($amount < 0) {
            abort(400, 'Amount is less than 0');
        }

        $processStage = InitFromWorkTaskStage::new(
            Amount::new($amount, $work_task->currency),
            Amount::new($amount, $work_task->currency)
        );
        $processStage->work_task_id = $work_task->id;
        $processStage->pay_type = $type;
        $processStage->price_change_id = $priceChange->id ?? null;

        $this->process[] = $processStage;

        $this->setInitAmount(Amount::new($amount, $work_task->currency));
        $this->setRetAmount(Amount::new($amount, $work_task->currency));

        return $this;
    }

    private function calcStagePayAmount($work_task)
    {
        $paidAmount = $work_task->paid_amount;
        $work_task_stages = $work_task->workTaskStages;

        foreach ($work_task_stages as $stage) {
            if ($stage->is_paid && $stage->work_status == WorkTaskStage::WorkStatusPending) {
                return 0;
            }
        }

        $tmp = 0;
        foreach ($work_task_stages as $stage) {
            if (! $stage->is_paid) {
                $tmp += $stage->amount;
                break;
            }
            $tmp += $stage->amount;
        }

        $amount = $tmp - $paidAmount;

        return $amount;
    }

    public function CnyCurrencyConversion()
    {
        $before_amount = $this->ret_amount;
        if ($before_amount->currency->code !== 'CNY') {
            return $this;
        }
        $amount = $before_amount->amount;

        $rate = RateService::new()->getRate('CNY', 'USD');
        $amount = RateService::new()->amountExchangeByRate($amount, $rate, 'ceil');

        $usd_currency = Currency::where('code', 'USD')->first();
        if (! $usd_currency) {
            abort(400, 'USD currency not found');
        }
        $after_amount = Amount::new($amount, $usd_currency);
        $processStage = CnyExchangeStage::new($before_amount, $after_amount);
        $processStage->rate = $rate;
        $this->process[] = $processStage;
        $this->setRetAmount($after_amount);

        return $this;
    }

    public function WalletBalanceDeduction($wallets)
    {
        $amount_before = $this->getRetAmount();

        $exchange_currency = $amount_before->currency;

        $amount_after = $this->getRetAmount();
        foreach ($wallets as $wallet) {
            if ($amount_after->amount <= 0) {
                continue;
            }
            $wallet_balance = $wallet->balance;
            $wallet_balance_currency = $wallet->currency;
            if ($wallet_balance <= 0) {
                continue;
            }

            $wallet_exchange_balance = RateService::new()->amountExchange($wallet_balance, $wallet_balance_currency->code, $exchange_currency->code, 'floor');

            if ($amount_before->amount - $wallet_exchange_balance > 0) {
                $amount_minus = $wallet_exchange_balance;
                $amount_after->amount = $amount_before->amount - $amount_minus;
                $wallet_minus = $wallet_balance;
                $wallet_balance_after = 0;
            } else {
                $amount_minus = $amount_before->amount;
                $amount_after->amount = 0;
                $wallet_minus = RateService::new()->amountExchange($amount_minus, $exchange_currency->code, $wallet_balance_currency->code, 'ceil');
                $wallet_balance_after = $wallet_balance - $wallet_minus;
            }

            $processStage = WalletBalanceDeductionStage::new($amount_before, $amount_after);
            $processStage->wallet_id = $wallet->id;
            $processStage->wallet_balance = Amount::new($wallet_balance, $wallet_balance_currency);
            $processStage->wallet_exchange_balance = Amount::new($wallet_exchange_balance, $exchange_currency);
            $processStage->wallet_minus = Amount::new($wallet_minus, $wallet_balance_currency);
            $processStage->wallet_balance_after = Amount::new($wallet_balance_after, $wallet_balance_currency);
            $processStage->amount_minus = Amount::new($amount_minus, $exchange_currency);
            $this->process[] = $processStage;

            $amount_before = $amount_after->clone();
        }

        $this->setRetAmount($amount_after);

        return $this;
    }

    public function ZeroDecimalCurrencyCeil()
    {
        $amount_before = $this->getRetAmount();
        $amount_after = $this->getRetAmount();
        if (! $amount_before->currency->is_zero_decimal) {
            return $this;
        }

        $amount_after->amount = ceil($amount_before->amount / 100) * 100;
        $amount_plus = $amount_after->amount - $amount_before->amount;

        if ($amount_plus === 0) {
            return $this;
        }

        $processStage = ZeroDecimalCurrencyCeilStage::new($amount_before, $amount_after);
        $processStage->amount_plus = Amount::new($amount_plus, $amount_before->currency);
        $this->process[] = $processStage;

        $this->setRetAmount($amount_after);

        return $this;
    }

    public function getProcess()
    {
        return $this->process;
    }

    public function getInitAmount()
    {
        return $this->init_amount->clone();
    }

    public function setInitAmount(Amount $amount)
    {
        $this->init_amount = $amount->clone();

        return $this;
    }

    public function getRetAmount()
    {
        return $this->ret_amount->clone();
    }

    public function setRetAmount(Amount $amount)
    {
        $this->ret_amount = $amount->clone();

        return $this;
    }

    public function toArray()
    {
        return [
            'init_amount' => $this->getInitAmount()->toArray(),
            'ret_amount' => $this->getRetAmount()->toArray(),
            'process' => json_decode(json_encode($this->getProcess()), true),
        ];
    }
}
