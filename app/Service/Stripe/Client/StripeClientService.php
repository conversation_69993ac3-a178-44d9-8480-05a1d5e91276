<?php

namespace App\Service\Stripe\Client;

use App\Models\StripeObject;
use Stripe\StripeClient;

class StripeClientService
{
    protected $client;

    protected $defaultsaveDB;

    protected $defaultfromDB;

    // 通过构造函数设置全局默认值
    public function __construct(bool $saveDB = false, bool $fromDB = false)
    {
        $this->client = new StripeClient(config('stripe.secret'));
        $this->defaultsaveDB = $saveDB;
        $this->defaultfromDB = $fromDB;
    }

    public static function New(bool $saveDB = false, bool $fromDB = false)
    {
        return new self($saveDB, $fromDB);
    }

    /**
     * 增强版资源获取方法
     *
     * @param  bool|null  $saveDB  null 时使用全局默认值
     * @param  null|array  $params  参数
     */
    private function retrieveResource(
        string $type,
        string $id,
        callable $retriever,
        ?bool $saveDB = null,
        ?bool $fromDB = null,
        $params = null,
    ): array {
        $saveDB = $saveDB ?? $this->defaultsaveDB;
        $fromDB = $fromDB ?? $this->defaultfromDB;

        // 其次从数据库获取
        if ($fromDB) {
            $stripeObject = StripeObject::where('stripe_id', $id)
                ->where('type', $type)
                ->first();

            if ($stripeObject) {
                return $stripeObject->data;
            }
        }

        // 从 Stripe API 获取最新数据
        $resource = $retriever($id, $params);
        $data = $resource->toArray();

        // 存储到数据库（如果启用）
        if ($saveDB) {
            StripeObject::updateOrCreate(
                ['stripe_id' => $id, 'type' => $type],
                ['data' => $data]
            );
        }

        return $data;
    }

    /**
     * 创建资源
     */
    private function createResource(
        callable $retriever,
        ?bool $saveDB = null
    ): array {
        $saveDB = $saveDB ?? $this->defaultsaveDB;

        $resource = $retriever();
        $data = $resource->toArray();

        // 保存到数据库
        if ($saveDB) {
            StripeObject::updateOrCreate(
                ['stripe_id' => $resource->id],
                ['data' => $data]
            );
        }

        return $data;
    }

    /**
     * 获取 Checkout Session
     */
    public function getCheckoutSession(
        string $sessionId,
        ?bool $saveDB = null,
        ?bool $fromDB = null,
        $params = [],
    ): \Stripe\Checkout\Session {
        $params = array_merge($params, [
            'expand' => array_merge($params['expand'] ?? [], [
                'payment_intent.latest_charge.balance_transaction',
            ]),
        ]);

        $data = $this->retrieveResource(
            \Stripe\Checkout\Session::OBJECT_NAME,
            $sessionId,
            function ($id, $params) {
                return $this->client->checkout->sessions->retrieve($id, $params);
            },
            $saveDB,
            $fromDB,
            $params,
        );

        return \Stripe\Util\Util::convertToStripeObject($data, []);
    }

    /**
     * 获取 Payment Intent
     */
    public function getPaymentIntent(
        string $intentId,
        ?bool $saveDB = null,
        ?bool $fromDB = null,
        $params = [],
    ): \Stripe\PaymentIntent {
        $params = array_merge($params, [
            'expand' => array_merge($params['expand'] ?? [], [
                'latest_charge.balance_transaction',
            ]),
        ]);

        $data = $this->retrieveResource(
            \Stripe\PaymentIntent::OBJECT_NAME,
            $intentId,
            function ($id, $params) {
                return $this->client->paymentIntents->retrieve($id, $params);
            },
            $saveDB,
            $fromDB,
            $params,
        );

        return \Stripe\Util\Util::convertToStripeObject($data, []);
    }
}
