<?php

namespace App\Service\Stripe;

use App\Models\WalletDeductionRecord;
use App\Service\Stripe\Client\StripeClientService;
use App\Enums\WalletDeductionStatus;
use Exception;
use Log;

class StripeTransferService
{
    protected $stripeClient;

    public function __construct()
    {
        $this->stripeClient = StripeClientService::New(saveDB: true);
    }

    /**
     * 执行转账到艺术家账户
     */
    public function transferToArtist(WalletDeductionRecord $record): array
    {
        if ($record->status !== WalletDeductionStatus::Pending) {
            throw new Exception('只能处理pending状态的补差记录');
        }

        // 更新状态为处理中
        $record->update(['status' => WalletDeductionStatus::Processing]);

        try {
            // 获取艺术家的Stripe账户ID
            $stripeAccountId = $record->artistWithdrawAccount->stripe_account_id;

            if (!$stripeAccountId) {
                throw new Exception('艺术家Stripe账户ID不存在');
            }

            // 创建转账
            $transfer = $this->stripeClient->client->transfers->create([
                'amount' => $record->amount,
                'currency' => strtolower($record->currency->code),
                'destination' => $stripeAccountId,
                'metadata' => [
                    'wallet_deduction_id' => $record->id,
                    'order_id' => $record->order_id,
                    'user_id' => $record->user_id,
                    'artist_user_id' => $record->artist_user_id,
                ],
            ]);

            // 更新记录状态
            $record->update([
                'status' => WalletDeductionStatus::Completed,
                'stripe_transfer_id' => $transfer->id,
                'compensated_at' => now(),
            ]);

            Log::info('Stripe transfer completed', [
                'deduction_record_id' => $record->id,
                'transfer_id' => $transfer->id,
                'amount' => $record->amount,
                'currency' => $record->currency->code,
            ]);

            return [
                'success' => true,
                'transfer_id' => $transfer->id,
                'amount' => $record->amount,
            ];

        } catch (Exception $e) {
            // 更新失败状态
            $record->update([
                'status' => WalletDeductionStatus::Failed,
                'failure_reason' => $e->getMessage(),
            ]);

            Log::error('Stripe transfer failed', [
                'deduction_record_id' => $record->id,
                'error' => $e->getMessage(),
                'amount' => $record->amount,
            ]);

            throw $e;
        }
    }

    /**
     * 批量处理待补差记录
     */
    public function processPendingRecords(int $limit = 50): array
    {
        $pendingRecords = WalletDeductionRecord::pending()
            ->with(['artistWithdrawAccount', 'currency'])
            ->limit($limit)
            ->get();

        $results = [
            'processed' => 0,
            'failed' => 0,
            'errors' => [],
        ];

        foreach ($pendingRecords as $record) {
            try {
                $this->transferToArtist($record);
                $results['processed']++;
            } catch (Exception $e) {
                $results['failed']++;
                $results['errors'][] = [
                    'record_id' => $record->id,
                    'error' => $e->getMessage(),
                ];
            }
        }

        return $results;
    }
}
