<?php

namespace App\Service\Stripe\ProcessStage;

class CnyExchangeStage extends ProcessStage
{
    public string $type = 'cny_currency_conversion';

    public $rate;

    public function __construct($before_amount, $after_amount)
    {
        parent::__construct($before_amount, $after_amount);
    }

    public static function new($before_amount, $after_amount)
    {
        return new self($before_amount, $after_amount);
    }
}
