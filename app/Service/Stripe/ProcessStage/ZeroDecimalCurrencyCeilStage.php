<?php

namespace App\Service\Stripe\ProcessStage;

use App\Service\Stripe\Amount;

class ZeroDecimalCurrencyCeilStage extends ProcessStage
{
    public string $type = 'zero_decimal1_currency_ceil';

    public Amount $amount_plus;

    public function __construct($before_amount, $after_amount)
    {
        parent::__construct($before_amount, $after_amount);
    }

    public static function new($before_amount, $after_amount)
    {
        return new self($before_amount, $after_amount);
    }
}
