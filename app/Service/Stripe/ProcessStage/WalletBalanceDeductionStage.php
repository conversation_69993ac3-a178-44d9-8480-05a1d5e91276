<?php

namespace App\Service\Stripe\ProcessStage;

use App\Service\Stripe\Amount;

class WalletBalanceDeductionStage extends ProcessStage
{
    public string $type = 'wallet_balance_deduction';

    public int $wallet_id;

    public Amount $wallet_balance;

    public Amount $wallet_exchange_balance;

    public Amount $wallet_minus;

    public Amount $wallet_balance_after;

    public Amount $amount_minus;

    public function __construct($before_amount, $after_amount)
    {
        parent::__construct($before_amount, $after_amount);
    }

    public static function new($before_amount, $after_amount)
    {
        return new self($before_amount, $after_amount);
    }
}
