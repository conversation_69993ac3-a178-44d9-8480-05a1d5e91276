<?php

namespace App\Service\Stripe\ProcessStage;

class InitFromWorkTaskStage extends ProcessStage
{
    public string $type = 'init_from_work_task_stage';

    public $work_task_id;

    public $pay_type;

    public $price_change_id;

    public function __construct($before_amount, $after_amount)
    {
        parent::__construct($before_amount, $after_amount);
    }

    public static function new($before_amount, $after_amount)
    {
        return new self($before_amount, $after_amount);
    }
}
