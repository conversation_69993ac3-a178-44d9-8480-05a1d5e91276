<?php

namespace App\Service\Stripe\ProcessStage;

use App\Service\Stripe\Amount;

class ProcessStage
{
    public string $type = '';

    public Amount $before_amount;

    public Amount $after_amount;

    public function __construct($before_amount, $after_amount)
    {
        $this->before_amount = $before_amount->clone();
        $this->after_amount = $after_amount->clone();
    }

    public static function new($before_amount, $after_amount)
    {
        return new self($before_amount, $after_amount);
    }
}
