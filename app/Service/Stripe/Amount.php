<?php

namespace App\Service\Stripe;

use App\Models\Currency;

class Amount
{
    public int $amount = 0;

    public Currency $currency;

    public function __construct($amount, Currency $currency)
    {
        $this->amount = $amount;
        $this->currency = $currency;
    }

    public static function new(?int $amount = null, ?Currency $currency = null): self
    {
        if ($amount === null) {
            $amount = 0;
        }

        if ($currency === null) {
            $currency = Currency::where('code', 'USD')->first();
        }

        return new self($amount, $currency);
    }

    public function clone(): self
    {
        return new self($this->amount, $this->currency);
    }

    public function toArray(): array
    {
        return [
            'amount' => $this->amount,
            'currency' => $this->currency->toArray(),
        ];
    }
}
