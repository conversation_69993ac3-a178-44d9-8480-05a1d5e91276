<?php

namespace App\Service\Stripe;

use App\Enums\CurrencyEnum;
use App\Enums\OrderStatus;
use App\Enums\SystemNotificationScene;
use App\Models\Currency;
use App\Models\Order;
use App\Models\ProductOrder;
use App\Models\StripeCheckoutSession;
use App\Models\WorkTask;
use App\Models\WorkTaskPriceChange;
use App\Models\WorkTaskStage;
use App\Notifications\SystemNotification;
use App\Service\RateService;
use App\Service\Stripe\Client\StripeClientService;
use App\Service\Stripe\Object\CalcFeeData;
use App\Service\Stripe\Object\FeeData;
use App\Service\Stripe\Object\PaymentInfoData;
use App\Service\Stripe\Object\StripeFeeData;
use App\Service\WorkTaskPriceChangeService;
use DB;
use Log;

class StripeWebhookService
{
    public static function New()
    {
        return new self;
    }

    public function processCheckoutSessionCompleted(array $payload): void
    {
        try {
            $event = \Stripe\Event::constructFrom($payload);

            DB::beginTransaction();
            try {

                // 防止重复的 checkout_session_id 消息
                $scs = StripeCheckoutSession::where('checkout_session_id', $event->data->object->id)
                    ->where('status', StripeCheckoutSession::STATUS_PENDING)
                    ->lockForUpdate()
                    ->first();
                if (! $scs) {
                    return;
                }

                $order = $scs->order;
                if (! $order) {
                    return;
                }

                if ($order->status !== OrderStatus::Paying) {
                    return;
                }

                $workTask = null;
                $productOrder = null;

                if ($order->busable_type === 'work_task') {
                    $workTask = $this->workTask($order, $scs);
                } elseif ($order->busable_type === 'product') {
                    $productOrder = $this->product($scs);
                }

                DB::commit();

                // 事务完成后发送通知
                if ($workTask && $workTask->status === WorkTask::StatusWorking) {
                    $this->sendWorkTaskNotifications($workTask);
                }

                if ($productOrder) {
                    $this->sendProductOrderNotifications($productOrder);
                }

            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }

        } catch (\UnexpectedValueException $e) {
            Log::error($e->getMessage());

            return;
        }
    }

    private function product($scs): ?ProductOrder
    {
        if (empty($scs->product_order_id)) {
            return null;
        }

        $order = ProductOrder::query()->find($scs->product_order_id);
        $order->status = ProductOrder::STATUS_PAID;
        $order->save();

        return $order;
    }

    private function workTask(Order $order, StripeCheckoutSession $scs): ?WorkTask
    {
        $work_task_id = $order->busable_info['work_task_id'];
        $pay_type = $order->busable_info['pay_type'];
        $work_task = WorkTask::find($work_task_id);

        // calc paid_amount
        $init_amount = $order->init_amount;
        $paidAmount = $work_task->paid_amount + $init_amount;

        // update work_task_stage
        if ($pay_type === StripeCheckoutSession::PAY_TYPE_FULL_PAY) {
            $work_task->workTaskStages()
                ->update([
                    'is_paid' => WorkTaskStage::PayStatusPaid,
                ]);

            $hasWorkingStage = $work_task->workTaskStages()
                ->where('work_status', WorkTaskStage::WorkStatusWorking)
                ->first();
            if (! $hasWorkingStage) {
                $work_task->workTaskStages()
                    ->where('work_status', WorkTaskStage::WorkStatusPending)
                    ->orderBy('percent')
                    ->first()
                    ->update([
                        'work_status' => WorkTaskStage::WorkStatusWorking,
                    ]);
            }

            if ($work_task->status === WorkTask::StatusWaitPay) {
                $work_task->status = WorkTask::StatusWorking;
                $work_task->save();
            }

        }

        if ($pay_type === StripeCheckoutSession::PAY_TYPE_STAGE_PAY) {
            $work_task->workTaskStages()
                ->where('work_status', WorkTaskStage::WorkStatusWorking)
                ->update(['work_status' => WorkTaskStage::WorkStatusFinished]);

            $stage = $work_task->workTaskStages()
                ->where('work_status', WorkTaskStage::WorkStatusPending)
                ->orderBy('percent')
                ->first();

            $stage->is_paid = WorkTaskStage::PayStatusPaid;
            $stage->work_status = WorkTaskStage::WorkStatusWorking;
            $stage->save();
        }

        if ($pay_type === StripeCheckoutSession::PAY_TYPE_PRICE_CHANGE) {
            $priceChangeId = $order->busable_info['price_change_id'];
            $priceChange = WorkTaskPriceChange::find($priceChangeId);
            WorkTaskPriceChangeService::Paid($priceChange, $paidAmount);
        }

        if ($work_task->status === WorkTask::StatusWaitPay) {
            $work_task->status = WorkTask::StatusWorking;
            $work_task->save();
        }

        // update work_task
        $work_task->paid_amount = $paidAmount;
        $work_task->save();

        // update order
        $order->status = OrderStatus::Paid;

        // calc fee
        $calc_fee = $this->calcFee($scs->checkout_session_id, $work_task->currency);
        $order->payment_info = $calc_fee->payment_info;
        $order->stripe_fee = $calc_fee->stripe_fee;
        $order->fee = $calc_fee->fee;
        $order->save();

        // update stripe_checkout_session
        $scs->status = StripeCheckoutSession::STATUS_FINISHED;
        $scs->save();

        return $work_task;
    }

    public function calcFee($checkout_session_id, ?Currency $target_currency = null): CalcFeeData
    {
        $checkout_session = StripeClientService::New(saveDB: true)->getCheckoutSession($checkout_session_id);
        if ($checkout_session->amount_total === 0) {
            return CalcFeeData::from([
                'fee' => FeeData::from([
                    'amount' => 0,
                ]),
            ]);
        }
        $latest_charge = $checkout_session->payment_intent->latest_charge;
        $balance_transaction = $latest_charge->balance_transaction;
        $txn_currency = Currency::query()->where('code', strtoupper($balance_transaction->currency))->first();

        $payment_info = PaymentInfoData::from([
            'payment_intent' => $checkout_session->payment_intent->toArray(),
            'latest_charge' => $latest_charge->toArray(),
            'balance_transaction' => $balance_transaction->toArray(),
        ]);

        $exchange_rate = $balance_transaction->exchange_rate;

        $stripe_fee = StripeFeeData::from([
            'amount' => $balance_transaction->fee,
            'currency' => [
                'id' => $txn_currency->id,
                'code' => $txn_currency->code,
                'symbol' => $txn_currency->symbol,
            ],
            'exchange_rate' => $exchange_rate,
            'fee_details' => $balance_transaction->fee_details,
        ]);

        $charge_currency = Currency::query()->where('code', strtoupper($latest_charge->currency))->first();

        $fee_rate = $exchange_rate ? 1 / $exchange_rate : 1;
        if ($target_currency && $target_currency->code === CurrencyEnum::CNY->value && $txn_currency->code === CurrencyEnum::USD->value) {
            $fee_rate = RateService::new()->getRate(CurrencyEnum::USD->value, CurrencyEnum::CNY->value);
            $charge_currency = $target_currency;
        }
        $fee_amount = RateService::new()->amountExchangeByRate($balance_transaction->fee, $fee_rate);
        if ($charge_currency->is_zero_decimal) {
            $fee_amount = $fee_amount * 100;
        }

        $fee = FeeData::from([
            'amount' => $fee_amount,
            'currency' => [
                'id' => $charge_currency->id,
                'code' => $charge_currency->code,
                'symbol' => $charge_currency->symbol,
            ],
            'exchange_rate' => $fee_rate,
            'fee_details' => [
                [
                    'amount' => $fee_amount,
                    'currency' => [
                        'id' => $charge_currency->id,
                        'code' => $charge_currency->code,
                        'symbol' => $charge_currency->symbol,
                    ],
                    'type' => 'stripe_fee',
                ],
            ],
        ]);

        return CalcFeeData::from([
            'payment_info' => $payment_info,
            'stripe_fee' => $stripe_fee,
            'fee' => $fee,
        ]);

    }

    private function sendWorkTaskNotifications(WorkTask $work_task): void
    {
        try {
            if ($work_task->reqable_type === 'service_request') {
                $notify = SystemNotification::make()
                    ->setScene(SystemNotificationScene::ServiceRequestUserPaid)
                    ->setMeta([
                        'work_task_id' => $work_task->id,
                    ]);
                $work_task->artist->notify($notify);
                $work_task->user->notify($notify);
            }

            if ($work_task->reqable_type === 'project_request') {
                $notify = SystemNotification::make()
                    ->setScene(SystemNotificationScene::ProjectRequestUserPaid)
                    ->setMeta([
                        'work_task_id' => $work_task->id,
                    ]);
                $work_task->artist->notify($notify);
                $work_task->user->notify($notify);
            }
        } catch (\Exception $e) {
            Log::error('Failed to send work task notifications', [
                'work_task_id' => $work_task->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    private function sendProductOrderNotifications(ProductOrder $productOrder): void
    {
        try {
            $notify = SystemNotification::make()
                ->setScene(SystemNotificationScene::ProductOrderPaid)
                ->setMeta([
                    'product_order_id' => $productOrder->id,
                ]);

            $productOrder->user->notify($notify);

            $productOrder->seller->notify($notify);

        } catch (\Exception $e) {
            Log::error('Failed to send product order notifications', [
                'product_order_id' => $productOrder->id,
                'error' => $e->getMessage(),
            ]);
        }
    }
}
