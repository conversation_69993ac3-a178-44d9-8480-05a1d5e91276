<?php

namespace App\Service;

use App\Enums\GroupUserPivotRole;
use App\Enums\SystemNotificationScene;
use App\Enums\WalletTransactionAction;
use App\Enums\WalletTransactionBizType;
use App\Enums\WalletTransactionStatus;
use App\Models\Group;
use App\Models\Wallet;
use App\Models\WorkTask;
use App\Models\WorkTaskStage;
use App\Notifications\SystemNotification;
use App\Service\Stripe\Amount;
use App\Service\Wallet\PlatFeeWalletService;
use DB;

class WorkTaskService
{
    public static function createGroupFromWorkTask($workTaskId)
    {
        $workTask = WorkTask::query()->with(['group'])->find($workTaskId);
        if (! $workTask) {
            return;
        }

        $group = $workTask->group;
        if ($group) {
            return;
        }

        $group = new Group();
        $group->name = "Creation-{$workTask->id}";
        $group->work_task_id = $workTask->id;
        $group->save();

        $group->users()->attach($workTask->user_id, [
            'role' => GroupUserPivotRole::User,
        ]);
        $group->users()->attach($workTask->artist->user_id, [
            'role' => GroupUserPivotRole::Artist,
        ]);

    }

    // 修改价格
    public static function updateWorkTaskPrice($workTaskId, $price)
    {
        $workTask = WorkTask::query()->with(['group'])->find($workTaskId);
        if (! $workTask) {
            return;
        }

        $workTask->price = $price;
        $workTask->save();
    }

    public static function transferMoneyToArtistWallet($workTaskId)
    {
        $workTask = WorkTask::query()->with(['group', 'workTaskStages'])->find($workTaskId);
        if (! $workTask) {
            return;
        }

        // check all stage is finished
        $isAllFinished = $workTask->workTaskStages()
            ->where('is_paid', WorkTaskStage::PayStatusPaid)
            ->where('work_status', '!=', WorkTaskStage::WorkStatusFinished)
            ->doesntExist();

        if (! $isAllFinished) {
            return;
        }

        DB::beginTransaction();
        $artistWallet = Wallet::where('user_id', $workTask->artist->user_id)
            ->where('currency_id', $workTask->currency_id)
            ->lockForUpdate()
            ->first();

        if (! $artistWallet) {
            return;
        }

        // calc balance
        $payFeeRatio = $workTask->artist->pay_fee_ratio;
        $platFeeRatio = $workTask->artist->worktask_plat_fee_ratio;
        if ($workTask->busable_type === WorkTask::TypeService || $workTask->busable_type === WorkTask::TypeProject) {
            $platFeeRatio = $workTask->artist->worktask_plat_fee_ratio;
        } elseif ($workTask->busable_type === WorkTask::TypeProduct) {
            $platFeeRatio = $workTask->artist->product_plat_fee_ratio;
        }

        $platFee = round($workTask->price * $platFeeRatio, 2);
        $platFeeWalletService = new PlatFeeWalletService();
        dump($platFee, $workTask->artist->user->id, $workTask->id);
        $platFeeProcessStage = $platFeeWalletService->minus($workTask->artist->user, Amount::new($platFee, $workTask->currency));
        if ($platFeeProcessStage) {
            $platFee = $platFeeProcessStage->after_amount->amount;
        }
        $payFee = round($workTask->price * $payFeeRatio, 2);
        $amount = round($workTask->price - $payFee - $platFee, 2);

        // update balance
        $beforeBalance = $artistWallet->balance;
        $afterBalance = $beforeBalance + $amount;

        $artistWallet->balance = $afterBalance;
        $artistWallet->save();

        $detail = [
            [
                'type' => 'init',
                'amount' => $workTask->price,
            ],
            [
                'type' => 'pay_fee',
                'action' => WalletTransactionAction::Minus,
                'amount' => $payFee,
            ],
            [
                'type' => 'plat_fee',
                'action' => WalletTransactionAction::Minus,
                'amount' => $platFee,
            ],
            [
                'type' => 'result',
                'amount' => $amount,
            ],
        ];

        $artistWallet->transactions()->create([
            'user_id' => $workTask->artist->user_id,
            'currency_id' => $workTask->currency_id,
            'action' => WalletTransactionAction::Plus,
            'biz_type' => WalletTransactionBizType::WorkTask,
            'status' => WalletTransactionStatus::Successed,
            'before_balance' => $beforeBalance,
            'after_balance' => $afterBalance,
            'amount' => $amount,
            'detail' => $detail,
        ]);

        // service completed_tasks ++
        if ($workTask->busable_type === WorkTask::TypeService) {
            $service = $workTask->busable;
            $service->completed_tasks += 1;
            $service->save();
        }
        DB::commit();

        // notify
        $notify = SystemNotification::make(SystemNotificationScene::WorktaskFinished)
            ->setMeta([
                'work_task_id' => $workTask->id,
            ]);
        $workTask->artist->notify($notify);
        $workTask->group->notify($notify);
        $workTask->user->notify($notify);
    }
}
