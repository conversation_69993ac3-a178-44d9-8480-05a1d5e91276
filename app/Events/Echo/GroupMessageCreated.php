<?php

namespace App\Events\Echo;

use App\Models\Group;
use App\Models\GroupMessage;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcastNow;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class GroupMessageCreated implements ShouldBroadcastNow
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public Group $group;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public GroupMessage $message,
    ) {
        $this->group = $this->message->group;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        $users = $this->group->users;

        $ret = collect($users)
            ->map(function ($user) {
                return new Channel('user_events.'.$user->id);
            })->toArray();

        return $ret;
    }

    public function broadcastWith(): array
    {
        $group = $this->group;
        $message = $this->message;

        return [
            'group' => $this->getGroupById($group->id),
            'message' => $this->getGroupMessageById($message->id),
        ];
    }

    private function getGroupMessageById($messageId)
    {
        $query = GroupMessage::query()
            ->where('id', $messageId);

        $query->with([
            'translate',
            'attachments',
            'replyMessage',
        ]);

        $message = $query->first();

        return $message;
    }

    private function getGroupById($chatId)
    {
        $query = Group::query()
            ->where('id', $chatId);

        $query->with([
            'users:id,name,avatar_id,language_id' => [
                'avatar',
                'language',
                'artist:id,avatar_id,user_id,name' => [
                    'avatar',
                ],
                'roles',
            ],
        ]);

        $group = $query->first();

        $group->users->transform(function ($item) {
            $item->makeHidden('roles');
            $is_artist = $item->hasRole('artist');
            $item->is_artist = $is_artist;
            if (! $is_artist) {
                unset($item->artist);
                $item->artist = null;
            }

            return $item;
        });

        return $group;
    }
}
