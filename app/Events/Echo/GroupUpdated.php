<?php

namespace App\Events\Echo;

use App\Models\Group;
use App\Models\GroupMessage;
use App\Models\User;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcastNow;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class GroupUpdated implements ShouldBroadcastNow
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public Group $group,
        public User $receiver,
    ) {}

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new Channel('user_events.'.$this->receiver->id),
        ];
    }

    public function broadcastWith(): array
    {
        $group = $this->group;

        return [
            'group' => $this->getGroupById($group->id),
        ];
    }

    private function getGroupById($chatId)
    {
        $query = Group::query()
            ->where('id', $chatId);

        $query->with([
            'users:id,name,avatar_id,language_id' => [
                'avatar',
                'language',
                'artist:id,avatar_id,user_id,name' => [
                    'avatar',
                ],
                'roles',
            ],
            'lastMessage',
        ]);

        $group = $query->first();

        $group->users->transform(function ($item) {
            $item->makeHidden('roles');
            $is_artist = $item->hasRole('artist');
            $item->is_artist = $is_artist;
            if (! $is_artist) {
                unset($item->artist);
                $item->artist = null;
            }

            return $item;
        });

        return $group;
    }

}
