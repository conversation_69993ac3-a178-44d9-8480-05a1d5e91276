<?php

namespace App\Events\Echo;

use App\Enums\ChatVisableStatus;
use App\Models\Chat;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcastNow;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ChatCreated implements ShouldBroadcastNow
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public Chat $chat,
    ) {}

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        $users = $this->chat->users;

        $ret = collect($users)
            ->filter(function ($user) {
                return $user->pivot->chat_visable_status === ChatVisableStatus::CanSee;
            })
            ->map(function ($user) {
                return new Channel('user_events.'.$user->id);
            })->toArray();

        return $ret;
    }

    public function broadcastWith(): array
    {
        $chat = $this->chat;

        return [
            'chat' => $this->getChatById($chat->id),
        ];
    }

    private function getChatById($chatId)
    {
        $query = Chat::query()
            ->where('id', $chatId);

        $query->with([
            'users:id,name,avatar_id,language_id' => [
                'avatar',
                'language',
                'artist:id,avatar_id,user_id,name' => [
                    'avatar',
                ],
                'roles',
            ],
            'lastMessage',
        ]);

        $chat = $query->first();

        $chat->users->transform(function ($item) {
            $item->pivot->makeHidden('chat_visable_status');
            $item->makeHidden('roles');
            $is_artist = $item->hasRole('artist');
            $item->is_artist = $is_artist;
            if (! $is_artist) {
                unset($item->artist);
                $item->artist = null;
            }

            return $item;
        });

        return $chat;
    }
}
