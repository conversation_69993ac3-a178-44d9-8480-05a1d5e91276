<?php

namespace App\Events;

use App\Jobs\TranslateJob;
use App\Models\GroupMessage;
use App\Models\GroupUserPivot;
use App\Models\UploadFile;
use App\Service\Translate\TranslateDB;
use Arr;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class GroupMessageSentEvent implements ShouldQueue
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public GroupMessage $message;

    public array $requestData;

    public int $user_id;

    /**
     * Create a new event instance.
     */
    public function __construct(GroupMessage $message, array $requestData, int $userId)
    {
        $this->message = $message;
        $this->requestData = $requestData;
        $this->user_id = $userId;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            // new PrivateChannel('channel-name'),
        ];
    }

    public function triggerGroupUserPivot()
    {
        GroupUserPivot::where('group_id', $this->message->group_id)
            ->update(['order_at_ts' => $this->message->ts]);
    }

    public function updateLastReadCursor()
    {
        GroupUserPivot::where('group_id', $this->message->group_id)
            ->where('user_id', '=', $this->message->sender_uid)
            ->update(['last_read_cursor' => $this->message->id]);
    }

    public function updateAttachments()
    {
        if (! $this->checkRequiredKeys(['attachments'], $this->requestData)) {
            return;
        }

        foreach ($this->requestData['attachments'] as $attachmentId) {
            UploadFile::where('id', $attachmentId)
                ->update([
                    'busable_id' => $this->message->id,
                    'busable_type' => 'group_message',
                ]);
        }
    }

    public function translateMessage($isAsync = true)
    {
        if (! $this->checkRequiredKeys(['content', 'content_lang'], $this->requestData)) {
            return;
        }

        $source_lang = $this->requestData['content_lang'];

        $users = $this->message->group->users;
        $target_langs = [];
        foreach ($users as $user) {
            $target_langs[] = $user->settings['chat_language_code'];
        }
        $target_langs = array_unique($target_langs);

        $translates = [];
        foreach ($target_langs as $target_lang) {
            if ($source_lang === $target_lang) {
                continue;
            }
            $translates[] = TranslateDB::createTranslateByModel($this->message, 'content', $source_lang, $target_lang, $this->user_id);
        }

        if (empty($translates)) {
            return;
        }

        $translate_ids = collect($translates)->pluck('id');

        GroupMessage::where('id', $this->message->id)->update(['translate_id' => $translate_ids[0]]);

        if ($isAsync) {
            TranslateJob::dispatch($translate_ids->toArray())->onQueue('translate');
        } else {
            $job = new TranslateJob($translate_ids->toArray());
            $job->handle();
        }
    }

    private function checkRequiredKeys(array $requiredKeys, array $requestData): bool
    {
        foreach ($requiredKeys as $key) {
            if (! Arr::get($requestData, $key)) {
                return false;
            }
        }

        return true;
    }
}
