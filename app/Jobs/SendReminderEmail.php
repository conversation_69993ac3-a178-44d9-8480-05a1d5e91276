<?php

namespace App\Jobs;

use App\Enums\GroupReminderStatus;
use App\Mail\GroupReminderMail;
use App\Models\GroupReminder;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;

class SendReminderEmail implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        protected GroupReminder $reminder,
        protected int $userId
    ) {}

    public function handle(): void
    {
        $user = User::find($this->userId);
        if (! $user || ! $user->email) {
            return;
        }

        $template = $this->reminder->template;
        $language = $user->language->code ?? 'en';

        Mail::to($user->email)->send(new GroupReminderMail(
            title: $template->getTranslation('title', $language) ?? '',
            content: $template->getTranslation('content', $language) ?? '',
            sender: $this->reminder->sender,
            group: $this->reminder->group
        ));

        $this->reminder->update(['status' => GroupReminderStatus::Successed]);
    }
}
