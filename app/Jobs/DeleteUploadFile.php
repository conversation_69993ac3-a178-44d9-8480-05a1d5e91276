<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Log;
use Storage;

class DeleteUploadFile implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected array $urls;

    /**
     * Create a new job instance.
     *
     * @var string[]
     */
    public function __construct(array $urls)
    {
        $this->urls = array_unique($urls);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info('Starting deletion process for image urls:', ['urls' => $this->urls]);

        try {
            foreach ($this->urls as $url) {
                Log::info("Attempting to delete image with URL: {$url}");
                Storage::delete($url);
                Log::info("Successfully deleted image with URL: {$url}");
            }

            Log::info('Successfully deleted all provided image URLs.');
        } catch (\Exception $e) {
            Log::error("Error deleting images. Exception: {$e->getMessage()}");
        }
    }
}
