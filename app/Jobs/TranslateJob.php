<?php

namespace App\Jobs;

use App\Service\Translate\TranslationManager;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Log;

class TranslateJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 30 * 60;

    protected $ids;

    /**
     * Create a new job instance.
     */
    public function __construct(array $ids)
    {
        $this->ids = $ids;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Log::info('Processing ids: {ids}', ['ids' => $this->ids]);
        try {

            TranslationManager::translateColumnByIds($this->ids);

            Log::info('Translates processing completed for ids: {ids}', ['ids' => $this->ids]);
        } catch (\Exception $e) {
            Log::error("Error processing ids {ids}: {$e->getMessage()}, {$e->getFile()}, {$e->getLine()}", ['ids' => $this->ids]);
        }
    }
}
