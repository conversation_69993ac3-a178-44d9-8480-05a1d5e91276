<?php

namespace App\Jobs;

use App\Models\UploadImage;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Image;
use Log;
use Process;
use Storage;
use Str;

class CompressImage implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 30 * 60;

    protected $uploadImageId;

    protected $magickTempPath;

    /**
     * Create a new job instance.
     */
    public function __construct(int $uploadImageId)
    {
        $this->uploadImageId = $uploadImageId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Log::info("Processing image id: {$this->uploadImageId}");

        try {
            $uploadImage = UploadImage::find($this->uploadImageId);
            if (! $uploadImage) {
                Log::error("Upload image not found for id: {$this->uploadImageId}");

                return;
            }

            $imageSizes = [
                ['type' => 'sm', 'size' => 320],
                ['type' => 'md', 'size' => 650],
                ['type' => 'lg', 'size' => 1250],
            ];

            $dbUpdateData = [
                'is_compressed' => 1,
            ];
            $file = Storage::get($uploadImage->url_og);

            Storage::disk('local')->put($uploadImage->url_og, $file);
            foreach ($imageSizes as $size) {
                $imgPath = $this->compressAndStoreImage($uploadImage->url_og, $size['type'], $size['size']);
                $dbUpdateData["url_{$size['type']}"] = $imgPath;
            }
            Storage::disk('local')->delete($uploadImage->url_og);

            $uploadImage->update($dbUpdateData);

            Log::info("Image processing completed for id: {$uploadImage->id}, updateData: ".json_encode($dbUpdateData));
        } catch (\Exception $e) {
            Log::error("Error processing image id {$uploadImage->id}: {$e->getMessage()}");
        }
    }

    private function compressAndStoreImage($imagePath, string $type, int $size): string
    {
        $imageFolder = pathinfo($imagePath, PATHINFO_DIRNAME);
        $imageName = pathinfo($imagePath, PATHINFO_FILENAME);
        $imageName = Str::replaceLast('_og', '', $imageName);
        $extension = pathinfo($imagePath, PATHINFO_EXTENSION);
        $destImgPath = "{$imageFolder}/{$imageName}_{$type}.webp";

        try {
            $imgOriginal = Image::read(Storage::disk('local')->path($imagePath));

            if ($imgOriginal->isAnimated()) {
                $scaledImagePath = "{$imageFolder}/{$imageName}_scaled.{$extension}";
                $LocalImgPath = Storage::disk('local')->path($imagePath);
                $LocalScaledImgPath = Storage::disk('local')->path($scaledImagePath);
                $LocalDestImgPath = Storage::disk('local')->path($destImgPath);

                $resizeCommand = "magick {$LocalImgPath} -coalesce -resize {$size}x\> -layers optimize {$LocalScaledImgPath}";
                Log::info("Running command: {$resizeCommand}");
                $resizeCommandProcess = Process::timeout(10 * 60);
                if (PHP_OS_FAMILY === 'Linux') {
                    $tempUniqueId = uniqid();
                    $magickTempPath = Storage::disk('local')->path("/tmp/magick/$tempUniqueId/");
                    if (! file_exists($magickTempPath)) {
                        mkdir($magickTempPath, 0777, true);
                    }
                    $resizeCommandProcess->env([
                        'MAGICK_TEMPORARY_PATH' => $magickTempPath,
                    ]);
                    $this->magickTempPath = $magickTempPath;
                    Log::info("Using MAGICK_TEMPORARY_PATH: {$this->magickTempPath}");
                }
                $result = $resizeCommandProcess->run($resizeCommand);
                if ($result->failed()) {
                    throw new \Exception("Error running command: {$result->command()}, exit code: {$result->exitCode()}, output: {$result->output()}");
                }

                $gif2WebpCommand = "gif2webp {$LocalScaledImgPath} -lossy -o {$LocalDestImgPath}";
                Log::info("Running command: {$gif2WebpCommand}");
                $result = Process::timeout(10 * 60)->run($gif2WebpCommand);
                if ($result->failed()) {
                    throw new \Exception("Error running command: {$result->command()}, exit code: {$result->exitCode()}, output: {$result->output()}");
                }
                Storage::put($destImgPath, Storage::disk('local')->get($destImgPath));
                Storage::disk('local')->delete($destImgPath);
                Storage::disk('local')->delete($scaledImagePath);

                return $destImgPath;
            }

            $scaledImage = $imgOriginal->scaleDown(width: $size);
            $img = $scaledImage->toWebp();
            $putImg = $img->toFilePointer();

            Storage::put($destImgPath, $putImg);
            Log::info("Compressed image stored: {$destImgPath}");
        } catch (\Exception $e) {
            throw new \Exception('Error storing image: '.$e->getMessage());
        }

        return $destImgPath;
    }

    public function failed(\Throwable $exception)
    {
        if ($this->magickTempPath) {
            Log::info("Deleting temporary directory: {$this->magickTempPath}");
            Storage::disk('local')->deleteDirectory($this->magickTempPath);
        }
    }
}
