<?php

namespace App\Mail;

use App\Models\Group;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class GroupReminderMail extends Mailable
{
    use Queueable, SerializesModels;

    public function __construct(
        protected string $title,
        protected string $content,
        protected User $sender,
        protected Group $group
    ) {}

    public function build()
    {
        return $this->subject("Group Reminder")
            ->view('emails.group-reminder', [
                'title' => $this->title ?? '',
                'content' => $this->content ?? '',
                'sender' => $this->sender,
                'group' => $this->group,
            ]);
    }
}
