<?php

namespace App\Notifications;

use App\Enums\SystemNotificationScene;
use App\Models\Artist;
use App\Models\Chat;
use App\Models\Group;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Blade;

class SystemNotification extends Notification
{
    use Queueable;

    public SystemNotificationScene $scene;

    public array $title;

    public array $content;

    public array $meta;

    /**
     * Create a new notification instance.
     */
    public function __construct(SystemNotificationScene $scene, array $title = [], array $content = [], array $meta = [])
    {
        $this->scene = $scene;
        $this->title = $title;
        $this->content = $content;
        $this->meta = $meta;
    }

    public static function make(?SystemNotificationScene $scene = null)
    {
        return new self($scene ?? SystemNotificationScene::cases()[0]);
    }

    public function clone(): self
    {
        return clone $this;
    }

    public function setScene(SystemNotificationScene $scene): self
    {
        $this->scene = $scene;

        return $this;
    }

    public function setTitle(array $title): self
    {
        $this->title = $title;

        return $this;
    }

    public function setContent(array $content): self
    {
        $this->content = $content;

        return $this;
    }

    public function setMeta(array $meta): self
    {
        $this->meta = $meta;

        return $this;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        $channelMap = [
            Chat::class => [ChatMessageChannel::class, 'database'],
            Group::class => [GroupMessageChannel::class, 'database'],
            User::class => ['database', EmailChannel::class],
            Artist::class => ['database', EmailChannel::class],
        ];

        return $channelMap[get_class($notifiable)] ?? ['database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->line('The introduction to the notification.')
            ->action('Notification Action', url('/'))
            ->line('Thank you for using our application!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        $channelMap = [
            Chat::class => 'chat',
            Group::class => 'group',
            User::class => 'user',
            Artist::class => 'artist',
        ];

        $receiver = $channelMap[get_class($notifiable)] ?? 'user';

        $template = $this->scene->getTemplate($receiver);
        $this->title = $template['title'];
        $content = $template['content'];

        $this->content = collect($content)
            ->map(function ($template, $lang) {
                $meta = $this->meta;
                $localizedMeta = [];
                foreach ($meta as $key => $v) {
                    if ($this->isMutilLanguage($v)) {
                        $localizedMeta[$key] = $v[$lang] ?? '';
                    } else {
                        $localizedMeta[$key] = $v;
                    }
                }

                return Blade::render($template, $localizedMeta);
            })
            ->toArray();

        return [
            'scene' => $this->scene,
            'meta' => collect($this->meta)
                ->merge([
                    'to' => $receiver,
                ])->toArray(),
            'title' => $this->title,
            'content' => $this->content,
        ];
    }

    private function isMutilLanguage($data): bool
    {
        return is_array($data) && array_key_exists('_lang', $data);
    }

    public function toDatabase(object $notifiable): array
    {
        return $this->toArray($notifiable);
    }

    public function toChatMessage(object $notifiable): array
    {
        return $this->toArray($notifiable);
    }

    public function toGroupMessage(object $notifiable): array
    {
        return $this->toArray($notifiable);
    }

    public function toEmail(object $notifiable): array
    {
        return $this->toArray($notifiable);
    }
}
