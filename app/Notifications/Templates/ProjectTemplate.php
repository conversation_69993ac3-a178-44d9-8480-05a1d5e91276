<?php

namespace App\Notifications\Templates;

use App\Contracts\NotificationTemplateInterface;

class ProjectTemplate extends BaseTemplate implements NotificationTemplateInterface
{
    protected static array $templates = [
        'user_canceled' => [
            'artist' => [
                'title' => [
                    'zh' => '项目已取消',
                    'en' => 'Project canceled',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '您的项目已取消，请重新提交',
                    'en' => 'Your project has been canceled, please resubmit',
                    '_lang' => 'en',
                ],
            ],
            'user' => [
                'title' => [
                    'zh' => '项目已取消',
                    'en' => 'Project canceled',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '您的项目已取消，请重新提交',
                    'en' => 'Your project has been canceled, please resubmit',
                    '_lang' => 'en',
                ],
            ],
        ],
        'artist_canceled' => [
            'artist' => [
                'title' => [
                    'zh' => '项目已取消',
                    'en' => 'Project canceled',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '您的项目已取消，请重新提交',
                    'en' => 'Your project has been canceled, please resubmit',
                    '_lang' => 'en',
                ],
            ],
            'user' => [
                'title' => [
                    'zh' => '项目已取消',
                    'en' => 'Project canceled',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '您的项目已取消，请重新提交',
                    'en' => 'Your project has been canceled, please resubmit',
                    '_lang' => 'en',
                ],
            ],
        ],
    ];
}
