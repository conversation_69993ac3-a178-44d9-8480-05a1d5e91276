<?php

namespace App\Notifications\Templates;

use App\Contracts\NotificationTemplateInterface;

class BaseTemplate implements NotificationTemplateInterface
{
    protected static array $templates = [];

    public static function getTemplate(string $scene, string $receiver = 'user'): array
    {
        if (! isset(static::$templates[$scene])) {
            throw new \InvalidArgumentException("Template scene '{$scene}' not found");
        }

        if (! isset(static::$templates[$scene][$receiver])) {
            throw new \InvalidArgumentException("Template receiver '{$receiver}' not found for scene '{$scene}'");
        }

        return static::$templates[$scene][$receiver];
    }

    public static function getTemplates(): array
    {
        return static::$templates;
    }
}
