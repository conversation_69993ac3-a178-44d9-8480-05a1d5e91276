<?php

namespace App\Notifications\Templates;

use App\Contracts\NotificationTemplateInterface;

class ServiceTemplate extends BaseTemplate implements NotificationTemplateInterface
{
    protected static array $templates = [
        'paid' => [
            'artist' => [
                'title' => [
                    'zh' => '服务订单已支付',
                    'en' => 'Service order paid',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '您的服务订单已支付，请等待艺术家确认',
                    'en' => 'Your service order has been paid, please wait for the artist to confirm',
                    '_lang' => 'en',
                ],
            ],
            'user' => [
                'title' => [
                    'zh' => '服务订单已支付',
                    'en' => 'Service order paid',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '您的服务订单已支付，请等待艺术家确认',
                    'en' => 'Your service order has been paid, please wait for the artist to confirm',
                    '_lang' => 'en',
                ],
            ],
        ],
        'artist_accepted' => [
            'artist' => [
                'title' => [
                    'zh' => '服务订单已确认',
                    'en' => 'Service order confirmed',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '您的服务订单已确认，请等待用户支付',
                    'en' => 'Your service order has been confirmed, please wait for the user to pay',
                    '_lang' => 'en',
                ],
            ],
            'user' => [
                'title' => [
                    'zh' => '服务订单已确认',
                    'en' => 'Service order confirmed',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '您的服务订单已确认，请等待用户支付',
                    'en' => 'Your service order has been confirmed, please wait for the user to pay',
                    '_lang' => 'en',
                ],
            ],
        ],
        'artist_rejected' => [
            'artist' => [
                'title' => [
                    'zh' => '服务订单被拒绝',
                    'en' => 'Service order rejected',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '您的服务订单被拒绝，请重新提交',
                    'en' => 'Your service order has been rejected, please resubmit',
                    '_lang' => 'en',
                ],
            ],
            'user' => [
                'title' => [
                    'zh' => '服务订单被拒绝',
                    'en' => 'Service order rejected',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '您的服务订单被拒绝，请重新提交',
                    'en' => 'Your service order has been rejected, please resubmit',
                    '_lang' => 'en',
                ],
            ],
        ],
        'user_canceled' => [
            'artist' => [
                'title' => [
                    'zh' => '服务订单已取消',
                    'en' => 'Service order canceled',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '您的服务订单已取消，请重新提交',
                    'en' => 'Your service order has been canceled, please resubmit',
                    '_lang' => 'en',
                ],
            ],
            'user' => [
                'title' => [
                    'zh' => '服务订单已取消',
                    'en' => 'Service order canceled',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '您的服务订单已取消，请重新提交',
                    'en' => 'Your service order has been canceled, please resubmit',
                    '_lang' => 'en',
                ],
            ],
        ],
    ];
}
