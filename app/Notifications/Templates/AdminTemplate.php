<?php

namespace App\Notifications\Templates;

use App\Contracts\NotificationTemplateInterface;

class AdminTemplate extends BaseTemplate implements NotificationTemplateInterface
{
    protected static array $templates = [
        'joined_group' => [
            'group' => [
                'title' => [
                    'zh' => '管理员加入群组',
                    'en' => 'Admin joined group',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '管理员已加入群组',
                    'en' => 'Admin joined group',
                    '_lang' => 'en',
                ],
            ],
        ],
    ];
}
