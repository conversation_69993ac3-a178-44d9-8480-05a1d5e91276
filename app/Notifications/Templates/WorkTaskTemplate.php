<?php

namespace App\Notifications\Templates;

use App\Contracts\NotificationTemplateInterface;

class WorkTaskTemplate extends BaseTemplate implements NotificationTemplateInterface
{
    protected static array $templates = [
        'created' => [
            'artist' => [
                'title' => [
                    'zh' => '任务已创建',
                    'en' => 'Task created',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '您的任务已创建，请等待用户确认',
                    'en' => 'Your task has been created, please wait for the user to confirm',
                    '_lang' => 'en',
                ],
            ],
            'user' => [
                'title' => [
                    'zh' => '任务已创建',
                    'en' => 'Task created',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '您的任务已创建，请等待用户确认',
                    'en' => 'Your task has been created, please wait for the user to confirm',
                    '_lang' => 'en',
                ],
            ],
        ],
        'user_canceled' => [
            'artist' => [
                'title' => [
                    'zh' => '任务已取消',
                    'en' => 'Task canceled',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '您的任务已取消，请重新提交',
                    'en' => 'Your task has been canceled, please resubmit',
                    '_lang' => 'en',
                ],
            ],
            'user' => [
                'title' => [
                    'zh' => '任务已取消',
                    'en' => 'Task canceled',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '您的任务已取消，请重新提交',
                    'en' => 'Your task has been canceled, please resubmit',
                    '_lang' => 'en',
                ],
            ],
        ],
        'artist_rejected' => [
            'artist' => [
                'title' => [
                    'zh' => '任务被拒绝',
                    'en' => 'Task rejected',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '您的任务被拒绝，请重新提交',
                    'en' => 'Your task has been rejected, please resubmit',
                    '_lang' => 'en',
                ],
            ],
            'user' => [
                'title' => [
                    'zh' => '任务被拒绝',
                    'en' => 'Task rejected',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '您的任务被拒绝，请重新提交',
                    'en' => 'Your task has been rejected, please resubmit',
                    '_lang' => 'en',
                ],
            ],
        ],
        'wait_pay' => [
            'artist' => [
                'title' => [
                    'zh' => '任务待支付',
                    'en' => 'Task wait pay',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '您的任务待支付，请尽快支付',
                    'en' => 'Your task is waiting for payment, please pay as soon as possible',
                    '_lang' => 'en',
                ],
            ],
            'user' => [
                'title' => [
                    'zh' => '任务待支付',
                    'en' => 'Task wait pay',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '您的任务待支付，请尽快支付',
                    'en' => 'Your task is waiting for payment, please pay as soon as possible',
                    '_lang' => 'en',
                ],
            ],
        ],
        'working' => [
            'artist' => [
                'title' => [
                    'zh' => '任务进行中',
                    'en' => 'Task working',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '您的任务正在进行中，请耐心等待',
                    'en' => 'Your task is in progress, please be patient',
                    '_lang' => 'en',
                ],
            ],
            'user' => [
                'title' => [
                    'zh' => '任务进行中',
                    'en' => 'Task working',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '您的任务正在进行中，请耐心等待',
                    'en' => 'Your task is in progress, please be patient',
                    '_lang' => 'en',
                ],
            ],
        ],
        'stage_finished' => [
            'artist' => [
                'title' => [
                    'zh' => '任务阶段完成',
                    'en' => 'Task stage finished',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '您的任务阶段已完成',
                    'en' => 'Your task stage has been completed',
                    '_lang' => 'en',
                ],
            ],
            'user' => [
                'title' => [
                    'zh' => '任务阶段完成',
                    'en' => 'Task stage finished',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '您的任务阶段已完成',
                    'en' => 'Your task stage has been completed',
                    '_lang' => 'en',
                ],
            ],
            'group' => [
                'title' => [
                    'zh' => '任务阶段完成',
                    'en' => 'Task stage finished',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '您的任务阶段已完成',
                    'en' => 'Your task stage has been completed',
                    '_lang' => 'en',
                ],
            ],
        ],
        'file_created' => [
            'artist' => [
                'title' => [
                    'zh' => '文件已上传',
                    'en' => 'File uploaded',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '您的任务文件已上传',
                    'en' => 'Your task file has been uploaded',
                    '_lang' => 'en',
                ],
            ],
            'user' => [
                'title' => [
                    'zh' => '有新的文件已上传',
                    'en' => 'New file uploaded',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '您的任务有新的文件已上传',
                    'en' => 'Your task has a new file uploaded',
                    '_lang' => 'en',
                ],
            ],
            'group' => [
                'title' => [
                    'zh' => '有新的文件已上传',
                    'en' => 'New file uploaded',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '有新的文件已上传',
                    'en' => 'New file uploaded',
                    '_lang' => 'en',
                ],
            ],
        ],
    ];
}
