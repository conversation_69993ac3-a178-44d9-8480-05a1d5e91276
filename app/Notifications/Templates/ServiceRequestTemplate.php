<?php

namespace App\Notifications\Templates;

use App\Contracts\NotificationTemplateInterface;

class ServiceRequestTemplate extends BaseTemplate implements NotificationTemplateInterface
{
    protected static array $templates = [
        'user_paid' => [
            'artist' => [
                'title' => [
                    'zh' => '服务订单已支付',
                    'en' => 'Service order paid',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '用户已支付服务订单，请前往创作中心查看',
                    'en' => 'User has paid for the service order, please go to the creation center to view',
                    '_lang' => 'en',
                ],
            ],
            'user' => [
                'title' => [
                    'zh' => '服务订单支付成功',
                    'en' => 'Payment successful',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '您已成功支付服务订单，艺术家将尽快处理',
                    'en' => 'Payment successful, the artist will process your order soon',
                    '_lang' => 'en',
                ],
            ],
        ],
        'created' => [
            'artist' => [
                'title' => [
                    'zh' => '收到服务申请',
                    'en' => 'Received service request',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '收到服务申请，请尽快确认',
                    'en' => 'Received service request, please confirm as soon as possible',
                    '_lang' => 'en',
                ],
            ],
            'user' => [
                'title' => [
                    'zh' => '服务申请已提交',
                    'en' => 'Service request submitted',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '您的服务申请已提交，等待艺术家确认',
                    'en' => 'Your service request has been submitted, waiting for artist confirmation',
                    '_lang' => 'en',
                ],
            ],
        ],
        'artist_accepted' => [
            'artist' => [
                'title' => [
                    'zh' => '已接受服务申请',
                    'en' => 'Service request accepted',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '您已接受服务申请，等待用户支付',
                    'en' => 'You have accepted the service request, waiting for user payment',
                    '_lang' => 'en',
                ],
            ],
            'user' => [
                'title' => [
                    'zh' => '服务申请已接受',
                    'en' => 'Service request accepted',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '您的服务申请已接受，请尽快支付',
                    'en' => 'Your service request has been accepted, please pay as soon as possible',
                    '_lang' => 'en',
                ],
            ],
        ],
        'artist_rejected' => [
            'artist' => [
                'title' => [
                    'zh' => '已拒绝服务申请',
                    'en' => 'Service request rejected',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '您已拒绝服务申请',
                    'en' => 'You have rejected the service request',
                    '_lang' => 'en',
                ],
            ],
            'user' => [
                'title' => [
                    'zh' => '服务申请被拒绝',
                    'en' => 'Service request rejected',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '您的服务申请被拒绝，请重新提交',
                    'en' => 'Your service request has been rejected, please resubmit',
                    '_lang' => 'en',
                ],
            ],
        ],
        'user_canceled' => [
            'artist' => [
                'title' => [
                    'zh' => '服务申请已取消',
                    'en' => 'Service request canceled',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '用户已取消服务申请',
                    'en' => 'User has canceled the service request',
                    '_lang' => 'en',
                ],
            ],
            'user' => [
                'title' => [
                    'zh' => '服务申请已取消',
                    'en' => 'Service request canceled',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '您已取消服务申请',
                    'en' => 'You have canceled the service request',
                    '_lang' => 'en',
                ],
            ],
        ],

    ];
}
