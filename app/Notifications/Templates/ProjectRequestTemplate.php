<?php

namespace App\Notifications\Templates;

use App\Contracts\NotificationTemplateInterface;

class ProjectRequestTemplate extends BaseTemplate implements NotificationTemplateInterface
{
    protected static array $templates = [
        'created' => [
            'artist' => [
                'title' => [
                    'zh' => '收到项目申请',
                    'en' => 'Received project request',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '收到新的项目申请，请及时查看',
                    'en' => 'Received a new project request, please check it soon',
                    '_lang' => 'en',
                ],
            ],
            'user' => [
                'title' => [
                    'zh' => '项目申请已提交',
                    'en' => 'Project request submitted',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '您的项目申请已提交，请等待艺术家回应',
                    'en' => 'Your project request has been submitted, waiting for artist response',
                    '_lang' => 'en',
                ],
            ],
        ],
        'user_choosen' => [
            'artist' => [
                'title' => [
                    'zh' => '您已被选中',
                    'en' => 'You have been chosen',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '您已被选中参与该项目，请及时确认',
                    'en' => 'You have been chosen for this project, please confirm soon',
                    '_lang' => 'en',
                ],
            ],
        ],
        'artist_canceled' => [
            'user' => [
                'title' => [
                    'zh' => '艺术家已取消项目',
                    'en' => 'Artist canceled the project',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '艺术家已取消该项目申请',
                    'en' => 'The artist has canceled this project request',
                    '_lang' => 'en',
                ],
            ],
        ],
        'user_canceled' => [
            'artist' => [
                'title' => [
                    'zh' => '项目已被取消',
                    'en' => 'Project canceled',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '用户已取消该项目申请',
                    'en' => 'The user has canceled this project request',
                    '_lang' => 'en',
                ],
            ],
            'user' => [
                'title' => [
                    'zh' => '项目已取消',
                    'en' => 'Project canceled',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '您已取消该项目申请',
                    'en' => 'You have canceled this project request',
                    '_lang' => 'en',
                ],
            ],
        ],
        'artist_rejected' => [
            'user' => [
                'title' => [
                    'zh' => '项目申请被拒绝',
                    'en' => 'Project request rejected',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '艺术家已拒绝该项目申请',
                    'en' => 'The artist has rejected this project request',
                    '_lang' => 'en',
                ],
            ],
            'artist' => [
                'title' => [
                    'zh' => '已拒绝项目申请',
                    'en' => 'Project request rejected',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '您已拒绝该项目申请',
                    'en' => 'You have rejected this project request',
                    '_lang' => 'en',
                ],
            ],
        ],
        'artist_accepted' => [
            'user' => [
                'title' => [
                    'zh' => '项目申请已通过',
                    'en' => 'Project request accepted',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '艺术家已接受您的项目申请，请尽快完成支付',
                    'en' => 'The artist has accepted your project request, please complete the payment soon',
                    '_lang' => 'en',
                ],
            ],
            'artist' => [
                'title' => [
                    'zh' => '已接受项目申请',
                    'en' => 'Project request accepted',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '您已接受该项目申请，等待用户支付',
                    'en' => 'You have accepted this project request, waiting for user payment',
                    '_lang' => 'en',
                ],
            ],
        ],
        'user_paid' => [
            'artist' => [
                'title' => [
                    'zh' => '项目订单已支付',
                    'en' => 'Project order paid',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '用户已支付项目订单，请前往创作中心查看',
                    'en' => 'User has paid for the project order, please go to the creation center to view',
                    '_lang' => 'en',
                ],
            ],
            'user' => [
                'title' => [
                    'zh' => '项目订单支付成功',
                    'en' => 'Payment successful',
                    '_lang' => 'en',
                ],
                'content' => [
                    'zh' => '您已成功支付项目订单，艺术家将尽快处理',
                    'en' => 'Payment successful, the artist will process your project soon',
                    '_lang' => 'en',
                ],
            ],
        ],
    ];
}
