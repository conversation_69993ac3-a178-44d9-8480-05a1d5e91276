<?php

namespace App\Notifications;

use App\Enums\ChatMessageContentType;
use App\Events\ChatMessageSentEvent;
use App\Events\Echo\ChatMessageCreated;
use App\Models\Chat;
use App\Models\ChatMessage;

class ChatMessageChannel
{
    public function send($notifiable, SystemNotification $notification)
    {
        $content = $notification->toChatMessage($notifiable);
        $chat = $notifiable;

        $message = \DB::transaction(function () use ($chat, $content) {
            $chat = Chat::lockForUpdate()->findOrFail($chat->id);

            $messageCount = $chat->message_count;
            $messageCount += 1;

            $message = new ChatMessage();
            $message->chat_id = $chat->id;
            $message->sender_uid = null;
            $message->content_type = ChatMessageContentType::SystemNotification;
            $message->content = $content;
            $message->seq_id = $messageCount;
            $message->translate_id = null;
            $message->save();

            $chat->message_count = $messageCount;
            $chat->save();

            return $message;
        });

        // $message = \DB::transaction(function () use ($message) {
        //     $event = new ChatMessageSentEvent($message, [], 0);
        //     // $event->updateLastMessageTimestamp();
        //     // $event->translateMessageAsync();

        //     return $message;
        // });

        event(new ChatMessageCreated($message));

    }
}
