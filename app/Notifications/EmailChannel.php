<?php

namespace App\Notifications;

use App\Mail\EmailNotification;
use App\Models\Artist;
use App\Models\User;
use Mail;

class EmailChannel
{
    public function send($notifiable, SystemNotification $notification)
    {
        $user = null;
        if ($notifiable instanceof User) {
            $user = $notifiable;
        }

        if ($notifiable instanceof Artist) {
            $user = $notifiable->user;
        }

        if ($user == null) {
            return;
        }

        $email = $user->email;
        $language = $user->language->code ?? 'en';

        $data = $notification->toEmail($notifiable);
        $title = $data['title'][$language] ?? '';
        $content = $data['content'][$language] ?? '';

        Mail::to(['email' => $email])->send(new EmailNotification([
            'title' => $title,
            'content' => $content,
        ]));
    }
}
