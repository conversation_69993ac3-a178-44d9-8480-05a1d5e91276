<?php

namespace App\Notifications;

use App\Enums\GroupMessageContentType;
use App\Events\Echo\GroupMessageCreated;
use App\Events\GroupMessageSentEvent;
use App\Models\Group;
use App\Models\GroupMessage;

class GroupMessageChannel
{
    public function send($notifiable, SystemNotification $notification)
    {

        $content = $notification->toChatMessage($notifiable);

        $message = \DB::transaction(function () use ($notifiable, $content) {
            $group = Group::lockForUpdate()->findOrFail($notifiable->id);
            $messageCount = $group->message_count;
            $messageCount += 1;

            $message = new GroupMessage();
            $message->group_id = $notifiable->id;
            $message->sender_uid = null;
            $message->content_type = GroupMessageContentType::SystemNotification;
            $message->seq_id = $messageCount;
            $message->content = $content;
            $message->save();

            $group->message_count = $messageCount;
            $group->save();

            return $message;
        });

        $message = \DB::transaction(function () use ($message) {
            $event = new GroupMessageSentEvent($message, [], 0);
            $event->triggerGroupUserPivot();

            return $message;
        });

        event(new GroupMessageCreated($message));
    }
}
