<?php

namespace App\Utils;

use Http;
use Illuminate\Support\Facades\Storage;

class Utils
{
    public static function storageUrl($url)
    {
        if (! $url) {
            return '';
        }

        return (filter_var($url, FILTER_VALIDATE_URL)) ? $url : Storage::url($url);
    }

    public static function videoPreviewImage($upload_type, $vid)
    {
        $video_preview_image = '';

        if ($upload_type === 'youtube_link') {
            $video_preview_image = "https://img.youtube.com/vi_webp/{$vid}/maxresdefault.webp";
        }

        if ($upload_type === 'bilibili_link') {
            $apiUrl = "https://api.bilibili.com/x/web-interface/view?bvid={$vid}";

            $response = Http::get($apiUrl);

            if ($response->successful()) {
                $data = $response->json();
                $video_preview_image = $data['data']['pic'] ?? '';
            }
        }

        return $video_preview_image;
    }
}
