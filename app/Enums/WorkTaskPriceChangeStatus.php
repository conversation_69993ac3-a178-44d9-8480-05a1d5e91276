<?php

namespace App\Enums;

enum WorkTaskPriceChangeStatus: string
{
    case PENDING = 'pending';     // 待审核
    case WAIT_PAY = 'wait_pay';   // 已同意待支付
    case PAID = 'paid';           // 已支付完成
    case REJECTED = 'rejected';    // 已拒绝
    case CANCELED = 'canceled';    // 已取消

    public function label(): string
    {
        return match ($this) {
            self::PENDING => '待审核',
            self::WAIT_PAY => '已同意待支付',
            self::PAID => '已支付完成',
            self::REJECTED => '已拒绝',
            self::CANCELED => '已取消',
        };
    }
}
