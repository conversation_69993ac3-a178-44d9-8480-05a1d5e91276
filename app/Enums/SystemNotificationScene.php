<?php

namespace App\Enums;

use App\Models\NotificationTemplate;
use App\Notifications\Templates\AdminTemplate;
use App\Notifications\Templates\ProjectRequestTemplate;
use App\Notifications\Templates\ProjectTemplate;
use App\Notifications\Templates\ServiceRequestTemplate;
use App\Notifications\Templates\ServiceTemplate;
use App\Notifications\Templates\WorkTaskTemplate;

enum SystemNotificationScene: string
{
    public const SERVICE_GROUP = 'service';

    public const SERVICE_REQUEST_GROUP = 'service_request';

    public const PROJECT_GROUP = 'project';

    public const PROJECT_REQUEST_GROUP = 'project_request';

    public const WORKTASK_GROUP = 'worktask';

    public const ADMIN_GROUP = 'admin';

    public const WORKTASK_PRICE_CHANGE_GROUP = 'price_change';

    public const USER_APPLICANT_GROUP = 'user_applicant';

    // Service 场景
    case ServicePaid = 'service.paid';
    case ServiceArtistAccepted = 'service.artist_accepted';
    case ServiceArtistRejected = 'service.artist_rejected';
    case ServiceUserCanceled = 'service.user_canceled';

    // Service Request 场景
    case ServiceRequestCreated = 'service_request.created';
    case ServiceRequestArtistAccepted = 'service_request.artist_accepted';
    case ServiceRequestArtistRejected = 'service_request.artist_rejected';
    case ServiceRequestUserCanceled = 'service_request.user_canceled';
    case ServiceRequestUserPaid = 'service_request.user_paid';

    // Project 场景
    case ProjectUserCanceled = 'project.user_canceled';
    case ProjectArtistCanceled = 'project.artist_canceled';

    // Project Request 场景
    case ProjectRequestCreated = 'project_request.created';
    case ProjectRequestUserChoosen = 'project_request.user_choosen';
    case ProjectRequestArtistCanceled = 'project_request.artist_canceled';
    case ProjectRequestUserCanceled = 'project_request.user_canceled';
    case ProjectRequestArtistRejected = 'project_request.artist_rejected';
    case ProjectRequestArtistAccepted = 'project_request.artist_accepted';
    case ProjectRequestUserPaid = 'project_request.user_paid';

    // Product 场景
    case ProductOrderPaid = 'product_order.paid';

    // WorkTask 场景
    case WorktaskCreated = 'worktask.created';
    case WorktaskUserCanceled = 'worktask.user_canceled';
    case WorktaskArtistCanceled = 'worktask.artist_canceled';
    case WorktaskArtistRejected = 'worktask.artist_rejected';
    case WorktaskWaitPay = 'worktask.wait_pay';
    case WorktaskWorking = 'worktask.working';
    case WorktaskFinished = 'worktask.finished';
    case WorktaskStageFinished = 'worktask.stage_finished';
    case WorktaskFileCreated = 'worktask.file_created';

    // WorkTask File Change Request 场景
    case WorkTaskFileChangeRequestCreated = 'work_task_file_change_request.created';
    case WorkTaskFileChangeRequestUpdated = 'work_task_file_change_request.updated';

    // Artist Rec Review 场景
    case ArtistRecReviewCreated = 'artist_rec_review.created';

    // User Rec Review 场景
    case UserRecReviewCreated = 'user_rec_review.created';

    // Admin 场景
    case AdminJoinedGroup = 'admin.joined_group';

    // Group 场景
    case GroupReminder = 'group.reminder';

    // WorkTask Price Change 场景
    case WorkTaskPriceChangeUserCreated = 'price_change.user_create';
    case WorkTaskPriceChangeArtistCreated = 'price_change.artist_create';
    case WorkTaskPriceChangeRejected = 'price_change.rejected';
    case WorkTaskPriceChangeCanceled = 'price_change.canceled';
    case WorkTaskPriceChangeWaitPay = 'price_change.wait_pay';
    case WorkTaskPriceChangePaid = 'price_change.paid';

    // User Applicant 场景
    case UserApplicantArtistAccepted = 'user_applicant.accepted';
    case UserApplicantArtistRejected = 'user_applicant.rejected';

    /**
     * 获取场景所属的组
     */
    private function getType(): string
    {
        return explode('.', $this->value)[0];
    }

    /**
     * 获取场景的动作
     */
    private function getEvent(): string
    {
        return explode('.', $this->value)[1];
    }

    /**
     * 获取对应的模板类
     */
    private function getTemplateClass(): string
    {
        return match ($this->getType()) {
            self::SERVICE_GROUP => ServiceTemplate::class,
            self::SERVICE_REQUEST_GROUP => ServiceRequestTemplate::class,
            self::PROJECT_GROUP => ProjectTemplate::class,
            self::PROJECT_REQUEST_GROUP => ProjectRequestTemplate::class,
            self::WORKTASK_GROUP => WorkTaskTemplate::class,
            self::ADMIN_GROUP => AdminTemplate::class,
        };
    }

    /**
     * 获取通知模板
     */
    public function getTemplate(string $receiver = 'user'): array
    {
        $template = NotificationTemplate::query()
            ->where('type', $this->getType())
            ->where('event', $this->getEvent())
            ->where('receiver', $receiver)
            ->first();

        if (! $template) {
            return [
                'title' => '',
                'content' => '',
            ];
        }

        return [
            'title' => $template->title,
            'content' => $template->content,
        ];
    }
}
