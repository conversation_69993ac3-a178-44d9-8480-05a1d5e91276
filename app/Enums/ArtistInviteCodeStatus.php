<?php

namespace App\Enums;

enum ArtistInviteCodeStatus: string
{
    case CanUse = 'can_use';
    case Used = 'used';
    case WaitConfirm = 'wait_confirm';

    public function color(): string
    {
        return match ($this) {
            self::CanUse => 'gray',
            self::Used => 'success',
            self::WaitConfirm => 'warning',
        };
    }

    public static function toArray(): array
    {
        foreach (self::cases() as $case) {
            $array[$case->value] = $case->name;
        }

        return $array;
    }

    public static function values(): array
    {
        return array_map(fn (ArtistInviteCodeStatus $status) => $status->value, self::cases());
    }

    public static function rules(): string
    {
        return implode(',', self::values());
    }
}
