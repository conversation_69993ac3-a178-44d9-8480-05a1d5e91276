<?php

if (! function_exists('getAppKeyForAes128')) {
    function getAppKeyForAes128()
    {
        $key = base64_decode(env('APP_KEY'));

        return substr($key, 0, 16);
    }
}

if (! function_exists('aes128Encrypt')) {
    function aes128Encrypt($plaintext)
    {
        $key = getAppKeyForAes128();
        $iv = random_bytes(openssl_cipher_iv_length('aes-128-cbc'));

        $encrypted = openssl_encrypt($plaintext, 'aes-128-cbc', $key, OPENSSL_RAW_DATA, $iv);

        return base64_encode($iv.$encrypted);
    }
}

if (! function_exists('aes128Decrypt')) {
    function aes128Decrypt($ciphertextBase64)
    {
        $key = getAppKeyForAes128();
        $data = base64_decode($ciphertextBase64);
        $ivLength = openssl_cipher_iv_length('aes-128-cbc');
        $iv = substr($data, 0, $ivLength);
        $encrypted = substr($data, $ivLength);

        $decrypted = openssl_decrypt($encrypted, 'aes-128-cbc', $key, OPENSSL_RAW_DATA, $iv);

        return $decrypted;
    }
}
