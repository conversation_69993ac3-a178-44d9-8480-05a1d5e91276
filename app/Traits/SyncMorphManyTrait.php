<?php

namespace App\Traits;

use Illuminate\Database\Eloquent\Relations\MorphMany;

trait SyncMorphManyTrait
{
    public function syncMorphMany(MorphMany $relation, array $newIds, bool $delete = false)
    {
        $currentIds = $relation->pluck($relation->getRelated()->getKeyName())->toArray();

        $idsToDelete = array_diff($currentIds, $newIds);
        $idsToAdd = array_diff($newIds, $currentIds);

        $related = $relation->getRelated();
        if ($delete) {
            $related->whereIn($related->getKeyName(), $idsToDelete)->delete();
        } else {
            $foreignKey = $relation->getForeignKeyName();
            $morphType = $relation->getMorphType();
            $related->whereIn($related->getKeyName(), $idsToDelete)
                ->update([$foreignKey => null, $morphType => null]);
        }

        foreach ($idsToAdd as $id) {
            $model = $related->find($id);
            if ($model) {
                $foreignKey = $relation->getForeignKeyName();
                $morphType = $relation->getMorphType();

                $model->{$foreignKey} = $this->id;
                $model->{$morphType} = get_class($this);
                $model->save();
            }
        }
    }
}
