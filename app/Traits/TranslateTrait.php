<?php

namespace App\Traits;

use App\Casts\TranslateCast;
use Illuminate\Database\Eloquent\Casts\Json;
use Illuminate\Http\Request;

trait TranslateTrait
{
    public function setTrans($key, $val, $lang): self
    {
        if (! $lang) {
            $lang = config('app.locale');
        }

        $translations = [
            $lang => $val,
            '_lang' => $lang,
        ];
        $this->setTranslations($key, $translations);

        return $this;
    }

    public function setTransByReq($key, Request $request, $isCreate = false): self
    {
        if (is_array($request->input($key))) {
            $translations = $request->input($key);

            $translations = array_map(function ($item) {
                return $item ?? '';
            }, $translations);

            unset($translations['_key']);
            if (! $isCreate) {
                unset($translations['_lang']);
            }
            foreach ($translations as $lang => $val) {
                $this->setTranslation($key, $lang, $val);
            }

            return $this;
        }

        $val = $request->input($key) ?? '';
        $lang = $request->input("{$key}_lang");
        if (! $lang) {
            $lang = config('app.locale');
        }

        $translations = [
            $lang => $val,
        ];

        if ($isCreate) {
            $translations['_lang'] = $lang;
        } else {
            $value = $this->getRawOriginal($key);
            $jsonValue = Json::decode($value, true);
            if (empty($jsonValue)) {
                $jsonValue = [];
            }
            if (! array_key_exists('_lang', $jsonValue)) {
                $translations['_lang'] = $lang;
            }
        }

        $this->setTranslations($key, $translations);

        return $this;
    }

    public function getCasts(): array
    {
        return array_merge(
            parent::getCasts(),
            array_fill_keys($this->getTranslatableAttributes(), TranslateCast::class),
        );
    }

    protected function filterTranslations(mixed $value = null, ?string $locale = null, ?array $allowedLocales = null): bool
    {
        if ($value === null) {
            return false;
        }

        if ($value === '') {
            return true;
        }

        if ($allowedLocales === null) {
            return true;
        }

        if (! in_array($locale, $allowedLocales)) {
            return false;
        }

        return true;
    }
}
