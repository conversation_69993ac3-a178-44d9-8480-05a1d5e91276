<?php

namespace App\Traits;

use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Support\Facades\Auth;

trait HasSavedStatusTrait
{
    public function savedUsers(): MorphToMany
    {
        return $this->morphToMany(User::class, 'user_savable')->withTimestamps();
    }

    public function isSaved(): Attribute
    {
        return Attribute::make(
            get: function () {
                if (! Auth::check()) {
                    return false;
                }

                if (! $this->relationLoaded('savedUsers')) {
                    return false;
                }

                return $this->savedUsers->contains('id', Auth::id());
            },
        );
    }

    public function scopeWithSavedStatus(Builder $query): Builder
    {
        if (! Auth::check()) {
            return $query;
        }

        return $query->with(['savedUsers' => function ($query) {
            $query->where('user_id', Auth::id());
        }]);
    }
}
