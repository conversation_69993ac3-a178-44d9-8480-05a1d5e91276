<?php

namespace App\Exceptions;

use App\Enums\ErrorCode;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class BusinessException extends Exception
{
    private ErrorCode $errorCode;

    public function __construct(ErrorCode $code, string $message = '')
    {
        $this->errorCode = $code;
        parent::__construct($message, $code->value);
    }

    /**
     * Render the exception as an HTTP response.
     */
    public function render(Request $request): JsonResponse
    {
        return response()->json([
            'code' => $this->errorCode->value,
            'message' => $this->getMessage(),
        ], 400);
    }
}
