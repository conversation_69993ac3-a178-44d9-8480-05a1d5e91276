<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use App\Models\Artist;
Use App\Models\User;
use App\Models\Tag;
use App\Models\Image;
use App\Models\Artwork;
use App\Models\Service;

class ArtistController extends Controller
{
    public function pageArtists($level=0,$type=0){
		$artists = Artist::orderByRaw('ISNULL(sort), sort ASC')->paginate(5);
		$artists = Artist::getPort($artists);
		
		$commissions = [];
		// $user = Auth::user();
		// if($user){
		// $commissions = Commission::where('user_id',$user->id)->get();
		// }
		
		return view('content/artists', compact('artists','commissions'));
		// return($artists);
    }

    public function artistDetail($artist_id){
		$artist = Artist::where('id',$artist_id)->first();
		$images = Artwork::where('artist_id',$artist_id)->orderByRaw('ISNULL(sort), sort ASC')->get();
		$artist->images = $images;
		$tags = Tag::join('artist_tag','artist_tag.tag_id','=','tags.id')->where('artist_tag.artist_id',$artist_id)->orderBy('name_en')->get();
		$services = Service::where('artist_id',$artist_id)->get();
		
		// $now_col = 1;
		// $max_col = 4;
		// $image_cols = array(1=>array(),2=>array(),3=>array(),4=>array());
		// foreach($images as $key=>$image){
		//   if($now_col > $max_col){
		//     $now_col = 1;
		//   }
		//   array_push($image_cols[$now_col],$image);
		//   $now_col ++ ;
		

		// }
		// dd($image_cols);
		if($artist->display == 1){
			return view('content/artist_detail',compact('artist','tags','services'));
		}
		else{
			return 'artist hidden';
		}
    
    }
}
