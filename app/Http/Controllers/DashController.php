<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;


use App\Models\Commission;
use App\Models\Artist;
use App\Models\ComPrefArtist;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\OrderPayment;
use App\Models\User;
use App\Models\ComReview;
use App\Models\ProductOrder;
use App\Models\ProductOrderItem;
use App\Models\ProductSkuItem;
use App\Models\UserWallet;
use App\Models\WalletTransaction;
use App\Models\ChatMessage;

use App\Events\MessageSent;


class DashController extends Controller
{
   


    public function commissions(){
      if (Auth::check()) {
        $user = Auth::user();
        $commissions = Commission::where('user_id',$user->id)->where('display',1)->orderBy('created_at','DESC')->get();
        $commissions = Commission::statusRead($commissions);
        return view('dashboard/commissions',compact('commissions'));
      }
      else{
        return redirect('/signin');
      }
    }


    public function commissionDetail($com_id){
      $user = Auth::user();
      $commission = Commission::where('id',$com_id)->first();
      $commission = Commission::statusRead($commission,0);
      $commission->detail_objs = json_decode($commission->detail);
      $commission->ref_files = json_decode($commission->ref_files);
      if($user && $commission && $commission->user_id == $user->id ){
        $pref_artists = Artist::join('com_pref_artists','com_pref_artists.artist_id','=','artists.id')
                              ->where('com_pref_artists.com_id',$com_id)
                              ->select('artists.*')
                              ->orderBy('sort')
                              ->get();
        $ava_artists = Artist::join('com_ava_artists','com_ava_artists.artist_id','=','artists.id')
                            ->where('com_ava_artists.com_id',$com_id)
                            // ->select('artists.*','com_ava_artists.*')
                            ->get();
        
        $order = Order::where('com_id',$com_id)->first();
        if($order){
          $order_items = OrderItem::where('order_id',$order->id)->get();
          $order_payments = OrderPayment::where('order_id',$order->id)->get();
          $order_payments = OrderPayment::statusRead($order_payments);
        }
        else{
          $order_items = [];
          $order_payments = [];
        }

        if($commission->status >= 9){
          foreach($ava_artists as $ava_artist){
            $review = ComReview::where('com_id',$com_id)->where('artist_id', $ava_artist->artist_id)->first();
            $ava_artist->review = $review;
          }
        }

        $pm = User::where('id',$commission->pm_user_id)->first();
        if($pm){
          $pm->review = ComReview::where('com_id',$com_id)->where('pm_user_id', $pm['id'])->first();
        }



        
                            // ->groupBy('role_id');
        
        return view('dashboard/com_detail',compact('commission','pref_artists','ava_artists','order','order_items','order_payments','pm'));
     }
     else{
       return redirect('/dashboard/commissions');
     }
    }

    public function sortArtists(Request $request){
      $user = Auth::user();
      if($user){
        $sorted_artist_ids = $request['sorted_artist_ids'];
        foreach($sorted_artist_ids as $key=>$artist_id){
          $com_pref_artist = ComPrefArtist::where('com_id',$request['com_id'])->where('artist_id',$artist_id)->first();
          $com_pref_artist->sort = $key;
          $com_pref_artist->save();
        }
      }
    }

    public function removeArtist(Request $request){
      $user = Auth::user();
      if($user){
        $remove_artist = ComPrefArtist::where('com_id',$request['com_id'])->where('artist_id',$request['artist_id'])->first();
        $remove_artist->delete();
        $return_json =  array('status'=>'success','message'=>'Remove Success','code'=>200);
        return $return_json;
      }
    }

    public function submitReview(Request $request){
      $user = Auth::user();
      if($user){
        $new_review_data = array('com_id'=>$request['com_id'],'user_id'=>$request['user_id'],'status'=>1,
                                  'artist_id'=>$request['artist_id'],'pm_user_id'=>$request['pm_user_id'],'rating'=>$request['rating'],
                                  'content'=>$request['content']);
        ComReview::create($new_review_data);
        $return_json =  array('status'=>'success','message'=>'Review Success','code'=>200);
        return $return_json;
      }
    }

    public function orders(){
      if (Auth::check()) {
        $user = Auth::user();
        $orders = ProductOrder::where('user_id',$user->id)->where('pay_status',1)->orderBy('created_at','DESC')->get();
        // $commissions = Commission::statusRead($commissions);
        return view('dashboard/orders',compact('orders'));
      }
      else{
        return redirect('/signin');
      }
    }

    public function orderDetail($order_id){
      $user = Auth::user();
      $order = ProductOrder::where('id',$order_id)->where('pay_status',1)->first();
      if ($user && $order && $user->id == $order->user_id) {
      
        $order_items = ProductOrderItem::where('order_id',$order_id)->get();
        
        // $commissions = Commission::statusRead($commissions);
        return view('dashboard/order_detail',compact('order','order_items'));
      }
      else{
        return 'permission denied';
      }
    }

    public function downloadFile($order_item_id){
      $user = Auth::user();
      $order_item = ProductOrderItem::where('id',$order_item_id)->first();
      if($user && $order_item && $order_item->user_id == $user->id){
        $sku_item = ProductSkuItem::where('id',$order_item->sku_id)->first();
        return Storage::download($sku_item->file_url);
      }
      else{
        return 'permission denied';
      }
    }

    public function profile(){
      $user = Auth::user();
      if($user){
        return view('dashboard/profile',compact('user'));
      }
    }

    public function updateInfo(Request $request){

    }

    public function changePassword(Request $request){
      
    }

    public function wallet(){
      $user = Auth::user();
      if($user){
        $wallet_currencies = UserWallet::where('user_id',$user->id)->get();
        $wallet_trans = WalletTransaction::where('user_id',$user->id)->get();
        return view('dashboard/wallet',compact('user','wallet_currencies','wallet_trans'));
      }
    }

    public function chat(){
      $user = Auth::user();
      if($user){
        $commissions = Commission::where('chat_open',1)->get();
        return view('dashboard/chat',compact('commissions'));
      }
      
    }

    public function pusherAuth(Request $request){
      $key = env('PUSHER_APP_KEY');
      $secret = env('PUSHER_APP_SECRET');
      // $com_id = $request->com_id;
      $channel = $request->channel_name;
      $socket_id  = $request->socket_id;
      $string = $socket_id.":".$channel;
      $sig = hash_hmac('SHA256', $string, $secret);
      $final = $key.":".$sig;
      $return_json = array('auth' => $final);
      return Response($return_json,200);
           
    }

    public function fetchMessage(Request $request){
      $messages = ChatMessage::where('com_id',$request->com_id)->get();
      $return_json =  array('status'=>'success','message'=>'Review Success','code'=>200,'data'=>$messages);
      return $return_json;
    }

    public function sendMessage(Request $request){
     
      $new_message = event(new MessageSent($request->all()));
      ChatMessage::create($request->only(['from_id','com_id','body']));
      
      $return_json =  array('status'=>'success','message'=>'Send Success','code'=>200);
      return $return_json;
      
      
      
    }

    // public function paymentStripe($order_id){

    //   $order = Order::where('id',$order_id)->first();
    //   $product_name = 'Commission Order '. $order_id;
    //   $stripe_price = $order->price * 100;
    //   // return($order->price);
      
    //   $stripe = new \Stripe\StripeClient('sk_test_51Lq19XLKgx0baDH0Cc7BffIJ4ExnZR5JPhqji80KkZGG6OD6it6S2eMNWqpFPNors0SSOcJ9xbXADXDiSD8ocAEy001eLOUloD');
    //   $new_product = $stripe->products->create(
    //     [
    //       'name' => $product_name,
    //       'default_price_data' => ['unit_amount' => $stripe_price, 'currency' => 'usd'],
    //       'expand' => ['default_price'],
    //     ]
    //   );

      
    //   $price_id = $new_product->default_price->id;

    //   \Stripe\Stripe::setApiKey('sk_test_51Lq19XLKgx0baDH0Cc7BffIJ4ExnZR5JPhqji80KkZGG6OD6it6S2eMNWqpFPNors0SSOcJ9xbXADXDiSD8ocAEy001eLOUloD');
    //   $checkout_session = \Stripe\Checkout\Session::create([
    //     'line_items' => [[
    //       # Provide the exact Price ID (e.g. pr_1234) of the product you want to sell
    //       'price' => $price_id,
    //       'quantity' => 1,
    //     ]],
    //     'mode' => 'payment',
    //     'success_url' => 'https://www.eldasolar.com/success',
    //     'cancel_url' => 'https://www.eldasolar.com/cancel',
    //     'automatic_tax' => [
    //       'enabled' => false,
    //     ],
    //   ]);

    //   return redirect($checkout_session->url);
    //   // header("HTTP/1.1 303 See Other");
    //   // header("Location: " . $checkout_session->url);





    // }

    public function paypalOnApprove(Request $request){
      $user = Auth::user();
      $order =  Order::where('id',$request['order_id'])->first();
      $payment = OrderPayment::where('id',$request['payment_id'])->first();
      $update_payment_data = array(
        // 'order_id'=>$order->id,
        // 'user_id'=>$order->user_id,
        'pay_type'=>'paypal',
        'status'=>3,
        'pay_status'=>1,
        // 'pay_amount'=>$request['pay_amount'],
        'third_party_code'=>$request['paypal_order_id'],
        'pay_note'=>json_encode($request['full_data']),
        'paid_at'=> date('Y-m-d H:i:s', time())
      );
      $payment->update($update_payment_data);
      $payment->save();
      // OrderPayment::create($new_payment_data);

      $order->pay_status = 1;
      $order->status = 2;
      $order->paid = $order->paid + $request['pay_amount'];
      $order->save();
      $return_json = array('status'=>'success','message'=>'Remove Success','code'=>200);
      return $return_json;
      // if($user && $order){
        


      // }
    }

    public function paymentPage($payment_id){
      $user = Auth::user();
      $order_payment = OrderPayment::where('id',$payment_id)->first();
      if($user->id == $order_payment->user_id || $user->role_id == 1){
        $order = Order::where('id',$order_payment->order_id)->first();
        $order_user = User::where('id',$order->user_id)->first();
        $commission = Commission::where('id',$order->com_id)->first();
        if($order){
          $order_items = OrderItem::where('order_id',$order->id)->get();
          $order_payments = OrderPayment::where('order_id',$order->id)->get();
          $order_payments = OrderPayment::statusRead($order_payments);
        }
        else{
          $order_items = [];
          $order_payments = [];
        }
        return view('dashboard/payment',compact('order','order_items','order_payment','commission','user'));
      }
      else{
        return 'permission denied';
      }
    }

    
}
