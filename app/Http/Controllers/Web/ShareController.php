<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\Artist;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Imagick;
use ImagickDraw;
use ImagickPixel;

class ShareController extends Controller
{
    public function twitterSharePage(Request $request)
    {
        $scence = $request->scence;
        $id = $request->id;

        if ($scence == 'artist') {
            $artist = Artist::findOrFail($id);
            $title = "PIPIPEN - {$artist->name}";
            $description = 'Built by creators, for creators. Let your inspiration and creation reach further, connect with artist across the globe.';
            $image = route('share.twitter.image', ['scence' => $scence, 'id' => $id]);
            $url = url("/{$scence}/{$id}");
        }

        return view('share.index', [
            'title' => $title,
            'description' => $description,
            'image' => $image,
            'url' => $url,
        ]);
    }

    public function twitterShareCardImage(Request $request)
    {
        $scence = $request->scence;
        $id = $request->id;
        $watermark = false;

        if ($scence == 'artist') {
            $artist = Artist::findOrFail($id);
            $coverUrl = $artist->cover->getRawOriginal('url_lg');
            $avatarUrl = $artist->avatar->getRawOriginal('url_lg');
            $name = $artist->name;

            // 创建主图片
            $cover = new Imagick;
            $cover->readImageBlob(Storage::get($coverUrl));

            // 设定分享图尺寸
            $width = 1200;
            $height = 630;
            $cover->cropThumbnailImage($width, $height);

            // 处理头像
            $avatar = new Imagick;
            $avatar->readImageBlob(Storage::get($avatarUrl));
            $avatarSize = 180;
            $avatar->cropThumbnailImage($avatarSize, $avatarSize);

            // 创建圆形蒙版
            $mask = new Imagick;
            $mask->newImage($avatarSize, $avatarSize, new ImagickPixel('transparent'));
            $draw = new ImagickDraw;
            $draw->setFillColor(new ImagickPixel('black'));
            $draw->circle($avatarSize / 2, $avatarSize / 2, $avatarSize / 2, 0);
            $mask->drawImage($draw);
            $avatar->compositeImage($mask, Imagick::COMPOSITE_COPYOPACITY, 0, 0);

            // 添加白色边框
            $borderSize = 6;
            $border = new Imagick;
            $border->newImage($avatarSize + $borderSize * 2, $avatarSize + $borderSize * 2, new ImagickPixel('transparent'));
            $draw = new ImagickDraw;
            $draw->setFillColor(new ImagickPixel('white'));
            $draw->circle(($avatarSize + $borderSize * 2) / 2, ($avatarSize + $borderSize * 2) / 2, ($avatarSize + $borderSize) / 2, 0);
            $border->drawImage($draw);

            // 将头像合成到带边框的图片上
            $border->compositeImage($avatar, Imagick::COMPOSITE_OVER, $borderSize, $borderSize);
            $avatar = $border;
            $avatarSize = $avatarSize + $borderSize * 2;

            // 合成头像到主图
            $x = (int) (($width - $avatarSize) / 2);
            $y = (int) (($height - $avatarSize) / 2 / 2);
            $cover->compositeImage($avatar, Imagick::COMPOSITE_OVER, $x, $y);

            $fontPath = public_path('Poppins-Bold.ttf');
            if (file_exists('/usr/share/fonts/opentype/noto/NotoSansCJK-Bold.ttc')) {
                $fontPath = '/usr/share/fonts/opentype/noto/NotoSansCJK-Bold.ttc';
            }
            if (file_exists(public_path('storage/NotoSansCJK-Bold.ttc'))) {
                $fontPath = public_path('storage/NotoSansCJK-Bold.ttc');
            }

            // 添加名字文字
            $draw = new ImagickDraw;
            $draw->setFont($fontPath);
            $draw->setFontSize(42);
            $draw->setFillColor(new ImagickPixel('white'));
            $draw->setTextAlignment(Imagick::ALIGN_CENTER);
            $cover->annotateImage($draw, $width / 2, $height / 2 + 36, 0, $name);

            if ($watermark) {
                // 添加水印
                $w1 = 40;
                $h1 = $height - 40;
                $fontSize = 28;
                $content = "Pipipen@{$name}";

                // 绘制水印文字
                $draw = new ImagickDraw;
                $draw->setFont($fontPath);
                $draw->setFontSize($fontSize);
                $draw->setFillColor(new ImagickPixel('white'));
                $draw->setTextAlignment(Imagick::ALIGN_LEFT);
                $draw->setTextUnderColor(new ImagickPixel('#000000'));
                $cover->annotateImage($draw, $w1, $h1, 0, $content);
            }

            // 输出图片
            $cover->setImageFormat('jpeg');
            $cover->setImageCompressionQuality(90);
            $ret = $cover->getImageBlob();

            // 清理缓存
            $cover->clear();
            $avatar->clear();
            $mask->clear();
            $border->clear();
            $draw->clear();

            return response($ret)
                ->header('Content-Type', 'image/jpeg');
        }
        abort(404);
    }
}
