<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;


use App\Models\Product;
// use App\Models\Order;
// use App\Models\OrderItem;
// use App\Models\OrderPayment;
use App\Models\ProductSkuItem;
use App\Models\ProductOrder;
use App\Models\ProductOrderItem;
use App\Models\ProductOrderPayment;
use App\Models\UserWallet;
use App\Models\WalletTransaction;
use App\Notifications\CommsInfo;



class CartController extends Controller
{
    public function cart(){
      $user = Auth::user();
      if($user){
        $total = $this->total();
        $points = UserWallet::where('user_id',$user->id)->where('currency_id',1)->first();
        if(!$points){
          $points = UserWallet::create(
            [
              'user_id'=>$user->id,
              'currency_id'=>1,
              'currency_name'=>'POINTS',
              'balance'=>0
            ]
          );
        }
        return view('frontend/cart',compact('total','points'));
      }
      else{
        $error_data = array(
                            'title'=>'Please Sign In First',
                            'message'=>'For a better experience, please sign in to continue your shopping.',
                            'btn_txt'=>'To Signin Page',
                            'btn_link'=>'signin',
                            'parameter'=>'origin=cart'
                          );
        return view('frontend/error',compact('error_data'));
      }
    }

    public function addToCart(Request $request)
    {
      $id = $request->sku_id;
      $product = Product::find($request->id);

      $sku =  ProductSkuItem::find($request->sku_id);

      if(!$product||!$sku) {
        abort(404);
      }

      $cart = session()->get('cart');

      // if cart is empty then this the first product
      if(!$cart) {
        $cart = [
          $id => [
              "name" => $product->name,
              "product_id"=>$product->id,
              "quantity" => 1,
              "price" => $sku->price,
              "total"=> $sku->price,
              "sku_attribute"=>json_decode($sku->sku_attribute),
              "image" => json_decode($product->images)[0],
              
          ]
        ];
        session()->put('cart', $cart);
        $return_json = array('status'=>'success','message'=>'Add Success','code'=>200,'cart'=>$cart);
        return $return_json;
          // return redirect()->back()->with('success', 'Product added to cart successfully!');
      }

      // if cart not empty then check if this product exist then increment quantity
      if(isset($cart[$id])) {
        if($sku->max_per_user && $cart[$id]['quantity'] >= $sku->max_per_user){
          $return_json = array('status'=>'success','message'=>'Maxinum '.$sku->max_per_user.' per user.','code'=>400);
          return $return_json;
        }
        $cart[$id]['quantity']++;
        $cart[$id]['total'] = $cart[$id]['quantity'] * $cart[$id]['price'];
        session()->put('cart', $cart);
        $return_json = array('status'=>'success','message'=>'Add Success','code'=>200,'cart'=>$cart);
        return $return_json;
          // return redirect()->back()->with('success', 'Product added to cart successfully!');
      }
      else{
        // if item not exist in cart then add to cart with quantity = 1
        $cart[$id] = [
          "name" => $product->name,
          "product_id"=>$product->id,
          "quantity" => 1,
          "price" => $sku->price,
          'total'=> $sku->price,
          "sku_attribute"=>json_decode($sku->sku_attribute),
          "image" => json_decode($product->images)[0]
        ];

        session()->put('cart', $cart);
        $return_json = array('status'=>'success','message'=>'Add Success','code'=>200,'cart'=>$cart);
        return $return_json;
        // return redirect()->back()->with('success', 'Product added to cart successfully!');
      }
    }

    public function remove(Request $request){
      $remove_id = $request->id;
      $cart = session()->get('cart');
      unset($cart[$remove_id]);
      session()->put('cart', $cart);
      $total = $this->total();
      $return_json = array('status'=>'success','message'=>'Remove Success','code'=>200,'total'=>$total);
      return $return_json;
      // $cart[$id]['quantity']++;
    }

    public function update(Request $request){
      if ($content->has($id)) {
        $cartItem = $content->get($id);
        switch ($action) {
          case 'plus':
            $cartItem->put('quantity', $content->get($id)->get('quantity') + 1);
            break;
          case 'minus':
            $updatedQuantity = $content->get($id)->get('quantity') - 1;
            if ($updatedQuantity < self::MINIMUM_QUANTITY) {
                $updatedQuantity = self::MINIMUM_QUANTITY;
            }
            $cartItem->put('quantity', $updatedQuantity);
            break;
        }
        $content->put($id, $cartItem);

        $this->session->put(self::DEFAULT_INSTANCE, $content);
      }
    }

    public static function emptyCart()
    {
      session()->forget('cart');
    }

    public static function total(){
      $cart = session()->get('cart');
      $total = 0;
      if($cart){
        foreach($cart as $key=>$item){
          $total+= $item['price'] * $item['quantity'];
        }
      }
      return number_format($total, 2);
    }


    public static function newOrderNumber(){
      // $uniqueId= time();
    }

    public function checkout(Request $request){
      if(Auth::check()){
        $cart = session()->get('cart');
        $user = Auth::user();
        if($cart){
          $total = $this->total();
          $new_order_data = array('user_id'=>$user->id,'status'=>1,'price'=>$total);
          $new_order = ProductOrder::create($new_order_data);
          foreach($cart as $sku_id=>$item){
            $new_order_item_data = array('user_id'=>$user->id,'status'=>1,'product_id'=>$item['product_id'],'order_id'=>$new_order->id,
                                        'sku_id'=>$sku_id,'sku_attribute'=>json_encode($item['sku_attribute']),'product_name'=>$item['name'],
                                        'unit_price'=>$item['price'],'quantity'=>$item['quantity'],'total_price'=>$item['total']);
            ProductOrderItem::create($new_order_item_data);
          }
          

          if($request->used_points){
            $used_points = $request->used_points;
            $user_points = UserWallet::where('user_id',$user->id)->where('currency_id',1)->first();
            if($user_points && $user_points>=$used_points){
              $point_discount = $used_points/100;
              $total_after_point = $total - $point_discount;

              $new_order->update([
                'price'=>$total_after_point,
                'point_discount'=>$point_discount,
                'point_used'=>$used_points
              ]);
              // $new_order->save();
              $new_order_payment_data =  array('user_id'=>$user->id,'status'=>1,'order_id'=>$new_order->id,'pay_amount'=>$total_after_point);
              $new_payment = ProductOrderPayment::create($new_order_payment_data);
              return redirect('/product_payment'.'/'.$new_payment->id);
            }  
          }
          else{
            // return 'no use point';
            // // $total = $this->total();
            $new_order_payment_data =  array('user_id'=>$user->id,'status'=>1,'order_id'=>$new_order->id,'pay_amount'=>$total);
            $new_payment = ProductOrderPayment::create($new_order_payment_data);
            return redirect('/product_payment'.'/'.$new_payment->id);
          }
          
          // return view('frontend/shop_payment');
        }
        else{
          return 'cart empty';
        }
      }
      else{
        $error_data = array(
                            'title'=>'Please Sign In First',
                            'message'=>'For a better experience, please sign in to continue your order.',
                            'btn_txt'=>'To Signin Page','btn_link'=>'login',
                            'btn_link'=>'signin',
                            'parameter'=>'origin=cart'
                          );
        return view('frontend/error',compact('error_data'));
      }
    }

    public function paymentPage($payment_id){
      $payment = ProductOrderPayment::where('id',$payment_id)->first();
      $user = Auth::user();
      if($payment && $payment->user_id == $user->id){
        $order = ProductOrder::where('id',$payment->order_id)->first();
        $order_items = ProductOrderItem::where('order_id',$payment->order_id)->get();
        return view('frontend/product_payment',compact('payment','order','order_items','user'));
      }
    }

    public function placeNoPay(Request $request){
      $user = Auth::user();
      $order =  ProductOrder::where('id',$request['order_id'])->first();
      $payment = ProductOrderPayment::where('id',$request['payment_id'])->first();
      if($user && $user->id == $order->user_id && $order->price  == 0){
        $user_point_wallet = UserWallet::where('user_id',$order->user_id)->where('currency_id',1)->first();
        $new_wallet_trasaction = WalletTransaction::create([
          'user_id'=>$order->user_id,
          'currency_id'=>1,
          'currency_name'=>'POINTS',
          'action'=>'minus',
          'amount'=>$order->point_used,
          'note'=>'Order # '. $order->id . ' used '.$order->point_used. 'points.',
        ]);
        $user_point_wallet->balance = $user_point_wallet->balance - $order->point_used;
        $user_point_wallet->save();

        $update_payment_data = array(
          'pay_type'=>'points',
          'status'=>3,
          'pay_status'=>1,
          'pay_note'=>'Used '.$order->point_used.' points',
          'paid_at'=> date('Y-m-d H:i:s', time())
        );
        $payment->update($update_payment_data);
        
        $order->pay_status = 1;
        $order->status = 2;
        $order->save();
        
        $this->emptyCart();

        $return_json = array('status'=>'success','message'=>'Paid Success','code'=>200);
        return $return_json;
      }

    }

    public function paypalOnApprove(Request $request){
      $user = Auth::user();
      $order =  ProductOrder::where('id',$request['order_id'])->first();
      $payment = ProductOrderPayment::where('id',$request['payment_id'])->first();
      $update_payment_data = array(
        // 'order_id'=>$order->id,
        // 'user_id'=>$order->user_id,
        'pay_type'=>'paypal',
        'status'=>3,
        'pay_status'=>1,
        // 'pay_amount'=>$request['pay_amount'],
        'third_party_code'=>$request['paypal_order_id'],
        'pay_note'=>json_encode($request['full_data']),
        'paid_at'=> date('Y-m-d H:i:s', time())
      );
      $payment->update($update_payment_data);
      $payment->save();
      // OrderPayment::create($new_payment_data);
      if($order->point_used){
        // $order_user = User::where('id',$order->user_id)->first();
        $user_point_wallet = UserWallet::where('user_id',$order->user_id)->where('currency_id',1)->first();
        $new_wallet_trasaction = WalletTransaction::create([
          'user_id'=>$order->user_id,
          'currency_id'=>1,
          'currency_name'=>'POINTS',
          'action'=>'minus',
          'amount'=>$order->point_used,
          'note'=>'Order # '. $order->id . ' used '.$order->point_used. 'points.',
        ]);
        $user_point_wallet->balance = $user_point_wallet->balance - $order->point_used;
        $user_point_wallet->save();
      }

      $order->pay_status = 1;
      $order->status = 2;
      // $order->paid = $order->paid + $request['pay_amount'];
      $order->save();
      
      $this->emptyCart();
      $return_json = array('status'=>'success','message'=>'Paid Success','code'=>200);
      return $return_json;
    }


    public function test(){
      $user = User::where('id',1)->first();
      $user->notify(new CommsInfo(['good']));
    }
}
