<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Http\File;
use GuzzleHttp\Client;
use Image;

use App\Models\User;
use App\Models\Artist;
use App\Models\Artwork;
use App\Models\Service;
use App\Models\ServiceCategory;
use App\Models\ServiceContent;
use App\Models\ServiceShowcase;
use App\Models\Tag;
use App\Models\ServiceContentTranslateJob;
use App\Jobs\TranslateServiceContent;
use App\Models\ServiceStage;
use App\Models\ServiceStageSelection;
use App\Models\ServiceTask;


class ArtistCenterController extends Controller
{
	public function profile(){
		$user = Auth::user();
		$artist = Artist::where('user_id',$user->id)->first();
		if($user){
			return view('artist_center/artist_profile',compact('user','artist'));
		}
	}



	public function artworks(){
		$user = Auth::user();
		$artist = Artist::where('user_id',$user->id)->first();
		if($artist){
			$artist_works = Artwork::where('artist_id',$artist->id)->get();
			foreach($artist_works as $work){
				$work->tag_ids = $work->tags()->pluck('tags.id')->toArray();
			}
			$tags = Tag::get();
			return view('artist_center/artist_works',compact('user','artist','artist_works','tags'));
		}
	}

	public function editWork(Request $request){
		$user = Auth::user();
		$artist = Artist::where('user_id',$user->id)->first();
		if($artist && $user){
			$work = Artwork::where('id',$request->id)->where('artist_id',$artist->id)->first();
			$work->update($request->only(['title','detail']));
			$work->save();

			$new_tags = $request->tags;
			$work->tags()->sync($new_tags);
			// foreach($new_tags as $new_tag){

			// }

			$work->tag_ids = $work->tags()->pluck('tags.id')->toArray();
			

			return response()->json([
				'status' => 'success',
				'message' => 'Upload Success',
				'code'=>200,
				'data'=>$work,
			]);

		}

	}


	public function sortWorks(Request $request){
		$user = Auth::user();
		if($user){
			$sorted_images_ids = $request['sorted_images_ids'];
			foreach($sorted_images_ids as $key=>$image_id){
			$image = Artwork::where('id',$image_id)->first();
			$image->sort = $key;
			$image->save();
			}
			$return_json =  array('status'=>'success','message'=>'Sort Success','code'=>200);
			return $return_json;
		}
	}


	public function updateProfile(Request $request){
		$user = Auth::user();
		$artist = Artist::where('id',$request->artist_id)->first();
		if($artist && $user){
			$artist->update($request->all());
			$artist->save();
			$return_json =  array('status'=>'success','message'=>'Save Success','code'=>200);
			return $return_json;
		}
		else{
			$return_json =  array('status'=>'error','message'=>'Permission denied','code'=>400);
			return $return_json;
		}

	}

	public function updateBanner(){

	}

	public function updateAvatar(){

	}

	public function uploadWork(Request $request){
		$user = Auth::user();
		$artist = Artist::where('user_id',$user->id)->first();
		if($artist && $user && $artist->user_id == $user->id){
			$image_file  = $request->file('file');
			$file_type = "r2";
			if($file_type == "r2"){
				// Storage::disk('r2')->put('/', $image_file);
				$cdn_url = env("CLOUDFLARE_R2_URL");
				$image_folder = 'artist_works/'.$artist->id.'/';
				$imageName =uniqid();
				// $db_path = 'artist_works/'.$artist->id.'/';
				$imageExtension = "jpg";
				$img_original_name = $imageName."_og.".$imageExtension;

				$img_original = Image::read($image_file->getRealPath());

				if($img_original->width() > 1500){

				// $img_lg = $img_original->resize(1500, null, function ($constraint) {
				// 	$constraint->aspectRatio();
				// })->stream($imageExtension, 80);

				$img_lg = $img_original->resize(1500, null, function ($constraint) {
					$constraint->aspectRatio();
				})->toJpeg()->toFilePointer();;
				
				// $img_lg = $img_lg;
				$img_lg_name = $imageName."_lg.".$imageExtension;
				Storage::disk('r2')->put($image_folder.$img_lg_name,$img_lg);
					$img_lg_db = $cdn_url.$image_folder.$img_lg_name;
				}
				else{
				$img_og = $img_original->toJpeg()->toFilePointer();;
				$img_original_name = $imageName."_og.".$imageExtension;
				if(!Storage::exists($image_folder.$img_original_name)){
					
					Storage::disk('r2')->put($image_folder.$img_original_name,$img_og);
				}	
				$img_lg_db = $cdn_url.$image_folder.$img_original_name;
				}

				// if($img_original->width() > 750){
				//   $img_md = $img_original->resize(750, null, function ($constraint) {
				//     $constraint->aspectRatio();
				//   });
				//   $img_md_name = $imageName."_md.".$imageExtension;
				//   $img_md->save(Storage::path($image_folder).$img_md_name);
				//   $img_md_db = $db_path.$img_md_name;
				// }
				// else{
				//   $img_md_db = $db_path.$img_original_name;
				//   if(!Storage::exists($image_folder.$img_original_name)){
				//     Storage::disk('r2')->putFileAs($image_folder,$image_file,$img_original_name);
				//   }	
				// }

				// $img_sm = $img_original->resize(375, null, function ($constraint) {
				//   $constraint->aspectRatio();
				// });
				// $img_sm_name = $imageName."_sm.".$imageExtension;
				// $img_sm->save(Storage::path($image_folder).$img_sm_name);
				// $img_sm_db = $db_path.$img_sm_name;

				$new_img_data = [
				"artist_id"=>$artist->id,
				"type"=>1,//for upload images
				
				"url_sm"=>$img_lg_db,
				"url_md"=>$img_lg_db,
				"url_lg"=>$img_lg_db,
				];
				
				$new_img_work = Artwork::create($new_img_data);
				$new_img_work->tags = $new_img_work->tags()->get();
			}

			if($file_type == "local"){
				$image_folder = 'public/artist_works/'.$user->id.'/';
				$db_path = 'artist_works/'.$user->id.'/';
				$imageName =uniqid();
				$imageExtension = $image_file->getClientOriginalExtension();
				$img_original_name = $imageName."_og.".$imageExtension;
				

				if($imageExtension == "gif"){ //image libray does not support gif for now, new to wait for 3.0 update
				
				}

				else{
				// $image_file->getRealPath() = Storage::path($image_folder)."/".$img_original_name;
				$img_original = Image::read($image_file->getRealPath());
				if($img_original->width() > 1500){
					$img_lg = $img_original->resize(1500, null, function ($constraint) {
					$constraint->aspectRatio();
					});
					$img_lg_name = $imageName."_lg.".$imageExtension;
					$img_lg->save(Storage::path($image_folder).$img_lg_name);
					$img_lg_db = $db_path.$img_lg_name;
				}
				else{
					$img_lg_db = $db_path.$img_original_name;
					if(!Storage::exists($image_folder.$img_original_name)){
					$image_file->move(Storage::path($image_folder),$img_original_name);
					}	
				}

				if($img_original->width() > 750){
					$img_md = $img_original->resize(750, null, function ($constraint) {
					$constraint->aspectRatio();
					});
					$img_md_name = $imageName."_md.".$imageExtension;
					$img_md->save(Storage::path($image_folder).$img_md_name);
					$img_md_db = $db_path.$img_md_name;
				}
				else{
					$img_md_db = $db_path.$img_original_name;
					if(!Storage::exists($image_folder.$img_original_name)){
					$image_file->move(Storage::path($image_folder),$img_original_name);
					}	
				}



				$img_sm = $img_original->resize(375, null, function ($constraint) {
					$constraint->aspectRatio();
				});
				$img_sm_name = $imageName."_sm.".$imageExtension;
				$img_sm->save(Storage::path($image_folder).$img_sm_name);
				$img_sm_db = $db_path.$img_sm_name;

				$new_img_data = [
					"artist_id"=>$artist->id,
					"type"=>1,//for upload images
					"url_sm"=>$img_sm_db,
					"url_md"=>$img_md_db,
					"url_lg"=>$img_lg_db,
				];
				
				$new_img_work = Artwork::create($new_img_data);
				
				$new_img_work->tags = $new_img_work->tags()->get();
				
				}
			}
			
			// $image->move(Storage::path($image_folder),$imageName);

			
			return response()->json([
				'status' => 'success',
				'message' => 'Upload Success',
				'code'=>200,
				'data'=>$new_img_work,
			]);
		}
		

	}

	public function deletework(Request $request){
		$user = Auth::user();
		$artist = Artist::where('id',$request->artist_id)->first();
		if($artist && $user && $artist->user_id == $user->id){
			$artist_work = Artwork::where('id',$request->work_id)->first();
			if($artist_work && $artist_work->artist_id == $artist->id){
				if($artist_work->type == 1){ //local storage image
					try {
						Storage::disk('r2')->delete($artist_work->url_sm);
						Storage::disk('r2')->delete($artist_work->url_md);
						Storage::disk('r2')->delete($artist_work->url_lg);
					} catch (\Throwable $th) {
						//throw $th;
					}
					
				}
				$artist_work->delete();
				return response()->json([
					'status' => 'success',
					'message' => 'Delete Success',
					'code'=>200
				]);
			}
		}
		else{
			$return_json =  array('status'=>'error','message'=>'Permission denied','code'=>400);
			return $return_json;
		}
	}


	public function services(){
		$user = Auth::user();
		$artist = Artist::where('user_id',$user->id)->first();
		if($artist){
			$services = Service::where('artist_id',$artist->id)->get();
			return view('artist_center/artist_services',compact('services','artist'));
		}
	}

	public function serviceDetail($service_id){
		$user = Auth::user();
		$artist = Artist::where('user_id',$user->id)->first();
		$service = Service::where('id',$service_id)->first();
		if($artist && $service && $service->artist_id == $artist->id){
			$categories = Category::get();

			$tags_categorized = Tag::join('tag_category_pivot','tag_category_pivot.tag_id','=','tags.id')
						->join('tag_categories',"tag_category_pivot.tag_category_id",'=',"tag_categories.id")
						->where('tag_categories.id', 1)->orwhere('tag_categories.id', 3)
						->select('tags.*', 'tag_categories.name_zh as category_name_zh')
						->get()
						->groupBy('category_name_zh');

			$tags = Tag::get();

			$categories = $service->categories()->pluck("service_categories.id");

			$general_tag_ids = Tag::join("service_tag_pivot",'service_tag_pivot.tag_id','=','tags.id')
									->where("service_id",$service_id)
									->where("service_tag_pivot.type",1)
									->pluck("tags.id");
			$prefer_tag_ids = Tag::join("service_tag_pivot",'service_tag_pivot.tag_id','=','tags.id')
									->where("service_id",$service_id)
									->where("service_tag_pivot.type",2)
									->pluck("tags.id");
			$cantdo_tag_ids = Tag::join("service_tag_pivot",'service_tag_pivot.tag_id','=','tags.id')
									->where("service_id",$service_id)
									->where("service_tag_pivot.type",3)
									->pluck("tags.id");
			$selected_data = [
				"categories"=>$categories,
				"general_tag_ids"=>$general_tag_ids,
				"prefer_tag_ids"=>$prefer_tag_ids,
				"cantdo_tag_ids"=>$cantdo_tag_ids
			];

			$service_stages = ServiceStage::where('service_id',$service_id)->orderBy('sort','ASC')->get();
			$service_stage_selections = ServiceStageSelection::get();
			// dd($general_tag_ids);
			
			$service_showcases = ServiceShowcase::where('service_id',$service_id)->orderBy('sort')->get();
			$addable_works = Artwork::doesntHave('showcases')->where('artist_id',$artist->id)->get();
			// $showcase_from_work = ServiceShowcase::where('service_id',$service_id)->where('type',1)->pluck('id');

			// $artist_work = Artwork::where('artist_id',$artist_id)->get();
			return view('artist_center/artist_service_detail',compact('service_categories','tags_categorized','artist','service','service_showcases','addable_works','selected_data','service_stages','service_stage_selections'));
		}
	}

	public function addServiceShowcase(Request $request){
		$user = Auth::user();
		$artist = Artist::where('user_id',$user->id)->first();
		$service = Service::where('id',$request->service_id)->first();
		if($artist && $service && $service->artist_id == $artist->id){
			if($request->type == "work"){
				$selected_works = $request->selected_works;
				foreach($selected_works as $work_id){
					
					$artist_work = Artwork::where('id',$work_id)->where('artist_id',$artist->id)->first();
					$new_showcase_data = [
						'artist_id'=>$artist->id,
						'service_id'=>$request->service_id,
						'artist_work_id'=>$artist_work->id,
						'type'=>1,
						'url_sm'=>$artist_work->url_sm,
						'url_md'=>$artist_work->url_md,
						'url_lg'=>$artist_work->url_lg,
					];
					ServiceShowcase::create($new_showcase_data);
				}
				return response()->json([
					'status' => 'success',
					'message' => 'Add Success',
					'code'=>200
				]);
			}
		}
	}

	public function deleteServiceShowcase(Request $request){
		$user = Auth::user();
		$artist = Artist::where('user_id',$user->id)->first();
		$delete_service_showcase = ServiceShowcase::where('id',$request->delete_id)->where('artist_id',$artist->id)->first();


		if($delete_service_showcase){

			if($delete_service_showcase->type == 1){//showcase from worklio
				$delete_service_showcase->delete();
			}

			return response()->json([
				'status' => 'success',
				'message' => 'Delete Success',
				'code'=>200
			]);
		}
	}

	public function sortServiceShowcases(Request $request){
		$user = Auth::user();
		$artist = Artist::where('user_id',$user->id)->first();
		$service = Service::where('id',$request->service_id)->first();
		if($user){
			$sorted_showcase_ids = $request->sorted_showcase_ids;
			foreach($sorted_showcase_ids as $key=>$showcase_id){
				$image = ServiceShowcase::where('id',$showcase_id)->first();
				$image->sort = $key;
				$image->save();
			}

			return response()->json([
				'status' => 'success',
				'message' => 'Sort Success',
				'code'=>200
			]);
			
		}
	}




	public function saveServiceContent(Request $request){
		$user = Auth::user();
		$artist = Artist::where('user_id',$user->id)->first();
		$service = Service::where('id',$request->service_id)->first();
		if($artist && $service && $service->artist_id == $artist->id){
			// $service->update($request->only(['title','price_from','price_to','detail']));
			// $service->save();
			$service_content_source = ServiceContent::where('service_id',$request->service_id)->where('is_source',1)->first();
			if($service_content_source){
				$service_content_source->update($request->only(['title','detail']));
				$service_content_source->save();

				// $job_exist = ServiceContentTranslateJob::where('service_id',$service_content_source->id)->where('status',1)
				// 									->where('source_lang_id',2)->where('target_lang_id',1)//中文转英文
				// 									->first();
				// if(!$job_exist){
				// 	$new_translate_job = ServiceContentTranslateJob::create([
				// 		'service_id'=>$service_content_source->id,
				// 		'artist_id'=>$artist->id,
				// 		'status'=>1,
				// 		'source_lang_id'=>2,
				// 		'source_lang_code'=>'zh',
				// 		'source_lang_name'=>'中文',
				// 		'target_lang_id'=>1,
				// 		'target_lang_code'=>'en',
				// 		'target_lang_name'=>'English'
				// 	]);
				// }
				// TranslateServiceContent::dispatch();
			}
			else{
				$original_content_data = [
					'service_id'=>$request->service_id,
					'artist_id'=>$artist->id,
					'title'=>$request->title,
					'detail'=>$request->detail,
					'is_source'=>1,
					'lang_id'=>1,
					'lang_code'=>'zh',
					'lang_name'=>'中文'
				];
				$service_content_source = ServiceContent::create($original_content_data);

				// $translate_content = $request->only(['title','detail']);
				// $yourApiKey = getenv('YOUR_API_KEY');
				// $client = OpenAI::client($yourApiKey);
			}

			return response()->json([
				'status' => 'success',
				'message' => 'Save Success',
				'code'=>200
			]);
		}
		else{
			return 'something wrong';
		}
	}

	public function saveServiceSetting(Request $request){
		$user = Auth::user();
		$artist = Artist::where('user_id',$user->id)->first();
		$service = Service::where('id',$request->service_id)->first();
		if($artist && $service && $service->artist_id == $artist->id){

			$service->update($request->only(['price','status','days_need','stock','usage']));
			$service->save();

			$service->categories()->sync($request->categories);

			$service->tags()->detach();
			$service->tags()->attach($request->general_tags,['type'=>1,'type_name'=>'general']);
			$service->tags()->attach($request->prefer_tags,['type'=>2,'type_name'=>'prefer']);
			$service->tags()->attach($request->cantdo_tags,['type'=>3,'type_name'=>'cantdo']);



			
			return response()->json([
				'status' => 'success',
				'message' => 'Save Success',
				'data'=>$request->all(),
				'code'=>200
			]);
		}
	}

	public function serviceTasks(){
		$user = Auth::user();
		$artist = Artist::where('user_id',$user->id)->first();
		if($artist){
			$service_tasks = ServiceTask::where('artist_id',$artist->id)->get();
			// $request_files = ServiceTaskFile::where('service_id',1)->orderBy('sort')->get();
			return view('artist_center/artist_service_tasks',compact('service_tasks','artist'));
		}
	}

	public function serviceTaskDetail($service_task_id){
		$user = Auth::user();
		$artist = Artist::where('user_id',$user->id)->first();
		if($artist){
			$service_task = ServiceTask::where("artist_id",$artist->id)->where("id",$service_task_id)->first();
			if($service_task){
				return view('artist_center/artist_service_task_detail',compact("service_task"));
			}
		}
	}

	public function serviceTaskChat($service_task_id){
		$user = Auth::user();
		$artist = Artist::where('user_id',$user->id)->first();
		if($artist){
			$service_task = ServiceTask::where("artist_id",$artist->id)->where("id",$service_task_id)->first();
			if($service_task){
				return view('artist_center/artist_service_task_chat',compact("service_task"));
			}
		}
	}
  
  
}
