<?php

namespace App\Http\Controllers;

use Illuminate\Http\File;
use Illuminate\Support\Facades\Storage;

class TestController extends Controller
{
    public function file()
    {
        $image_file = Storage::get('test3.txt');

        return Storage::disk('r2-private')->download('test.txt');
        // Storage::disk('r2-private')->put('test.txt',$image_file);
    }

    public function uploadStoreageFile()
    {
        $localPath = '/storage/background-6.mp4';
        $file = File::get(public_path($localPath));

        $remotePath = $localPath;
        Storage::disk('r2')->put($remotePath, $file);
    }
}
