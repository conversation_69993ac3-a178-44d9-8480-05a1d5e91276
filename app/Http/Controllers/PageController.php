<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;


use App\Models\User;
use App\Models\Commission;
use App\Models\Artist;
use App\Models\ContactForm;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\ProductAttribute;
use App\Models\ProductSkuItem;
use App\Controllers\CartController;

class PageController extends Controller
{
    public function Signup(){
      if (Auth::check()) {
        return redirect('/dashboard/commissions');
      }
      else{
        return view('frontend/signup');
      }
    }

    public function Signin(){
      if (Auth::check()) {
        return redirect('/dashboard/commissions');
      }
      else{
        return view('frontend/signin');
      }
    }

    public function Logout(){
      Auth::logout();
      return redirect('/');
    }


    public function dashboard(){
      if (Auth::check()) {
         return view('dashboard/index');
      }
      else{
        return redirect('/signin');
      }
    }

    public function FormDesign(){
      if (Auth::check()) {
        $user = Auth::user();
        $pending_count = Commission::where('status','<',100)->where('user_id',$user->id)->count();
        // $pending_count = 0;
        // dd($pending_count);


        return view('frontend/form_design',compact('pending_count'));
      }
      else{
        $error_data = array('title'=>'Please Sign In First',
                            'message'=>'For a better experience, please sign in to continue your request.',
                            'btn_txt'=>'To Signin Page','btn_link'=>'login',
                            'btn_link'=>'signin',
                            'parameter'=>'origin=form/design'
                          );
        return view('frontend/error',compact('error_data'));
      }
    }

    public function FormModel(){
      if (Auth::check()) {
        $user = Auth::user();
        $pending_count = Commission::where('status','<',100)->where('user_id',$user->id)->count();
        // $pending_count = 0;
        // dd($pending_count);


        return view('frontend/form_model',compact('pending_count'));
      }
      else{
        $error_data = array('title'=>'Please Sign In First',
                            'message'=>'For a better experience, please sign in to continue your request.',
                            'btn_txt'=>'To Signin Page','btn_link'=>'login',
                            'btn_link'=>'signin',
                            'parameter'=>'origin=form/model'
                          );
        return view('frontend/error',compact('error_data'));
      }
    }

    public function FormRig(){
      if (Auth::check()) {
        $user = Auth::user();
        $pending_count = Commission::where('status','<',100)->where('user_id',$user->id)->count();
        // $pending_count = 0;
        // dd($pending_count);


        return view('frontend/form_rig',compact('pending_count'));
      }
      else{
        $error_data = array('title'=>'Please Sign In First',
                            'message'=>'For a better experience, please sign in to continue your request.',
                            'btn_txt'=>'To Signin Page','btn_link'=>'login',
                            'btn_link'=>'signin',
                            'parameter'=>'origin=form/rig'
                          );
        return view('frontend/error',compact('error_data'));
      }
    }
    
    public function FormIllu(){
      if (Auth::check()) {
        $user = Auth::user();
        $pending_count = Commission::where('status','<',100)->where('user_id',$user->id)->count();
        // $pending_count = 0;
        // dd($pending_count);


        return view('frontend/form_illu',compact('pending_count'));
      }
      else{
        $error_data = array('title'=>'Please Sign In First',
                            'message'=>'For a better experience, please sign in to continue your request.',
                            'btn_txt'=>'To Signin Page','btn_link'=>'login',
                            'btn_link'=>'signin',
                            'parameter'=>'origin=form/illu'
                          );
        return view('frontend/error',compact('error_data'));
      }
    }

    public function FormEmote(){
      if (Auth::check()) {
        $user = Auth::user();
        $pending_count = Commission::where('status','<',100)->where('user_id',$user->id)->count();
        // $pending_count = 0;
        // dd($pending_count);


        return view('frontend/form_emote',compact('pending_count'));
      }
      else{
        $error_data = array('title'=>'Please Sign In First',
                            'message'=>'For a better experience, please sign in to continue your request.',
                            'btn_txt'=>'To Signin Page','btn_link'=>'login',
                            'btn_link'=>'signin',
                            'parameter'=>'origin=form/emote'
                          );
        return view('frontend/error',compact('error_data'));
      }
    }

    public function FormOther(){
      if (Auth::check()) {
        $user = Auth::user();
        $pending_count = Commission::where('status','<',100)->where('user_id',$user->id)->count();
        // $pending_count = 0;
        // dd($pending_count);


        return view('frontend/form_other',compact('pending_count'));
      }
      else{
        $error_data = array('title'=>'Please Sign In First',
                            'message'=>'For a better experience, please sign in to continue your request.',
                            'btn_txt'=>'To Signin Page','btn_link'=>'login',
                            'btn_link'=>'signin',
                            'parameter'=>'origin=form/other'
                          );
        return view('frontend/error',compact('error_data'));
      }
    }


    public function sample(){
      $lv1_artists = Artist::where('uid','lv1')->get();
      $lv2_artists = Artist::where('uid','lv2')->get();
      $lv3_artists = Artist::where('uid','lv3')->get();
      $lv5_artists = Artist::where('uid','lv5')->get();
      return view('frontend/sample',compact('lv1_artists','lv2_artists','lv3_artists','lv5_artists'));
    }

    public function sampleDetail1(){
      $lv1_artists = Artist::where('uid','lv1')->get();
      return view('frontend/sample_detail_1',compact('lv1_artists'));
    }

    public function sampleDetail2(){
      $lv2_artists = Artist::where('uid','lv2')->get();
      return view('frontend/sample_detail_2',compact('lv2_artists'));
    }

    public function sampleDetail3(){
      $lv3_artists = Artist::where('uid','lv3')->get();
      return view('frontend/sample_detail_3',compact('lv3_artists'));

    }

    public function sampleDetail5(){
      $lv5_artists = Artist::where('uid','lv5')->get();
      return view('frontend/sample_detail_5',compact('lv5_artists'));

    }

    public function contactForm(Request $request){
      $data = $request->only('name','email','content');
      ContactForm::create($data);
      Session::flash('form_message', 'Form Submit Success');
      // $mesage = Session::get('form_message');
      return redirect('/contact');
    }

    public function shop(Request $request){
      $selected_categories = null;
    
      if($request->categories){
        $selected_categories = explode(",",$request->categories);
      }
      $price_from = $request->price_from;
      $price_to = $request->price_to;
      
      
      

      // dd($selected_categories);
      // $selected_categories = $params;
      
      $products = Product::Join('product_sku_items','product_sku_items.product_id','products.id')
                         ->leftJoin('product_category_pivot','product_category_pivot.product_id', '=', 'products.id')
                         ->where(function ($query) use ($selected_categories,$price_from,$price_to) {
                              if($selected_categories){
                                foreach($selected_categories as $cate) {
                                  $query->orWhere('product_category_pivot.product_category_id', $cate);
                                }
                              } 
                              if($price_from){
                                $query->where('product_sku_items.price','>=', $price_from);
                              }
                              if($price_to){
                                $query->where('product_sku_items.price','<=', $price_to);
                              }    
                          })
                        ->where('publish_status',1)
                        ->select('products.*')
                        ->distinct()
                        ->paginate(9)->withQueryString();
     
      $categories = ProductCategory::get();
      return view('frontend/product_shop',compact('products','categories'));
    }

    public function product($product_id){
      $product = Product::where('id',$product_id)->first();
      $attributes = ProductAttribute::where('product_id',$product_id)->get();
      $skus = ProductSkuItem::where('product_id',$product_id)->get();
      $product->images = json_decode($product->images);
      return view('frontend/product_detail',compact('product','product_id','attributes','skus'));
    }

   

    public function Error(){
      return view('frontend/error');
    }

}
