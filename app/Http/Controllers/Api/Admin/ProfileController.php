<?php

namespace App\Http\Controllers\Api\Admin;

use App\Enums\TranslateStatus;
use App\Enums\TranslateType;
use App\Http\Controllers\Controller;
use App\Models\Artist;
use App\Models\Group;
use App\Models\Project;
use App\Models\Service;
use App\Models\Translate;
use Illuminate\Http\Request;

class ProfileController extends Controller
{
    public function jobCount(Request $request)
    {
        return [
            'data' => [
                'translate' => $this->translateCount(),
                'join_group' => $this->joinGroupCount(),
            ],
        ];
    }

    private function translateCount()
    {
        $query = Translate::query()
            ->whereHasMorph('busable', [Service::class, Project::class, Artist::class])
            ->select('busable_type', \DB::raw('COUNT(DISTINCT busable_id) as count'))
            ->where('status', TranslateStatus::Pending)
            ->where('translate_type', TranslateType::Human)
            ->groupBy('busable_type');

        $counts = $query->get();

        return $counts;
    }

    private function joinGroupCount()
    {
        return Group::where('request_admin_join', true)->count();
    }
}
