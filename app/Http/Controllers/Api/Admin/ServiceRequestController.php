<?php

namespace App\Http\Controllers\Api\Admin;

use App\Enums\TranslateStatus;
use App\Enums\TranslateType;
use App\Http\Controllers\Controller;
use App\Models\ServiceRequest;
use App\Service\Admin\AdminService;
use App\Service\Translate\TranslateDB;
use Illuminate\Http\Request;

class ServiceRequestController extends Controller
{
    public function list(Request $request)
    {
        $request->validate([
            'page' => 'integer|min:1',
            'size' => 'integer|min:1,max:50',
            'status' => 'nullable|string|in:pending,artist_accepted,artist_rejected,user_canceled',
            'translate_type' => 'nullable|string|in:human,gpt',
            'translate_status' => 'nullable|string|in:pending,successed,cancelled',
            'has_translate' => 'nullable|boolean',
        ]);

        $query = ServiceRequest::query()
            ->orderBy('id', 'desc');

        TranslateDB::queryByTypeAndStatus($query, $request->translate_type, $request->translate_status, $request->has_translate);

        if ($request->status) {
            $query->where('status', $request->status);
        }

        $query->with(['translates' => function ($query) {
            $query->select('id', 'busable_id', 'busable_field', 'source_lang', 'target_lang')
                ->where('status', TranslateStatus::Pending)
                ->where('translate_type', TranslateType::Human);
        }]);

        $query->with([
            'user:id,name,avatar_id' => [
                'avatar',
            ],
            'serviceRequestFiles:id,service_request_id,upload_image_id' => [
                'uploadImage',
            ],
            'currency',
            'artist:id,name,avatar_id' => [
                'avatar',
            ],
            'service:id,name',
        ]);

        $services = $query->paginate($request->size);

        return [
            'data' => $services->items(),
            'total' => $services->total(),
        ];
    }

    public function update(Request $request)
    {
        $request->validate([
            'id' => 'required|integer',
            'is_finish_human_translates' => 'nullable|boolean',
        ]);

        $serviceRequest = ServiceRequest::find($request->id);

        if ($request->has('detail')) {
            AdminService::updateTranslate($serviceRequest, $request, 'detail');
        }

        //TODO 记录操作日志

        return ['ok' => true];
    }

    public function info(Request $request)
    {
        $request->validate([
            'id' => 'required|integer',
        ]);

        $ret = ServiceRequest::query()->where('id', $request->id)
            ->with([
                'user:id,name,avatar_id' => [
                    'avatar',
                ],
                'serviceRequestFiles:id,service_request_id,upload_image_id' => [
                    'uploadImage',
                ],
                'currency',
                'artist:id,name,avatar_id' => [
                    'avatar',
                ],
                'service',
                'workTask' => [
                    'workTaskStages',
                ],
            ])->first();

        return [
            'data' => $ret,
        ];
    }
}
