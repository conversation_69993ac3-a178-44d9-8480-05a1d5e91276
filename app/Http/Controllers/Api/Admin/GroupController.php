<?php

namespace App\Http\Controllers\Api\Admin;

use App\Enums\GroupUserPivotRole;
use App\Enums\SystemNotificationScene;
use App\Events\Echo\GroupUpdated;
use App\Http\Controllers\Controller;
use App\Models\Group;
use App\Notifications\SystemNotification;
use Auth;
use Illuminate\Http\Request;

class GroupController extends Controller
{
    public function join(Request $request)
    {
        $request->validate([
            'group_id' => 'integer|min:1',
            'work_task_id' => 'integer|min:1',
        ]);

        $user = Auth::user();

        $group_id = $request->group_id;
        $work_task_id = $request->work_task_id;

        $query = Group::query()
            ->orderBy('id', 'desc');

        if ($group_id) {
            $query->where('group_id', $group_id);
        }

        if ($work_task_id) {
            $query->where('work_task_id', $work_task_id);
        }

        $group = $query->first();

        if (! $group) {
            return abort(404, 'group not found');
        }

        $isInGroup = $group->users()->where('user_id', $user->id)->exists();
        if ($isInGroup) {
            $group->users()->updateExistingPivot($user->id, [
                'role' => GroupUserPivotRole::Admin,
                'order_at_ts' => now()->getTimestampMs(),
            ]);

            event(new GroupUpdated($group, $user));
        } else {
            $group->users()->attach($user->id, ['role' => GroupUserPivotRole::Admin]);

            $notify = SystemNotification::make()
                ->setScene(SystemNotificationScene::AdminJoinedGroup)
                ->setMeta([
                    'group_id' => $group->id,
                    'work_task_id' => $group->work_task_id,
                ]);

            $group->notify($notify);
        }

        // 清除请求管理员加入的标志
        Group::where('id', $group->id)->update(['request_admin_join' => false]);

        return [
            'data' => $group,
        ];
    }
}
