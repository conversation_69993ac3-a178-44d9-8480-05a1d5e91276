<?php

namespace App\Http\Controllers\Api\Admin;

use App\Enums\TranslateStatus;
use App\Enums\TranslateType;
use App\Http\Controllers\Controller;
use App\Models\Project;
use App\Service\Admin\AdminService;
use App\Service\Translate\TranslateDB;
use Illuminate\Http\Request;

class ProjectController extends Controller
{
    public function list(Request $request)
    {

        $request->validate([
            'page' => 'integer|min:1',
            'size' => 'integer|min:1,max:50',
            'translate_type' => 'nullable|string|in:human,gpt',
            'translate_status' => 'nullable|string|in:pending,successed,cancelled',
            'has_translate' => 'nullable|boolean',
        ]);

        $query = Project::query()
            ->orderBy('id', 'desc');

        TranslateDB::queryByTypeAndStatus($query, $request->translate_type, $request->translate_status, $request->has_translate);

        $query->with(['translates' => function ($query) {
            $query->select('id', 'busable_id', 'busable_field', 'source_lang', 'target_lang')
                ->where('status', TranslateStatus::Pending)
                ->where('translate_type', TranslateType::Human);
        }]);

        $projects = $query->paginate($request->size);

        return [
            'data' => $projects->items(),
            'total' => $projects->total(),
        ];
    }

    public function update(Request $request)
    {
        $request->validate([
            'id' => 'required|integer',
            'is_finish_human_translates' => 'nullable|boolean',
        ]);

        $project = Project::find($request->id);

        if ($request->has('name')) {
            AdminService::updateTranslate($project, $request, 'name');
        }

        if ($request->has('content')) {
            AdminService::updateTranslate($project, $request, 'content');
        }
        //TODO 记录操作日志

        return ['ok' => true];
    }
}
