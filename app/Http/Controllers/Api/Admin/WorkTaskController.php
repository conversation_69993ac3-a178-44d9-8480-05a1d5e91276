<?php

namespace App\Http\Controllers\Api\Admin;

use App\Enums\TranslateStatus;
use App\Enums\TranslateType;
use App\Http\Controllers\Controller;
use App\Models\WorkTask;
use Auth;
use Illuminate\Http\Request;

class WorkTaskController extends Controller
{
    public function list(Request $request)
    {
        $request->validate([
            'page' => 'integer|min:1',
            'size' => 'integer|min:1,max:50',
            'filter_joined_group' => 'nullable|boolean',
            'filter_request_admin_join' => 'nullable|boolean',
        ]);

        $query = WorkTask::query()
            ->orderBy('id', 'desc');

        $query->with([
            'translates' => function ($query) {
                $query->where('status', TranslateStatus::Pending)
                    ->where('translate_type', TranslateType::Human);
            },
            'group',
            'user:id,name,email',
            'artist:id,name,user_id' => [
                'user:id,name,email',
            ],
        ]);

        if ($request->filter_joined_group) {
            $query->whereHas('group.users', function ($query) {
                $query->where('user_id', Auth::user()->id);
            });
        }

        if ($request->filter_request_admin_join) {
            $query->whereHas('group', function ($query) {
                $query->where('request_admin_join', true);
            });
        }

        $workTasks = $query->paginate($request->size);

        return [
            'data' => $workTasks->items(),
            'total' => $workTasks->total(),
        ];
    }

    public function info(Request $request)
    {
        $request->validate([
            'id' => 'integer|min:1',
        ]);

        $ret = WorkTask::with([
            'user:id,name,avatar_id' => [
                'avatar',
            ],
            'artist:id,name,avatar_id' => [
                'avatar',
            ],
            'busable',
            'reqable',
            'workTaskStages',
            'workTaskFiles.uploadFile',
            'artistRecReview',
            'userRecReview',
            'currency',
            'payingOrder:id,status,busable_id,busable_type',
        ])->find($request->id);

        return ['data' => $ret];
    }
}
