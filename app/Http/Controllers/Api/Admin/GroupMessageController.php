<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Models\Group;
use App\Models\GroupMessage;
use Illuminate\Http\Request;

class GroupMessageController extends Controller
{
    public function list(Request $request)
    {
        $request->validate([
            'group_id' => 'integer|min:1|required_without:work_task_id',
            'work_task_id' => 'integer|min:1|required_without:group_id',
            'sender_uid' => 'integer|min:1',
            'page' => 'integer|min:1',
            'size' => 'integer|min:1,max:50',
        ]);

        $group_id = $request->group_id;
        $work_task_id = $request->work_task_id;
        $sender_uid = $request->sender_uid;

        $group = null;
        if ($group_id) {
            $group = Group::find($group_id);
        }
        if ($work_task_id) {
            $group = Group::where('work_task_id', $work_task_id)->first();
        }

        if (! $group) {
            return abort(404, 'Group not found');
        }

        $query = GroupMessage::query();
        $query->where('group_id', $group->id);

        if ($sender_uid) {
            $query->where('sender_uid', $sender_uid);
        }

        $query->with([
            'translate',
            'attachments',
        ]);

        $query->orderBy('id', 'desc');

        $ret = $query->paginate($request->size);

        $users = $group->users()
            ->select('users.id', 'users.name', 'users.avatar_id', 'users.language_id')
            ->with([
                'avatar',
                'language',
                'artist:id,avatar_id,user_id,name' => [
                    'avatar',
                ],
                'roles',
            ])->get();

        return [
            'data' => $ret->items(),
            'users' => $users,
            'total' => $ret->total(),
        ];
    }
}
