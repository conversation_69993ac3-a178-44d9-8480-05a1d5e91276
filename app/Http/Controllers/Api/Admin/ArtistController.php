<?php

namespace App\Http\Controllers\Api\Admin;

use App\Enums\TranslateStatus;
use App\Enums\TranslateType;
use App\Http\Controllers\Controller;
use App\Models\Artist;
use App\Service\Admin\AdminService;
use App\Service\Translate\TranslateDB;
use Illuminate\Http\Request;

class ArtistController extends Controller
{
    public function list(Request $request)
    {

        $request->validate([
            'page' => 'integer|min:1',
            'size' => 'integer|min:1,max:50',
            'translate_type' => 'nullable|string|in:human,gpt',
            'translate_status' => 'nullable|string|in:pending,successed,cancelled',
            'has_translate' => 'nullable|boolean',
        ]);

        $query = Artist::query()
            ->orderBy('id', 'desc');

        TranslateDB::queryByTypeAndStatus($query, $request->translate_type, $request->translate_status, $request->has_translate);

        $query->with(['translates' => function ($query) {
            $query->select('id', 'busable_id', 'busable_field', 'source_lang', 'target_lang')
                ->where('status', TranslateStatus::Pending)
                ->where('translate_type', TranslateType::Human);
        }]);

        $projects = $query->paginate($request->size);

        return [
            'data' => $projects->items(),
            'total' => $projects->total(),
        ];
    }

    public function update(Request $request)
    {
        $request->validate([
            'id' => 'required|integer',
            'is_finish_human_translates' => 'nullable|boolean',
        ]);

        $artist = Artist::find($request->id);

        if ($request->has('intro')) {
            AdminService::updateTranslate($artist, $request, 'intro');
        }
        if ($request->has('contract')) {
            AdminService::updateTranslate($artist, $request, 'contract');
        }
        //TODO 记录操作日志

        return ['ok' => true];
    }
}
