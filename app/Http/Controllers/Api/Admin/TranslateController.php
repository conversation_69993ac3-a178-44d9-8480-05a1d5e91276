<?php

namespace App\Http\Controllers\Api\Admin;

use App\Enums\TranslateStatus;
use App\Enums\TranslateType;
use App\Http\Controllers\Controller;
use App\Models\Translate;
use Illuminate\Http\Request;

class TranslateController extends Controller
{
    public function humanPendingCount(Request $request)
    {

        $query = Translate::query()
            ->select('busable_type', \DB::raw('COUNT(DISTINCT busable_id) as count'))
            ->where('status', TranslateStatus::Pending)
            ->where('translate_type', TranslateType::Human)
            ->groupBy('busable_type');

        $counts = $query->get();

        return [
            'counts' => $counts,
        ];
    }
}
