<?php

namespace App\Http\Controllers\Api;

use App\Enums\OrderStatus;
use App\Enums\UserWithdrawAccountStatus;
use App\Enums\UserWithdrawAccountType;
use App\Enums\UserWithdrawStripeServiceAgreement;
use App\Enums\WalletUsage;
use App\Http\Controllers\Controller;
use App\Models\Currency;
use App\Models\StripeCheckoutSession;
use App\Models\StripePaymentIntent;
use App\Models\StripePaymentLink;
use App\Models\StripeWebhook;
use App\Models\User;
use App\Models\UserWithdrawAccount;
use App\Models\Wallet;
use App\Models\WorkTaskStage;
use App\Service\RateService;
use App\Service\Stripe\Client\StripeClientService;
use App\Service\Stripe\StripeWebhookService;
use Auth;
use DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;
use URL;

class StripeController extends Controller
{
    public function checkout(Request $request)
    {

        $stripePriceId = 'price_1PJX3IFSolDvP35EwiTW2aWd';

        $quantity = 1;

        return $request->user()->checkout([$stripePriceId => $quantity], [
            'success_url' => route('checkout-success'),
            'cancel_url' => route('checkout-cancel'),
        ]);
    }

    public function checkoutSuccess(Request $request)
    {
        Log::info('checkoutSuccess', $request->all());

        return $request->all();
    }

    public function checkoutCancel(Request $request)
    {
        Log::info('checkoutCancel', $request->all());

        return $request->all();
    }

    public function createConnectAccountSession(Request $request)
    {
        $user = Auth::user();

        $stripe = new \Stripe\StripeClient(config('stripe.secret'));

        $account = $stripe->accounts->create([
            'type' => 'express',
        ]);

        $account_session = $stripe->accountSessions->create([
            'account' => $account->id,
            'components' => [
                'account_onboarding' => [
                    'enabled' => true,
                    'features' => ['external_account_collection' => true],
                ],
            ],
        ]);

        return [
            'data' => [
                'account' => $account,
                'account_session' => $account_session,
            ],
        ];
    }

    public function createConnectAccountLink(Request $request)
    {

        $request->validate([
            'callback' => 'required|url',
            'country' => 'required|string',
            'service_agreement' => 'required|in:full,recipient',
        ]);

        $user = Auth::user();

        $accountLink = $this->getAccountLink($user, $request);

        return [
            'data' => [
                'link' => $accountLink,
            ],
        ];
    }

    private function getAccountLink(User $user, $request)
    {
        $country = $request->country ?? 'US';
        $service_agreement = $request->service_agreement ?? UserWithdrawStripeServiceAgreement::Full->value; 

        $hasBindedAccount = $user->withdrawAccounts()
            ->where('bind_status', UserWithdrawAccountStatus::Successed)
            ->where('type', UserWithdrawAccountType::Stripe)
            ->where('stripe_service_agreement', $service_agreement)
            ->first();

        if ($hasBindedAccount) {
            abort(400, 'Already has stripe account');
        }

        $stripe = new \Stripe\StripeClient(config('stripe.secret'));

        $pendingAccount = $user->withdrawAccounts()
            ->where('bind_status', UserWithdrawAccountStatus::Pending)
            ->where('type', UserWithdrawAccountType::Stripe)
            ->first();

        if ($pendingAccount) {
            $pendingAccount->delete();
        }

        $params = [
            'country' => $country,
            'type' => 'custom',
            'email' => $user->email,
            'capabilities' => [
                'transfers' => ['requested' => true],
            ],
            'settings' => [
                'payouts' => [
                    'schedule' => ['interval' => 'manual'],
                ],
            ],
        ];

        if ($service_agreement == UserWithdrawStripeServiceAgreement::Full->value) {
            $params['tos_acceptance']['service_agreement'] = 'full';
            $params['capabilities']['card_payments'] = ['requested' => true];
        }
        if ($service_agreement == UserWithdrawStripeServiceAgreement::Recipient->value) {
            $params['tos_acceptance']['service_agreement'] = 'recipient';
        }

        $account = $stripe->accounts->create($params);
        $account_id = $account->id;

        $user->withdrawAccounts()->create([
            'stripe_connect_account_id' => $account_id,
            'bind_status' => UserWithdrawAccountStatus::Pending,
            'type' => UserWithdrawAccountType::Stripe,
            'stripe_service_agreement' => $service_agreement,
        ]);

        $returnUrl = URL::signedRoute('create_connect_account_return', [
            'account_id' => $account_id,
            'callback' => $request->callback,
        ]);

        $refresh_url = route('create_connect_account_refresh');

        // force https in production environment
        if (App::environment(['prod', 'production'])) {
            $returnUrl = str_replace('http://', 'https://', $returnUrl);
            $refresh_url = str_replace('http://', 'https://', $refresh_url);
        }

        $body = [
            'account' => $account_id,
            'refresh_url' => $refresh_url,
            'return_url' => $returnUrl,
            'type' => 'account_onboarding',
            'collection_options' => ['fields' => 'eventually_due'],
        ];

        $accountLink = $stripe->accountLinks->create($body);

        return $accountLink;
    }

    public function payout(Request $request)
    {
        $stripe = new \Stripe\StripeClient(config('stripe.secret'));

        $stripe->payouts->create([
            'amount' => 100,
            'currency' => 'usd',
        ], ['stripe_account' => 'acct_1RY1d3QFrcJNqje7']);
    }

    public function createConnectAccountRefresh(Request $request)
    {
        abort(400, 'Invalid request');
    }

    public function createConnectAccountReturn(Request $request)
    {
        if (! $request->hasValidSignature()) {
            abort(403, 'Invalid or expired link.');
        }

        $account_id = $request->account_id;
        $callback = $request->callback;

        DB::beginTransaction();
        $withdrawAccount = UserWithdrawAccount::where('stripe_connect_account_id', $account_id)
            ->where('type', UserWithdrawAccountType::Stripe)
            ->first();

        if (! $withdrawAccount) {
            abort(400, 'Withdraw account not found');
        }

        if ($withdrawAccount->bind_status == UserWithdrawAccountStatus::Pending) {
            $withdrawAccount->update([
                'bind_status' => UserWithdrawAccountStatus::Successed,
            ]);
        }

        // get account info
        $stripe = new \Stripe\StripeClient(config('stripe.secret'));
        $account = $stripe->accounts->retrieve($account_id);

        $currency_code = $account->default_currency;

        // create user wallet
        $user = $withdrawAccount->user;
        $currency = Currency::where('code', strtoupper($currency_code))->first();
        if (! $currency) {
            abort(400, "Currency $currency_code not found");
        }

        $withdrawAccount->update([
            'currency_id' => $currency->id,
            'stripe_email' => $account->business_profile->support_email,
        ]);

        // if wallet not exists, create
        $stripe_service_agreement = $withdrawAccount->stripe_service_agreement;
        if ($stripe_service_agreement == UserWithdrawStripeServiceAgreement::Full->value) {
            $usage = WalletUsage::StripeFull;
        }
        if ($stripe_service_agreement == UserWithdrawStripeServiceAgreement::Recipient->value) {
            $usage = WalletUsage::StripeRecipient;
        }

        $user->wallets()->create([
            'usage' => $usage,
            'currency_id' => $currency->id,
            'balance' => 0,
            'stripe_connect_account_id' => $account_id,
        ]);

        DB::commit();

        return redirect($callback);
    }

    public function createPaymentLink(Request $request)
    {
        $request->validate([
            'work_task_id' => 'required|integer|exists:work_tasks,id',
            'type' => 'required|in:stage_pay,full_pay',
            'currency_id' => 'required|integer|exists:currencies,id',
        ]);

        $work_task_id = $request->work_task_id;
        $user = Auth::user();

        $work_task = $user->workTasks()->find($work_task_id);
        if (! $work_task) {
            return response()->json(['msg' => 'Not found'], 400);
        }

        $work_task_stages = $work_task->workTaskStages()
            ->where('is_paid', WorkTaskStage::PayStatusUnpaid)
            ->orderBy('percent', 'asc')
            ->get();
        if ($work_task_stages->isEmpty()) {
            return response()->json(['msg' => 'No unpaid stage found'], 400);
        }

        [$amount, $work_task_stage_ids] = $this->calcAmount($request->type, $work_task_stages);
        if ($amount === 0) {
            return response()->json([
                'msg' => 'Amount is 0',
            ], 422);
        }

        $stripe = new \Stripe\StripeClient(config('stripe.secret'));

        $prodId = 'prod_Pfsih781Nwqz2Q';

        $price = $stripe->prices->create([
            'currency' => 'usd',
            'unit_amount' => $amount,
            'product' => $prodId,
        ]);

        $link = $stripe->paymentLinks->create([
            'line_items' => [
                [
                    'price' => $price->id,
                    'quantity' => 1,
                ],
            ],
            'after_completion' => [
                'type' => 'redirect',
                'redirect' => ['url' => url('/api/stripe/callback_payment_link?' . http_build_query([
                    'pay_type' => $request->type,
                ]))],
            ],
        ]);

        StripePaymentLink::create([
            'link_id' => $link->id,
            'url' => $link->url,
            'pay_type' => $request->type,
        ]);

        return ['data' => ['url' => $link->url]];
    }

    public function paymentLinkCallback(Request $request)
    {
        Log::info('paymentLinkCallback', $request->all());

        return $request->all();
    }

    public function webhook(Request $request)
    {

        Log::info('stripe_webhook', $request->all());

        StripeWebhook::create([
            'raw' => $request->getContent(),
        ]);

        return ['ok' => true];
    }

    public function paymentIntent(Request $request)
    {
        $stripe = new \Stripe\StripeClient(config('stripe.secret'));

        $paymentIntent = $stripe->paymentIntents->create([
            'amount' => 2000,
            'currency' => 'usd',
            'automatic_payment_methods' => ['enabled' => true],
        ]);

        return $paymentIntent;
    }

    public function setupIntent()
    {
        $stripe = new \Stripe\StripeClient(config('stripe.secret'));

        $customId = 'cus_PgexIsXch2RYrC';
        $ret = $stripe->setupIntents->create([
            'customer' => $customId,
            'automatic_payment_methods' => ['enabled' => true],
        ]);

        return $ret;
    }

    private function calcAmount($type, $work_task_stages, $priceChange = null)
    {
        $amount = 0;
        $work_task_stage_ids = [];
        if ($type == 'full_pay') {
            $amount = $work_task_stages->sum('amount');
            $work_task_stage_ids = $work_task_stages->pluck('id')->toArray();
        }
        if ($type == 'stage_pay') {
            $work_task_stage = $work_task_stages->first();
            $amount = $work_task_stage->amount;
            $work_task_stage_ids = [$work_task_stage->id];
        }
        if ($type == 'price_change') {
            $amount = $priceChange->need_pay_amount;
            $work_task_stage_ids = [];
        }

        return [$amount, $work_task_stage_ids];
    }

    private function calcAmountByWallets($amount, $wallets, $target_currency)
    {
        $process = [];

        $amount_before = $amount;
        $amount_after = $amount;
        foreach ($wallets as $wallet) {
            if ($amount_after <= 0) {
                continue;
            }
            $wallet_balance = $wallet->balance;
            $wallet_balance_code = $wallet->currency->code;
            if ($wallet_balance <= 0) {
                continue;
            }

            $wallet_target_currency_balance = RateService::new()->amountExchange($wallet_balance, $wallet_balance_code, $target_currency->code, 'floor');

            if ($amount_before - $wallet_target_currency_balance > 0) {
                $amount_minus = $wallet_target_currency_balance;
                $amount_after = $amount_before - $amount_minus;
                $wallet_minus = $wallet_balance;
                $wallet_balance_after = 0;
            } else {
                $amount_minus = $amount_before;
                $amount_after = 0;
                $wallet_minus = RateService::new()->amountExchange($amount_minus, $target_currency->code, $wallet_balance_code, 'ceil');
                $wallet_balance_after = $wallet_balance - $wallet_minus;
            }

            $process[] = [
                'wallet_id' => $wallet->id,
                'wallet_currency_id' => $wallet->currency_id,
                'wallet_balance' => $wallet_balance,
                'wallet_balance_code' => $wallet_balance_code,
                'wallet_target_currency_balance' => $wallet_target_currency_balance,
                'wallet_target_currency_balance_code' => $target_currency->code,
                'wallet_minus' => $wallet_minus,
                'wallet_minus_code' => $wallet_balance_code,
                'wallet_balance_after' => $wallet_balance_after,
                'wallet_balance_after_code' => $wallet_balance_code,
                'amount_before' => $amount_before,
                'amount_before_code' => $target_currency->code,
                'amount_minus' => $amount_minus,
                'amount_minus_code' => $target_currency->code,
                'amount_after' => $amount_after,
                'amount_after_code' => $target_currency->code,
                'type' => 'wallet_balance',
            ];

            $amount_before = $amount_after;
        }

        return [
            'process' => $process,
            'amount_before' => $amount,
            'amount_after' => $amount_after,
        ];
    }

    public function callbackCheckoutSession(Request $request)
    {
        $request->validate([
            'checkout_session_id' => 'required|string',
        ]);

        $checkout_session_id = $request->checkout_session_id;
        $checkout_session = StripeClientService::New(saveDB: true)->getCheckoutSession($checkout_session_id);

        $stripeCheckoutSession = StripeCheckoutSession::where('checkout_session_id', $checkout_session_id)->first();
        if (! $stripeCheckoutSession) {
            abort(400, 'Stripe checkout session not found');
        }

        $order = $stripeCheckoutSession->order;
        if (! $order) {
            abort(400, 'Order not found');
        }

        if ($order->status !== OrderStatus::Paying) {
            abort(400, 'Order status is not paying');
        }

        if ($checkout_session->status == 'complete' && $checkout_session->payment_status == 'paid') {
            $stripeWebhookService = new StripeWebhookService;
            $stripeWebhookService->processCheckoutSessionCompleted([
                'data' => [
                    'object' => $checkout_session,
                ],
                'type' => \Stripe\Event::TYPE_CHECKOUT_SESSION_COMPLETED,
            ]);
        }

        return [
            'data' => [
                'status' => $checkout_session->status,
            ],
        ];
    }

    public function createWorkTaskPaymentIntent(Request $request)
    {
        $request->validate([
            'work_task_id' => 'required|integer|exists:work_tasks,id',
            'type' => 'required|in:stage_pay,full_pay',
            'currency_id' => 'required|integer|exists:currencies,id',
        ]);

        $work_task_id = $request->work_task_id;
        $user = Auth::user();

        $work_task = $user->workTasks()->find($work_task_id);
        if (! $work_task) {
            return response()->json(['msg' => 'Not found'], 400);
        }

        $work_task_stages = $work_task->workTaskStages()
            ->where('is_paid', WorkTaskStage::PayStatusUnpaid)
            ->orderBy('percent', 'asc')
            ->get();
        if ($work_task_stages->isEmpty()) {
            return response()->json(['msg' => 'No unpaid stage found'], 400);
        }

        [$amount, $work_task_stage_ids] = $this->calcAmount($request->type, $work_task_stages);
        if ($amount === 0) {
            return response()->json([
                'msg' => 'Amount is 0',
            ], 422);
        }

        $currency = Currency::find($request->currency_id);

        $stripe = new \Stripe\StripeClient(config('stripe.secret'));
        $currency = $request->get('currency', 'usd');

        $paymentIntent = $stripe->paymentIntents->create([
            'amount' => $amount,
            'currency' => $currency,
            'automatic_payment_methods' => ['enabled' => true],
        ]);

        StripePaymentIntent::create([
            'payment_intent' => $paymentIntent->id,
            'pay_type' => $request->type,
        ]);

        return ['data' => [
            'id' => $paymentIntent->id,
            'client_secret' => $paymentIntent->client_secret,
            'amount' => $amount,
            'currency' => $currency,
        ]];
    }

    public function createProductCheckoutSession(Request $request)
    {
        $request->validate([
            'currency_id' => 'required|integer|exists:currencies,id',
            'product_order_number' => 'required|integer|exists:product_orders,order_number',
        ]);

        $user = Auth::user();
        $stripe = new \Stripe\StripeClient(config('stripe.secret'));

        $currency_id = $request->currency_id;
        $currency = Currency::find($currency_id);

        $order = $user->productOrders()->where('order_number', $request->product_order_number)->first();
        if (! $order) {
            abort(400, 'Order Not found');
        }

        $amount = $order->amount;

        $checkout_session = $stripe->checkout->sessions->create([
            'line_items' => [[
                'price_data' => [
                    'currency' => $currency->code,
                    'product_data' => [
                        'name' => 'test product',
                    ],
                    'unit_amount' => $amount,
                ],
                'quantity' => 1,
            ]],
            'mode' => 'payment',
            'ui_mode' => 'embedded',
            'redirect_on_completion' => 'never',
        ]);

        StripeCheckoutSession::create([
            'bus_type' => 'product',
            'checkout_session_id' => $checkout_session->id,
            'product_order_id' => $order->id,
            'pay_type' => StripeCheckoutSession::PAY_TYPE_FULL_PAY,
        ]);

        return ['data' => [
            'id' => $checkout_session->id,
            'client_secret' => $checkout_session->client_secret,
            'amount' => $amount,
            'currency' => $currency,
        ]];
    }

    public function createEmbeddedAccountSessions(Request $request)
    {
        $user = Auth::user();
        $stripe = new \Stripe\StripeClient(config('stripe.secret'));

        $account = $user->withdrawAccounts()->where('type', UserWithdrawAccountType::Stripe)->where('bind_status', UserWithdrawAccountStatus::Successed)->first();
        if (! $account) {
            abort(400, 'No Stripe connected account');
        }

        $accountSession = $stripe->accountSessions->create([
            'account' => $account->stripe_connect_account_id,
            'components' => [
                'balances' => [
                    'enabled' => true,
                    'features' => [
                        'instant_payouts' => true,
                        'standard_payouts' => true,
                        'external_account_collection' => true,
                    ],
                ],
            ],
        ]);

        return [
            'data' => $accountSession,
        ];
    }
}
