<?php

namespace App\Http\Controllers\Api\Content;

use App\Http\Controllers\Controller;
use App\Models\Tag;
use Illuminate\Http\Request;

class TagController extends Controller
{
    public function list(Request $request)
    {

        $request->validate([
            'category' => 'nullable|string',
            'keyword' => 'nullable|string',
            'page' => 'integer|min:1',
            'size' => 'integer|min:1,max:50',
        ]);

        $page = $request->input('page', 1);
        $size = $request->input('size', 15);

        $query = Tag::query();
        $needPaginate = true;

        if ($request->keyword) {
            $keyword = $request->input('keyword');
            $keyword = strtolower($keyword);
            $query->where('search_text', 'like', '%'.$keyword.'%');
        }

        if ($request->category) {
            $query->whereHas('tagCategories', function ($query) use ($request) {
                $query->where('type', $request->input('category'));
            });
            $needPaginate = false;
        }

        $ret = $query->orderByDesc('id');

        if ($needPaginate) {
            $ret = $ret->paginate($size);

            return [
                'data' => $ret->items(),
                'total' => $ret->total(),
            ];
        }

        $ret = $ret->get();

        return [
            'data' => $ret,
            'total' => $ret->count(),
        ];

    }
}
