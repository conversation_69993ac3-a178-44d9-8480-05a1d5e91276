<?php

namespace App\Http\Controllers\Api\Content;

use App\Http\Controllers\Controller;
use App\Models\ReminderTemplate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Broadcast;

class ReminderTemplateController extends Controller
{
    public function list(Request $request)
    {
        $request->validate([
            'category_id' => 'nullable|integer|exists:reminder_categories,id',
            'search_text' => 'nullable|string',
        ]);

        $query = ReminderTemplate::query()
            ->active();

        // 按分类筛选
        if ($request->filled('category_id')) {
            $query->where('category_id', $request->category_id);
        }
        if ($request->filled('search_text')) {
            $query->where('title', 'like', '%'.$request->search_text.'%');
        }

        $templates = $query->orderBy('id', 'desc')->get();

        return [
            'data' => $templates,
        ];
    }

    /**
     * Authenticate the request for channel access.
     *
     * @return \Illuminate\Http\Response
     */
    public function authenticate(Request $request)
    {
        if ($request->hasSession()) {
            $request->session()->reflash();
        }

        $ret = Broadcast::auth($request);
        dd($ret);

        return Broadcast::auth($request);
    }
}
