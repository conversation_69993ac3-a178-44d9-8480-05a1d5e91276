<?php

namespace App\Http\Controllers\Api\Content;

use App\Enums\ErrorCode;
use App\Http\Controllers\Controller;
use App\Service\CaptchaSservice;
use Illuminate\Http\Request;

class CaptchaController extends Controller
{
    public function create(Request $request)
    {
        return CaptchaSservice::Generate();
    }

    public function verify(Request $request)
    {
        $request->validate([
            'captcha_value' => 'required|string',
            'key' => 'required|string',
        ]);

        if (! CaptchaSservice::Verify($request->key, $request->captcha_value)) {
            return response()->json([
                'code' => ErrorCode::SystemCaptchaError->value,
                'message' => 'You captcha is wrong.',
            ], 400);
        }

        return [
            'ok' => true,
        ];
    }
}
