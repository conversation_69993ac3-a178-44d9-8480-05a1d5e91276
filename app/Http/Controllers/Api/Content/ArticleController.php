<?php

namespace App\Http\Controllers\Api\Content;

use App\Http\Controllers\Controller;
use App\Models\Article;
use Illuminate\Http\Request;

class ArticleController extends Controller
{
    public function list(Request $request)
    {
        $request->validate([
            'page' => 'integer|min:1',
            'size' => 'integer|min:1,max:50',
            'category_id' => 'integer|exists:article_categories,id',
        ]);

        $page = $request->input('page', 1);
        $size = $request->input('size', 15);

        $query = Article::query()
            ->select('id', 'title', 'article_category_id', 'created_at')
            ->with(['category'])
            ->orderByDesc('sort')
            ->orderByDesc('updated_at')
            ->orderByDesc('created_at');

        if ($request->has('category_id')) {
            $query->where('article_category_id', $request->input('category_id'));
        }

        $ret = $query->paginate($size);

        return [
            'data' => $ret->items(),
            'total' => $ret->total(),
        ];
    }

    public function info(Request $request)
    {
        $request->validate([
            'id' => 'required|integer',
        ]);

        $article = Article::query()
            ->with(['category'])
            ->find($request->input('id'));

        if (! $article) {
            abort(404, 'Article not found');
        }

        return [
            'data' => $article,
        ];
    }
}
