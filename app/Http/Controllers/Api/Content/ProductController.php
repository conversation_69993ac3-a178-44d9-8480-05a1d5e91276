<?php

namespace App\Http\Controllers\Api\Content;

use App\Http\Controllers\Controller;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ProductController extends Controller
{
    public function list(Request $request)
    {
        $request->validate([
            'page' => 'integer|min:1',
            'size' => 'integer|min:1,max:50',
        ]);

        if ($request->bearerToken() && $user = Auth::guard('sanctum')->user()) {
            Auth::setUser($user);
        }

        $page = $request->input('page', 1);
        $size = $request->input('size', 15);

        $query = Product::query();
        $query->with([
            'currency',
            'tags',
            'categories',
            'showcases',
        ])
            ->withSavedStatus()
            ->orderByDesc('id');

        $ret = $query->paginate($size);

        return [
            'data' => $ret->items(),
            'total' => $ret->total(),
        ];
    }

    public function info(Request $request)
    {
        $request->validate([
            'id' => 'integer|min:1|exists:products,id',
        ]);

        if ($request->bearerToken() && $user = Auth::guard('sanctum')->user()) {
            Auth::setUser($user);
        }

        $query = Product::query();
        $query->with([
            'currency',
            'tags',
            'categories',
            'showcases',
        ])
            ->withSavedStatus()
            ->orderByDesc('id');

        $ret = $query->find($request->id);

        return [
            'data' => $ret,
        ];
    }
}
