<?php

namespace App\Http\Controllers\Api\Content;

use App\Http\Controllers\Controller;
use App\Models\Artist;
use App\Models\ArtStyle;
use App\Models\Category;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ArtistController extends Controller
{
    use AuthorizesRequests, DispatchesJobs, ValidatesRequests;

    public function artistList(Request $request)
    {
        $request->validate([
            'category_ids' => 'array',
            'category_ids.*' => 'integer',
            'art_style_ids' => 'array',
            'art_style_ids.*' => 'integer',
            'is_open' => 'boolean',
            'keyword' => 'string',
            'order_by' => 'string',
        ]);

        if ($request->bearerToken() && $user = Auth::guard('sanctum')->user()) {
            Auth::setUser($user);
        }

        $per_page = 5;
        if ($request->per_page) {
            $per_page = $request->per_page;
        }

        $query = Artist::query();

        if ($request->category_ids) {
            $query->whereHas('services', function ($query) use ($request) {
                $query->whereHas('categories', function ($query) use ($request) {
                    $query->whereIn('category_id', $request->category_ids);
                });
            })->orWhereHas('artworks', function ($query) use ($request) {
                $query->whereHas('categories', function ($query) use ($request) {
                    $query->whereIn('category_id', $request->category_ids);
                });
            });
        }

        if ($request->art_style_ids) {
            $query->whereHas('services', function ($query) use ($request) {
                $query->whereHas('artStyles', function ($query) use ($request) {
                    $query->whereIn('art_style_id', $request->art_style_ids);
                });
            })->orWhereHas('artworks', function ($query) use ($request) {
                $query->whereHas('artStyles', function ($query) use ($request) {
                    $query->whereIn('art_style_id', $request->art_style_ids);
                });
            });
        }

        if ($request->has('is_open')) {
            $query->where('is_open', $request->is_open);
        }

        if ($request->keyword) {
            $query->where('name', 'LIKE', "%{$request->keyword}%");
        }

        // 最新(newest)，最热(recent_hot = followers)，推荐(recommend_score = likes * weight)
        $order_by = $request->get('order_by', 'newest');

        if ($order_by == 'recent_hot') {
            $query->orderBy('followers', 'desc');
        } elseif ($order_by == 'recommend') {
            $query->orderBy('recommend_score', 'desc');
        } elseif ($order_by == 'newest') {
            $query->orderBy('created_at', 'desc');
        }
        $query->orderBy('id', 'desc');

        $artists = $query->with([
            'artworks' => function ($query) {
                $query
                    ->withSavedStatus()
                    ->with([
                        'tags',
                        'uploadImage',
                        'uploadVideo',
                        'categories',
                        'artStyles',
                    ])
                    ->take(4);
            },
            'avatar',
        ])
            ->withSavedStatus()
            ->paginate($per_page);

        return [
            'data' => $artists->items(),
            'current_page' => $artists->currentPage(),
            'total' => $artists->total(),
        ];

    }

    public function artistInfo(Request $request)
    {
        if ($request->bearerToken() && $user = Auth::guard('sanctum')->user()) {
            Auth::setUser($user);
        }

        $request->validate([
            'artist_id' => 'integer|min:1',
        ]);

        $artist = Artist::where('id', $request->artist_id)
            ->withSavedStatus()
            ->with(['avatar', 'cover', 'tags'])
            ->first();

        if (! $artist) {
            abort(404, 'Artist not found');
        }

        $artist->langs = $artist->langs()->withPivot('fluent')->get();
        $artist->artworks = $artist->artworks()
            ->withSavedStatus()
            ->with(['tags', 'uploadImage', 'uploadVideo', 'categories', 'artStyles'])->get();

        $artist->artist_rec_reviews = $artist->artistRecReviews()
            ->with(['userInfo.avatar', 'serviceInfo'])->get();

        $artist->products = $artist->products()
            ->withSavedStatus()
            ->with(['currency', 'tags', 'categories', 'showcases'])->get();

        $artist->services = $artist->services()
            ->withSavedStatus()
            ->with(['showcases', 'currency'])->get();

        return [
            'data' => $artist,
        ];
    }

    public function artistFilters(Request $request)
    {
        $categories = Category::tree()->get()->toTree();
        $art_styles = ArtStyle::tree()->get()->toTree();
        $retrn_data = [
            'categories' => $categories,
            'art_styles' => $art_styles,
        ];

        return [
            'data' => $retrn_data,
        ];
    }
}
