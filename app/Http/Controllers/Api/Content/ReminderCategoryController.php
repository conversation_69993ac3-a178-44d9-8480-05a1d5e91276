<?php

namespace App\Http\Controllers\Api\Content;

use App\Http\Controllers\Controller;
use App\Models\ReminderCategory;
use Illuminate\Http\Request;

class ReminderCategoryController extends Controller
{
    public function list(Request $request)
    {
        $ret = ReminderCategory::select(['id', 'name', 'role'])
            ->orderBy('id', 'asc')
            ->get();

        return [
            'data' => $ret,
        ];
    }
}
