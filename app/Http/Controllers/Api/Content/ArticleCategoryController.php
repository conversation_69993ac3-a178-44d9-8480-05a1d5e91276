<?php

namespace App\Http\Controllers\Api\Content;

use App\Http\Controllers\Controller;
use App\Models\ArticleCategory;
use Illuminate\Http\Request;

class ArticleCategoryController extends Controller
{
    public function list(Request $request)
    {
        $request->validate([
            'page' => 'integer|min:1',
            'size' => 'integer|min:1,max:50',
            'with_article_size' => 'integer|min:0',
        ]);

        $page = $request->input('page', 1);
        $size = $request->input('size', 15);
        $withArticleSize = $request->input('with_article_size', 0);

        $query = ArticleCategory::query()
            ->withCount('articles')
            ->orderByDesc('sort')
            ->orderByDesc('id');

        if ($withArticleSize) {
            $query = $query->with([
                'articles' => function ($query) use ($withArticleSize) {
                    $query
                        ->select('id', 'title', 'article_category_id')
                        ->orderByDesc('sort')
                        ->orderByDesc('updated_at')
                        ->orderByDesc('created_at')
                        ->limit($withArticleSize);
                },
            ]);
        }

        $ret = $query->paginate($size);

        return [
            'data' => $ret->items(),
            'total' => $ret->total(),
        ];
    }
}
