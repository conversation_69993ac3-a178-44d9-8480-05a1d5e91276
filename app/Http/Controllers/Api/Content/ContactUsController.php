<?php

namespace App\Http\Controllers\Api\Content;

use App\Enums\ErrorCode;
use App\Http\Controllers\Controller;
use App\Models\ContactUs;
use App\Service\CaptchaSservice;
use Illuminate\Http\Request;

class ContactUsController extends Controller
{
    public function create(Request $request)
    {
        $request->validate([
            'captcha_value' => 'required|string',
            'key' => 'required|string',
        ]);

        if (! CaptchaSservice::Verify($request->key, $request->captcha_value)) {
            return response()->json([
                'code' => ErrorCode::SystemCaptchaError->value,
                'message' => 'You captcha is wrong.',
            ], 400);
        }

        $request->validate([
            'name' => 'required',
            'email' => 'required|email',
            'content' => 'required',
        ]);

        ContactUs::create([
            'name' => $request->name,
            'email' => $request->email,
            'content' => $request->content,
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
        ]);

        return [
            'ok' => true,
        ];
    }
}
