<?php

namespace App\Http\Controllers\Api\Content;

use App\Http\Controllers\Controller;
use App\Models\Currency;
use App\Service\RateService;
use Cache;

class CurrenciesController extends Controller
{
    public function rates()
    {
        $rates = Cache::remember('api:content:currencies:rates', 60 * 60, function () {
            return RateService::new()->all();
        });

        return [
            'data' => $rates,
        ];
    }

    public function list()
    {
        $currencies = Cache::remember('api:content:currencies:list', 60 * 60, function () {
            return Currency::get();
        });

        return [
            'data' => $currencies,
        ];
    }
}
