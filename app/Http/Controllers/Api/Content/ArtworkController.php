<?php

namespace App\Http\Controllers\Api\Content;

use App\Http\Controllers\Controller;
use App\Models\ArtStyle;
use App\Models\Artwork;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ArtworkController extends Controller
{
    public function artworkList(Request $request)
    {

        $request->validate([
            'category_ids' => 'array',
            'category_ids.*' => 'integer',
            'art_style_ids' => 'array',
            'art_style_ids.*' => 'integer',
            'order_by' => 'string',
        ]);

        if ($request->bearerToken() && $user = Auth::guard('sanctum')->user()) {
            Auth::setUser($user);
        }

        $per_page = 20;
        if ($request->per_page) {
            $per_page = $request->per_page;
        }
        $query = Artwork::query();

        if ($request->has('category_ids')) {
            $query->whereHas('categories', function ($query) use ($request) {
                $query->whereIn('category_id', $request->category_ids);
            });
        }
        if ($request->has('art_style_ids')) {
            $query->whereHas('artStyles', function ($query) use ($request) {
                $query->whereIn('art_style_id', $request->art_style_ids);
            });
        }

        $order_by = $request->get('order_by', 'newest');

        if ($order_by == 'recent_hot') {
            $query->where('created_at', '>=', now()->subDays(7))
                ->orderBy('likes', 'desc');
        } elseif ($order_by == 'recommend') {
            $query->orderBy('recommend_score', 'desc');
        } elseif ($order_by == 'newest') {
            $query->orderBy('created_at', 'desc');
        }
        $query->orderBy('id', 'desc');

        $artworks = $query->with([
            'artist' => function ($query) {
                $query->withSavedStatus()->with('avatar');
            },
            'tags',
            'uploadImage',
            'uploadVideo',
            'categories',
            'artStyles',
        ])
            ->withSavedStatus()
            ->paginate($per_page);

        return [
            'data' => $artworks->items(),
            'current_page' => $artworks->currentPage(),
            'total' => $artworks->total(),
        ];
    }

    public function artworkInfo(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|exists:artworks,id',
        ]);

        if ($request->bearerToken() && $user = Auth::guard('sanctum')->user()) {
            Auth::setUser($user);
        }

        $query = Artwork::query();

        $artwork = $query->with([
            'artist' => function ($query) {
                $query->withSavedStatus()->with('avatar');
            },
            'tags',
            'uploadImage',
            'uploadVideo',
            'categories',
            'artStyles',
        ])
            ->withSavedStatus()
            ->find($request->id);

        return [
            'data' => $artwork,
        ];
    }

    public function artistFilters(Request $request)
    {
        $categories = Category::tree()->get()->toTree();
        $art_styles = ArtStyle::tree()->get()->toTree();
        $retrn_data = [
            'categories' => $categories,
            'art_styles' => $art_styles,
        ];

        return [
            'data' => $retrn_data,
        ];
    }
}
