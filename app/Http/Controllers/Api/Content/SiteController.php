<?php

namespace App\Http\Controllers\Api\Content;

use App\Http\Controllers\Controller;
use App\Models\Language;
use App\Models\RightTemplate;
use App\Models\SiteAsset;
use App\Models\SiteSelectedReview;
use App\Service\DownloadService;
use Illuminate\Http\Request;
use Storage;

class SiteController extends Controller
{
    public function meta()
    {
        $languages = Language::get();

        $assets = SiteAsset::query()
            ->where('is_active', true)
            ->with([
                'artwork' => function ($query) {
                    $query->withTrashed()->with([
                        'tags:id,name',
                        'uploadImage',
                        'uploadVideo',
                        'artist:id,name,avatar_id' => [
                            'avatar',
                        ],
                        'categories',
                        'artStyles',
                    ]);
                },
            ])->get();

        $assets->each(function ($asset) {
            if ($asset->artwork && $asset->artwork->uploadImage) {
                $uploadImage = $asset->artwork->uploadImage;
                $uploadImage->makeVisible('url_og');
                $uploadImage->url_lg = $uploadImage->getAttributeValue('url_og');
            }
        });

        $right_templates = RightTemplate::with([
            'rights',
        ])->get();

        $selected_reviews = SiteSelectedReview::with([
            'user:id,name,avatar_id' => [
                'avatar',
            ],
        ])->get();

        $right_templates->each(function ($rightTemplate) {
            $rightTree = $rightTemplate->getRightTree();
            $rightTemplate->setAttribute('right_tree', $rightTree);
            $rightTemplate->makeHidden(['rightPivots', 'rights']);
        });

        return [
            'data' => [
                'languages' => $languages,
                'assets' => $assets,
                'right_templates' => $right_templates,
                'selected_reviews' => $selected_reviews,
            ],
        ];
    }

    public function download(Request $request)
    {
        $request->validate([
            'key' => 'required|string',
        ]);

        $data = DownloadService::parseDownloadKey($request->query('key'));

        $filePath = $data['file_path'];
        $expireAt = $data['expire_at'];
        if (time() > $expireAt) {
            return response()->json([
                'msg' => 'download key expired',
            ], 403);
        }

        return Storage::download($filePath);
    }
}
