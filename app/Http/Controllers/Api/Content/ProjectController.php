<?php

namespace App\Http\Controllers\Api\Content;

use App\Enums\TranslateStatus;
use App\Enums\TranslateType;
use App\Http\Controllers\Controller;
use App\Models\Project;
use App\Models\ProjectRequest;
use App\Service\ArtistService;
use Illuminate\Http\Request;

class ProjectController extends Controller
{
    public function list(Request $request)
    {
        $request->validate([
            'page' => 'integer|min:1',
            'size' => 'integer|min:1,max:50',
        ]);

        $page = $request->input('page', 1);
        $size = $request->input('size', 15);

        $projects = Project::query()
            ->where('is_private', false)
            ->where('is_archived', false)
            ->with([
                'examples' => function ($query) {
                    $query->take(8);
                },
                'categories',
                'currency',
            ])
            ->withCount(['projectRequests'])
            ->orderByDesc('id')
            ->paginate($size);

        return [
            'data' => $projects->items(),
            'total' => $projects->total(),
        ];
    }

    public function info(Request $request)
    {
        $request->validate([
            'id' => 'integer|min:1',
        ]);

        $query = Project::query();
        $query->where('is_private', false);

        $ret = $query->with([
            'user:id,name,avatar_id' => [
                'avatar',
            ],
            'currency:id,name,code,symbol',
            'categories',
            'colorModels',
            'formats',
            'stages',
            'artStyles',
            'examples',
            'projectRequests' => function ($query) {
                $query
                    ->where('status', '!=', ProjectRequest::StatusArtistCanceled)
                    ->with([
                        'artworks' => function ($query) {
                            $query
                                ->with([
                                    'uploadImage',
                                    'uploadVideo',
                                ])
                                ->take(4);
                        },
                        'artist' => [
                            'avatar',
                        ],
                    ]);
            },
            'rightTemplate' => [
                'rightPivots:right_template_id,right_id,state',
            ],
            'translates' => function ($query) {
                $query->select('id', 'busable_id', 'busable_field', 'source_lang', 'target_lang')
                    ->where('status', TranslateStatus::Pending)
                    ->where('translate_type', TranslateType::Human);
            },
        ])->find($request->input('id'));

        if (! $ret) {
            abort(404, 'Project not found');
        }

        $ret->projectRequests->transform(function ($pr) {
            $pr->artist->rating_score = ArtistService::getRatingScore($pr->artist);

            return $pr;
        });

        if ($ret->rightTemplate) {
            $rightTemplateData = $ret->rightTemplate->getRightTree();
            $ret->rightTemplate->setAttribute('right_tree', $rightTemplateData);
            $ret->rightTemplate->makeHidden(['rightPivots', 'rights']);
        }

        return [
            'data' => $ret,
        ];
    }

    public function projectCategoryList(Request $request)
    {

        // $service_categories = Category::with('children.children')->root()->get();
        $project_categories = Category::tree()->get()->toTree();

        return response()->json([
            'status' => 'success',
            'message' => 'Get Service Category List Success',
            'code' => 200,
            'data' => $service_categories,
        ], 200);
    }

    public function projectFilters(Request $request)
    {
        $categories = Category::tree()->get()->toTree();
        $art_styles = ArtStyle::tree()->get()->toTree();
        $workflows = WorkFlow::get();
        // $creation_type = CreationType::get();

        $retrn_data = [
            'categories' => $categories,
            'art_styles' => $art_styles,
            'workflows' => $workflows,
        ];

        return response()->json([
            'status' => 'success',
            'message' => 'Get Project Filter Success',
            'code' => 200,
            'data' => $retrn_data,
        ], 200);
    }
}
