<?php

namespace App\Http\Controllers\Api\Content;

use App\Enums\TranslateStatus;
use App\Enums\TranslateType;
use App\Http\Controllers\Controller;
use App\Models\ArtStyle;
use App\Models\Category;
use App\Models\CreationType;
use App\Models\Currency;
use App\Models\Service;
use App\Models\WorkflowType;
use App\Service\ArtistService;
use App\Service\RateService;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ServiceController extends Controller
{
    use AuthorizesRequests, DispatchesJobs, ValidatesRequests;

    public function serviceList(Request $request)
    {
        $request->validate([
            'category_ids' => 'array',
            'category_ids.*' => 'integer',
            'art_style_ids' => 'array',
            'art_style_ids.*' => 'integer',
            'workflow_type_ids' => 'array',
            'workflow_type_ids.*' => 'integer',
            'creation_type_ids' => 'array',
            'creation_type_ids.*' => 'integer',
            'price_currency_id' => '',
        ]);

        if ($request->bearerToken() && $user = Auth::guard('sanctum')->user()) {
            Auth::setUser($user);
        }

        $per_page = 20;
        if ($request->per_page) {
            $per_page = $request->per_page;
        }

        $query = Service::query();

        if ($request->category_ids) {
            $query->whereHas('categories', function ($query) use ($request) {
                $query->whereIn('category_id', $request->category_ids);
            });
        }

        if ($request->art_style_ids) {
            $query->whereHas('artStyles', function ($query) use ($request) {
                $query->whereIn('art_style_id', $request->art_style_ids);
            });
        }

        if ($request->workflow_type_ids) {
            $query->whereIn('workflow_type_id', $request->workflow_type_ids);
        }

        if ($request->creation_type_ids) {
            $query->whereIn('creation_type_id', $request->creation_type_ids);
        }

        if ($request->price_currency_id) {
            $price_currency = Currency::query()->find($request->price_currency_id);

            if ($request->price_from && $price_currency) {
                $price_from = RateService::new()->amountExchange($request->price_from, $price_currency->code, Service::BASE_PRICE_CURRENCY_CODE);

                $query->where('base_currency_price', '>=', $price_from);
            }

            if ($request->price_to && $price_currency) {
                $price_to = RateService::new()->amountExchange($request->price_to, $price_currency->code, Service::BASE_PRICE_CURRENCY_CODE);

                $query->where('base_currency_price', '<=', $price_to);
            }
        }

        if ($request->rating_score) {
            $query->where('rating_score', '>=', $request->rating_score);
        }

        if ($request->keyword) {
            $keyword_language = 'en';
            if ($request->keyword_language) {
                $keyword_language = $request->keyword_language;
            }
            $serch_json_key = 'name->'.$keyword_language;
            $query->where($serch_json_key, 'LIKE', "%{$request->keyword}%");
        }

        $query->orderBy('status', 'asc');
        if ($request->order_by) {
            if ($request->order_by == 'completed_tasks') {
                $query->orderBy('completed_tasks', 'desc');
            }
            if ($request->order_by == 'followers') {
                $query->orderBy('followers', 'desc');
            }
            if ($request->order_by == 'rating_score') {
                $query->orderBy('followers', 'desc');
            }
            if ($request->order_by == 'recommend') {
                $query->orderBy('recommend_score', 'desc');
            }
        }
        $query->orderByDesc('id');

        $services = $query
            ->with(['artist.avatar', 'showcase', 'currency', 'categories'])
            ->WithSavedStatus()
            ->paginate($per_page);

        return response()->json([
            'status' => 'success',
            'message' => 'Get Service List Success',
            'code' => 200,
            'data' => $services->items(),
            'current_page' => $services->currentPage(),
            'total' => $services->total(),
        ], 200);
    }

    public function serviceInfo(Request $request)
    {
        $this->validate($request, [
            'service_id' => 'integer|min:1',
        ]);

        if ($request->bearerToken() && $user = Auth::guard('sanctum')->user()) {
            Auth::setUser($user);
        }

        $query = Service::query();

        $service = $query->where('id', $request->service_id)->with([
            'artist' => function ($query) {
                $query->with([
                    'avatar',
                    'cover',
                ])
                    ->withCount('artistRecReviews');
            },
            'currency', 'showcases', 'stages',
            'artistRecReviews' => [
                'userInfo' => [
                    'avatar',
                ],
            ],
            'artStyles', 'workflowType', 'creationType',
            'categories',
            'rightTemplate' => [
                'rightPivots:right_template_id,right_id,state',
            ],
            'preferTags',
            'cantdoTags',
            'translates' => function ($query) {
                $query->select('id', 'busable_id', 'busable_field', 'source_lang', 'target_lang')
                    ->where('status', TranslateStatus::Pending)
                    ->where('translate_type', TranslateType::Human);
            },
        ])
            ->WithSavedStatus()
            ->first();

        if (! $service) {
            abort(404, 'Service Not Found');
        }

        if ($service->rightTemplate) {
            $rightTemplateData = $service->rightTemplate->getRightTree();
            $service->rightTemplate->setAttribute('right_tree', $rightTemplateData);
            $service->rightTemplate->makeHidden(['rightPivots', 'rights']);
        }

        $service->artist->rating_score = ArtistService::getRatingScore($service->artist);

        return [
            'data' => $service,
        ];
    }

    public function serviceFilters(Request $request)
    {
        $categories = Category::tree()->get()->toTree();
        $art_styles = ArtStyle::tree()->get()->toTree();
        $workflow_types = WorkflowType::get();
        $creation_types = CreationType::get();

        return [
            'data' => [
                'categories' => $categories,
                'art_styles' => $art_styles,
                'workflows_types' => $workflow_types,
                'creation_types' => $creation_types,
            ],
        ];
    }
}
