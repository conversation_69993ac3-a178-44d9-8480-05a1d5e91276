<?php

namespace App\Http\Controllers\Api\User;

use App\Http\Controllers\Controller;
use App\Models\Group;
use App\Services\GroupReminderService;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class GroupReminderController extends Controller
{
    /**
     * 创建群组提醒
     */
    public function create(Request $request)
    {

        $request->validate([
            'group_id' => [
                'required',
                'integer',
                Rule::exists('group_user_pivot', 'group_id')->where('user_id', auth()->id()),
            ],
            'template_id' => 'required|integer|exists:reminder_templates,id',
            'mentioned_user_id' => 'required|integer|exists:users,id',
        ]);

        $sender = auth()->user();
        $group = Group::findOrFail($request->group_id);

        $reminder = GroupReminderService::New()->createReminder(
            sender: $sender,
            group: $group,
            templateId: $request->template_id,
            mentionedUserIds: [$request->mentioned_user_id],
        );

        return [
            'data' => $reminder,
        ];
    }
}
