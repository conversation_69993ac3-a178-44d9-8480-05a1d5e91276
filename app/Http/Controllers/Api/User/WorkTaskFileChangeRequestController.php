<?php

namespace App\Http\Controllers\Api\User;

use App\Enums\SystemNotificationScene;
use App\Http\Controllers\Controller;
use App\Models\WorkTaskFile;
use App\Notifications\SystemNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class WorkTaskFileChangeRequestController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function list(Request $request)
    {
        $request->validate([
            'work_task_file_id' => 'required|integer|min:1|exists:work_task_files,id',
        ]);

        $request->validate([
            'page' => 'integer|min:1',
            'size' => 'integer|min:1,max:50',
        ]);

        $page = $request->input('page', 1);
        $size = $request->input('size', 15);

        $user = Auth::user();
        $workTaskFile = $user->workTaskFiles()->find($request->work_task_file_id);

        if (! $workTaskFile) {
            return response()->json([
                'message' => 'Work task file not found',
            ], 404);
        }

        $ret = $workTaskFile->workTaskFileChangeRequests()->with([
            'uploadImages',
        ])
            ->where('work_task_file_id', $request->work_task_file_id)->paginate($size);

        return [
            'data' => $ret->items(),
            'total' => $ret->total(),
        ];
    }

    public function create(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'work_task_file_id' => 'required|integer|min:1|exists:work_task_files,id',
            'upload_images' => '',
            'content' => 'required',
        ]);

        $workTaskFile = WorkTaskFile::find($request->work_task_file_id);
        $workTask = $workTaskFile->workTask;

        $workTaskFileChangeRequest = $workTaskFile->workTaskFileChangeRequests()->create([
            'content' => $request->content,
            'work_task_id' => $workTask->id,
        ]);

        $uploadImages = $user->uploadImages()
            ->whereIn('id', $request->upload_images)
            ->get();

        $workTaskFileChangeRequest->uploadImages()->saveMany($uploadImages);

        $workTaskFileChangeRequest->setTransByReq('content', $request)->save();

        //notification
        $notification = SystemNotification::make(SystemNotificationScene::WorkTaskFileChangeRequestCreated)
            ->setMeta([
                'work_task_id' => $workTask->id,
                'work_task_file_id' => $workTaskFile->id,
            ]);
        $workTask->artist->notify($notification);

        return [
            'data' => $workTaskFileChangeRequest,
        ];
    }

    public function update(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'id' => 'required|integer|min:1|exists:work_task_file_change_requests,id',
            'upload_images' => '',
            'content' => 'required',
        ]);

        $workTaskFileChangeRequest = $user->workTaskFileChangeRequests()->find($request->id);
        if (! $workTaskFileChangeRequest) {
            return response()->json([
                'message' => 'Work task file change request not found',
            ], 403);
        }

        $workTaskFile = $workTaskFileChangeRequest->workTaskFile;
        $workTask = $workTaskFileChangeRequest->workTaskFile->workTask;

        $userUploadImageIds = $user->uploadImages()->whereIn('id', $request->upload_images)->pluck('id')->toArray();

        $workTaskFileChangeRequest->syncMorphMany($workTaskFileChangeRequest->uploadImages(), $userUploadImageIds);

        $workTaskFileChangeRequest->setTransByReq('content', $request)->save();

        $data = $user->workTaskFileChangeRequests()->with('uploadImages')->find($request->id);

        //notification
        $notification = SystemNotification::make(SystemNotificationScene::WorkTaskFileChangeRequestUpdated)
            ->setMeta([
                'work_task_id' => $workTask->id,
                'work_task_file_id' => $workTaskFile->id,
            ]);
        $workTask->artist->notify($notification);

        return [
            'data' => $data,
        ];
    }
}
