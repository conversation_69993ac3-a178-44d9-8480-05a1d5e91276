<?php

namespace App\Http\Controllers\Api\User;

use App\Enums\ChatVisableStatus;
use App\Events\Echo\ChatCreated;
use App\Events\Echo\ChatUpdated;
use App\Http\Controllers\Controller;
use App\Models\Chat;
use App\Models\ChatUserPivot;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Pagination\Cursor;

class ChatController extends Controller
{
    public function list(Request $request)
    {
        $request->validate([
            'size' => 'integer|min:1,max:50',
            'ts' => 'required|integer|min:0',
            'cursor' => 'nullable|string',
        ]);

        $size = $request->input('size', 15);
        $user = auth()->user();

        $chatUserPivotQuery = ChatUserPivot::query();

        $chatUserPivotQuery->where('order_at_ts', '<=', $request->input('ts'))
            ->where('user_id', '=', $user->id)
            ->where('chat_visable_status', '=', ChatVisableStatus::CanSee)
            ->orderBy('order_at_ts', 'desc')
            ->orderBy('chat_id', 'desc');

        $chatUserPivots = $chatUserPivotQuery->cursorPaginate($size);

        $chatIds = $chatUserPivots->pluck('chat_id');
        $chats = $this->getChatByIds($chatIds, $user);

        $next_cursor = $chatUserPivots->nextCursor()?->encode();

        return [
            'data' => $chats,
            'count' => $chatUserPivots->count(),
            'has_more' => $chatUserPivots->hasMorePages(),
            'next_cursor' => $next_cursor,
            'prev_cursor' => $chatUserPivots->previousCursor()?->encode(),
            'cursor' => $chatUserPivots->cursor()?->encode(),
        ];
    }

    public function newChats(Request $request)
    {
        $user = auth()->user();
        $request->validate([
            'size' => 'integer|min:1,max:50',
            'ts' => 'required|integer|min:0',
            'cursor' => 'nullable|string',
        ]);

        $size = $request->input('size', 15);

        $chatUserPivotQuery = ChatUserPivot::query();

        $chatUserPivotQuery->where('order_at_ts', '>', $request->input('ts'))
            ->where('user_id', '=', $user->id)
            ->where('chat_visable_status', '=', ChatVisableStatus::CanSee)
            ->orderBy('order_at_ts')
            ->orderBy('chat_id');

        $chatUserPivots = $chatUserPivotQuery->cursorPaginate($size);

        $chatIds = $chatUserPivots->pluck('chat_id');
        $chats = $this->getChatByIds($chatIds, $user);

        $next_cursor = $chatUserPivots->nextCursor()?->encode();
        if ($chatUserPivots->hasMorePages() === false) {
            if ($chatUserPivots->count() > 0) {
                $lastItem = collect($chatUserPivots->items())->max();
                $next_cursor = (new Cursor(['order_at_ts' => $lastItem->order_at_ts, 'chat_id' => $lastItem->chat_id], true))->encode();
            } else {
                $next_cursor = $chatUserPivots->cursor()?->encode();
            }
        }

        return [
            'data' => $chats,
            'count' => $chatUserPivots->count(),
            'has_more' => $chatUserPivots->hasMorePages(),
            'next_cursor' => $next_cursor,
            'prev_cursor' => $chatUserPivots->previousCursor()?->encode(),
            'cursor' => $chatUserPivots->cursor()?->encode(),
        ];

    }

    private function getChatByIds($chatIds, $user): Collection
    {

        if ($chatIds->isEmpty()) {
            return new Collection;
        }

        $query = Chat::query()
            ->whereIn('id', $chatIds)
            ->orderByRaw('FIELD(id, '.$chatIds->implode(',').')');
        $this->withQuery($query);

        $chats = $query->get();

        $this->transformChats($chats, $user);

        return $chats;
    }

    private function withQuery($query)
    {
        $query->with([
            'users:id,name,avatar_id,language_id' => [
                'avatar',
                'language',
                'artist:id,avatar_id,user_id,name' => [
                    'avatar',
                ],
                'roles',
            ],
            'lastMessage',
        ]);
    }

    private function transformChats(Collection $chats, $user)
    {
        $chats->transform(function ($item) use ($user) {
            $last_read_cursor = $item->users->where('id', '=', $user->id)->first()?->pivot->last_read_cursor ?? 0;
            $last_message_id = $item->lastMessage?->id ?? 0;
            $item->has_new_message = $last_message_id > $last_read_cursor;

            $item->users->transform(function ($item) {
                $item->pivot->makeHidden('chat_visable_status');
                $item->makeHidden('roles');
                $is_artist = $item->hasRole('artist');
                $item->is_artist = $is_artist;
                if (! $is_artist) {
                    unset($item->artist);
                    $item->artist = null;
                }

                return $item;
            });

            return $item;
        });
    }

    public function createOrJoin(Request $request)
    {
        $user = auth()->user();

        $request->validate([
            'user_id' => 'required|integer|exists:users,id',
        ]);

        $key = collect([$user->id, $request->input('user_id')])->sort()->join('_');

        $chat = Chat::where('key', $key)->first();

        if (! $chat) {
            $chat = Chat::create([
                'key' => $key,
            ]);

            $chat->users()->attach($user->id);
            $chat->users()->attach($request->input('user_id'));

            ChatCreated::dispatch($chat);
        }

        $chat->users()->updateExistingPivot($user->id, [
            'chat_visable_status' => ChatVisableStatus::CanSee,
            'order_at_ts' => now()->getTimestampMs(),
        ]);
        event(new ChatUpdated(chat: $chat, receiver: $user));

        $query = Chat::query()->where('id', $chat->id);
        $this->withQuery($query);
        $chat = $query->first();

        return [
            'data' => $chat,
        ];
    }

    public function updateSettings(Request $request)
    {
        $user = auth()->user();
        $request->validate([
            'chat_id' => 'required|integer|exists:chats,id',
            'settings' => 'array',
        ]);

        $chatUser = ChatUserPivot::where('chat_id', $request->input('chat_id'))
            ->where('user_id', $user->id)
            ->first();

        if (! $chatUser) {
            return abort(404, 'Chat user not found');
        }

        $chatUser->update([
            'settings' => $request->input('settings'),
        ]);

        return [
            'data' => $chatUser->settings,
        ];
    }

    public function getSettings(Request $request)
    {
        $user = auth()->user();
        $request->validate([
            'chat_id' => 'required|integer|exists:chats,id',
        ]);

        $chatUser = ChatUserPivot::where('chat_id', $request->input('chat_id'))
            ->where('user_id', $user->id)
            ->first();

        return [
            'data' => $chatUser->settings,
        ];
    }
}
