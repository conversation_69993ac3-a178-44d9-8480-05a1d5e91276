<?php

namespace App\Http\Controllers\Api\User;

use App\Enums\OrderStatus;
use App\Http\Controllers\Controller;
use App\Models\StripeCheckoutSession;
use App\Service\OrderService;
use Auth;
use DB;
use Illuminate\Http\Request;

class OrderController extends Controller
{
    public function cancel(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|exists:orders,id',
        ]);

        $id = $request->id;
        $user = Auth::user();

        $order = $user->orders()->find($id);
        if (! $order) {
            abort(400, 'Order not found');
        }

        if ($order->status != OrderStatus::Paying) {
            abort(400, 'Order status is not paying');
        }

        $busable = $order->busable;
        if (! $busable) {
            abort(400, 'Busable not found');
        }

        DB::beginTransaction();
        OrderService::cancelOrder($order);
        DB::commit();

        $stripe = new \Stripe\StripeClient(config('stripe.secret'));
        $stripe->checkout->sessions->expire($order->stripeCheckoutSessions()->first()->checkout_session_id);

        return [
            'ok' => true,
            'data' => [
                'order' => $order,
            ],
        ];
    }

    public function continue(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|exists:orders,id',
        ]);

        $id = $request->id;
        $user = Auth::user();

        $order = $user->orders()->find($id);
        if (! $order) {
            abort(400, 'Order not found');
        }

        if ($order->status != OrderStatus::Paying) {
            abort(400, 'Order status is not paying');
        }

        $busable = $order->busable;
        if (! $busable) {
            abort(400, 'Busable not found');
        }

        $session = $order->stripeCheckoutSessions()->first();
        if ($session) {
            return [
                'data' => [
                    'checkout_session_id' => $session->checkout_session_id,
                    'client_secret' => $session->client_secret,
                    'amount' => $order->amount,
                    'currency' => $order->currency,
                ],
            ];
        }

        $stripe = new \Stripe\StripeClient(config('stripe.secret'));

        $checkout_session = $stripe->checkout->sessions->create([
            'line_items' => [[
                'price_data' => [
                    'currency' => $order->currency->code,
                    'product_data' => [
                        'name' => 'test product',
                    ],
                    'unit_amount' => $order->amount,
                ],
                'quantity' => 1,
            ]],
            'mode' => 'payment',
            'ui_mode' => 'embedded',
            'redirect_on_completion' => 'never',
            'customer_email' => '<EMAIL>',
        ]);

        StripeCheckoutSession::create([
            'checkout_session_id' => $checkout_session->id,
            'client_secret' => $checkout_session->client_secret,
            'order_id' => $order->id,
        ]);

        return [
            'data' => [
                'checkout_session_id' => $checkout_session->id,
                'client_secret' => $checkout_session->client_secret,
                'amount' => $order->amount,
                'currency' => $order->currency,
            ],
        ];
    }
}
