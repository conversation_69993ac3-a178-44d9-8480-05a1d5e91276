<?php

namespace App\Http\Controllers\Api\User;

use App\Events\Echo\GroupUpdated;
use App\Http\Controllers\Controller;
use App\Models\Group;
use App\Models\GroupUserPivot;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Pagination\Cursor;

class GroupController extends Controller
{
    public function list(Request $request)
    {
        $request->validate([
            'size' => 'integer|min:1,max:50',
            'ts' => 'required|integer|min:0',
            'cursor' => 'nullable|string',
        ]);

        $size = $request->input('size', 15);
        $user = auth()->user();

        $groupUserPivotQuery = GroupUserPivot::query();

        $groupUserPivotQuery->where('order_at_ts', '<=', $request->input('ts'))
            ->where('user_id', '=', $user->id)
            ->orderBy('order_at_ts', 'desc')
            ->orderBy('group_id', 'desc');
        $groupUserPivots = $groupUserPivotQuery->cursorPaginate($size);

        $groupIds = $groupUserPivots->pluck('group_id');
        $groups = $this->getGroupsByIds($groupIds, $user);

        $next_cursor = $groupUserPivots->nextCursor()?->encode();

        return [
            'data' => $groups,
            'count' => $groupUserPivots->count(),
            'has_more' => $groupUserPivots->hasMorePages(),
            'next_cursor' => $next_cursor,
            'prev_cursor' => $groupUserPivots->previousCursor()?->encode(),
            'cursor' => $groupUserPivots->cursor()?->encode(),
        ];
    }

    public function info(Request $request)
    {
        $user = auth()->user();
        $request->validate([
            'id' => 'integer|exists:groups,id',
            'work_task_id' => 'integer|exists:work_tasks,id',
        ]);

        $query = Group::query();
        $query->whereHas('users', function ($query) use ($user) {
            $query->where('user_id', $user->id);
        });

        if ($request->input('id')) {
            $query->where('id', $request->input('id'));
        }
        if ($request->input('work_task_id')) {
            $query->where('work_task_id', $request->input('work_task_id'));
        }
        $this->withQuery($query);
        $group = $query->first();

        return [
            'data' => $group,
        ];
    }

    public function openGroup(Request $request)
    {
        $user = auth()->user();
        $request->validate([
            'id' => 'integer|exists:groups,id',
            'work_task_id' => 'integer|exists:work_tasks,id',
        ]);

        $query = Group::query();
        $query->whereHas('users', function ($query) use ($user) {
            $query->where('user_id', $user->id);
        });

        if ($request->input('id')) {
            $query->where('id', $request->input('id'));
        }
        if ($request->input('work_task_id')) {
            $query->where('work_task_id', $request->input('work_task_id'));
        }
        $group = $query->first();

        if (! $group) {
            return abort(404, 'Group not found');
        }

        $group->users()->updateExistingPivot($user->id, [
            'order_at_ts' => now()->getTimestampMs(),
        ]);
        event(new GroupUpdated(group: $group, receiver: $user));

        return [
            'data' => $group,
        ];
    }

    public function newGroups(Request $request)
    {
        $user = auth()->user();
        $request->validate([
            'size' => 'integer|min:1,max:50',
            'ts' => 'required|integer|min:0',
            'cursor' => 'nullable|string',
        ]);

        $size = $request->input('size', 15);

        $groupUserPivotQuery = GroupUserPivot::query();

        $groupUserPivotQuery->where('order_at_ts', '>', $request->input('ts'))
            ->where('user_id', '=', $user->id)
            ->orderBy('order_at_ts')
            ->orderBy('group_id');
        $groupUserPivots = $groupUserPivotQuery->cursorPaginate($size);

        $groupIds = $groupUserPivots->pluck('group_id');
        $groups = $this->getGroupsByIds($groupIds, $user);

        $next_cursor = $groupUserPivots->nextCursor()?->encode();
        if ($groupUserPivots->hasMorePages() === false) {
            if ($groupUserPivots->count() > 0) {
                $lastItem = collect($groupUserPivots->items())->max();
                $next_cursor = (new Cursor(['order_at_ts' => $lastItem->order_at_ts, 'group_id' => $lastItem->group_id], true))->encode();
            } else {
                $next_cursor = $groupUserPivots->cursor()?->encode();
            }
        }

        return [
            'data' => $groups,
            'count' => $groupUserPivots->count(),
            'has_more' => $groupUserPivots->hasMorePages(),
            'next_cursor' => $next_cursor,
            'prev_cursor' => $groupUserPivots->previousCursor()?->encode(),
            'cursor' => $groupUserPivots->cursor()?->encode(),
        ];

    }

    private function getGroupsByIds($groupIds, $user): Collection
    {
        if ($groupIds->isEmpty()) {
            return new Collection();
        }

        $query = Group::query();
        $query->whereIn('id', $groupIds)
            ->orderByRaw('FIELD(id, '.$groupIds->implode(',').')');
        $this->withQuery($query);
        $groups = $query->get();
        $this->transformGroups($groups, $user);

        return $groups;
    }

    private function withQuery($query)
    {
        $query->with([
            'users:id,name,avatar_id,language_id' => [
                'avatar',
                'language',
                'artist:id,avatar_id,user_id,name' => [
                    'avatar',
                ],
                'roles',
            ],
            'workTask',
            'lastMessage',
        ]);
    }

    private function transformGroups($groups, $user)
    {
        $groups->transform(function ($item) use ($user) {
            $last_read_cursor = $item->users->where('id', '=', $user->id)->first()?->pivot->last_read_cursor ?? 0;
            $last_message_id = $item->lastMessage?->id ?? 0;
            $item->has_new_message = $last_message_id > $last_read_cursor;

            $item->users->transform(function ($item) {
                $item->makeHidden('roles');
                $is_artist = $item->hasRole('artist');
                $item->is_artist = $is_artist;
                if (! $is_artist) {
                    unset($item->artist);
                    $item->artist = null;
                }

                return $item;
            });

            return $item;
        });
    }

    public function updateSettings(Request $request)
    {
        $user = auth()->user();
        $request->validate([
            'group_id' => 'required|integer|exists:groups,id',
            'settings' => 'array',
        ]);

        $groupUser = GroupUserPivot::where('group_id', $request->input('group_id'))
            ->where('user_id', $user->id)
            ->first();

        if (! $groupUser) {
            return abort(404, 'Group user not found');
        }

        $groupUser->update([
            'settings' => $request->input('settings'),
        ]);

        return [
            'data' => $groupUser->settings,
        ];
    }

    public function getSettings(Request $request)
    {
        $user = auth()->user();
        $request->validate([
            'group_id' => 'required|integer|exists:groups,id',
        ]);

        $groupUser = GroupUserPivot::where('group_id', $request->input('group_id'))
            ->where('user_id', $user->id)
            ->first();

        return [
            'data' => $groupUser->settings,
        ];
    }

    public function requestAdminJoin(Request $request)
    {
        $request->validate([
            'group_id' => [
                'required',
                'integer',
            ],
            'work_task_id' => 'integer|min:1',
        ]);

        $user = auth()->user();

        $query = Group::query();

        if ($request->input('work_task_id')) {
            $query->where('work_task_id', $request->input('work_task_id'));
        }
        if ($request->input('group_id')) {
            $query->where('id', $request->input('group_id'));
        }

        $group = $query->first();
        if (! $group) {
            return abort(404, 'Group not found');
        }

        $isInGroup = $group->users()->where('user_id', $user->id)->exists();

        if (! $isInGroup) {
            return abort(400, 'You are not in the group');
        }

        $group->request_admin_join = true;
        $group->save();

        return ['ok' => true];
    }
}
