<?php

namespace App\Http\Controllers\Api\User;

use App\Http\Controllers\Controller;
use App\Models\ProjectManager;
use Illuminate\Http\Request;

class ProjectManagerController extends Controller
{
    public function list(Request $request)
    {
        $request->validate([
            'page' => 'integer|min:1',
            'size' => 'integer|min:1,max:50',
            'preset' => 'string',
        ]);

        $page = $request->input('page', 1);
        $size = $request->input('size', 15);

        $ret = ProjectManager::query()
            ->with(['languages'])
            ->paginate($size);

        return [
            'data' => $ret->items(),
            'total' => $ret->total(),
        ];
    }
}
