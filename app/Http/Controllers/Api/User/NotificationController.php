<?php

namespace App\Http\Controllers\Api\User;

use App\Http\Controllers\Controller;
use App\Http\Resources\NotificationResource;
use App\Models\Artist;
use App\Models\CustomNotification;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class NotificationController extends Controller
{
    public function markAsRead(Request $request)
    {
        $user = auth()->user();
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => Rule::exists('notifications', 'id'),
        ]);

        $ids = $request->input('ids');

        $notifications = CustomNotification::whereIn('id', $ids)
            ->get();

        $notifications->each(function ($notification) {
            $notification->markAsRead();
        });

        return [
            'ok' => true,
        ];
    }

    public function list(Request $request)
    {
        $user = auth()->user();
        $request->validate([
            'size' => 'integer|min:1|max:100',
            'type' => 'string|in:all,unread,read',
        ]);

        $size = $request->input('size', 10);
        $type = $request->input('type', 'all');

        $query = $user->notifications()
            ->when($user->artist, function ($query) use ($user) {
                $query->orWhereHasMorph('notifiable', [Artist::class], function ($query) use ($user) {
                    $query->where('id', $user->artist->id);
                });
            });

        if ($type === 'unread') {
            $query = $query->unread();
        } elseif ($type === 'read') {
            $query = $query->read();
        } else {
            // all
        }

        $query->orderBy('created_at_ts', 'desc');

        $notifications = $query->cursorPaginate($size);

        return [
            'data' => NotificationResource::collection($notifications->items()),
            'has_more' => $notifications->hasMorePages(),
            'next_cursor' => $notifications->nextCursor()?->encode(),
        ];
    }

    public function getUnreadNotifications(Request $request)
    {
        $user = auth()->user();
        $request->validate([
            'ts' => 'integer',
            'size' => 'required|integer|min:1|max:100',
            'mark_as_read' => 'boolean',
        ]);

        $size = $request->input('size', 10);
        $ts = $request->input('ts');
        $markAsRead = $request->input('mark_as_read', false);

        $query = $user->unreadNotifications();

        if ($ts) {
            $query->where('created_at_ts', '>', $ts);
        }

        $query->orderBy('created_at_ts', 'desc');

        $notifications = $query->paginate($size);

        // mark as read
        if ($markAsRead) {
            $notifications->each(function ($notification) {
                $notification->markAsRead();
            });
        }

        return [
            'data' => NotificationResource::collection($notifications->items()),
            'has_more' => $notifications->hasMorePages(),
            'total' => $notifications->total(),
        ];
    }

    public function getReadNotifications(Request $request)
    {
        $user = auth()->user();
        $request->validate([
            'ts' => 'integer',
            'size' => 'required|integer|min:1|max:100',
        ]);

        $size = $request->input('size', 10);
        $ts = $request->input('ts');

        $query = $user->readNotifications();

        if ($ts) {
            $query->where('read_at_ts', '<', $ts);
        }

        $query->orderBy('created_at_ts', 'desc');

        $notifications = $query->paginate($size);

        return [
            'data' => NotificationResource::collection($notifications->items()),
            'has_more' => $notifications->hasMorePages(),
            'total' => $notifications->total(),
        ];

    }

    public function hasUnreadNotification(Request $request)
    {
        $user = auth()->user();
        $query = $user->notifications()
            ->when($user->artist, function ($query) use ($user) {
                $query->orWhereHasMorph('notifiable', [Artist::class], function ($query) use ($user) {
                    $query->where('id', $user->artist->id);
                });
            });
        $count = $query->unread()->count();

        return [
            'has_unread' => $count > 0,
            'total' => $count,
        ];
    }

    public function hasNewNotification(Request $request)
    {
        $user = auth()->user();
        $request->validate([
            'ts' => 'integer',
        ]);

        $ts = $request->input('ts');

        $query = $user->notifications()
            ->when($user->artist, function ($query) use ($user) {
                $query->orWhereHasMorph('notifiable', [Artist::class], function ($query) use ($user) {
                    $query->where('id', $user->artist->id);
                });
            });

        if ($ts) {
            $query->where('created_at_ts', '>', $ts);
        }

        $count = $query->unread()->count();

        return [
            'has_new' => $count > 0,
            'total' => $count,
        ];
    }
}
