<?php

namespace App\Http\Controllers\Api\User;

use App\Http\Controllers\Controller;
use App\Models\Artist;
use App\Models\User;
use Illuminate\Http\Request;

class ProfileController extends Controller
{
    public function jobCount(Request $request)
    {
        $user = $request->user();

        $artist = $user->artist;

        return [
            'data' => [
                'artist_unread_change_requests' => $artist ? $this->unreadChangeRequestsCount($artist) : 0,
                'user_unread_files' => $this->userUnreadFiles($user),
            ],
        ];
    }

    private function unreadChangeRequestsCount(Artist $artist)
    {
        return $artist->workTaskFiles()
            ->join('work_task_file_change_requests', 'work_task_files.id', '=', 'work_task_file_change_requests.work_task_file_id')
            ->where('work_task_file_change_requests.artist_is_read', false)
            ->count();
    }

    private function userUnreadFiles(User $user)
    {
        return $user->workTaskFiles()
            ->where('user_is_read', false)
            ->count();
    }
}
