<?php

namespace App\Http\Controllers\Api\User;

use App\Enums\ErrorCode;
use App\Enums\OrderStatus;
use App\Enums\UserWithdrawAccountStatus;
use App\Enums\UserWithdrawAccountType;
use App\Enums\UserWithdrawStripeServiceAgreement;
use App\Enums\WalletTransactionAction;
use App\Enums\WalletTransactionBizType;
use App\Enums\WalletTransactionStatus;
use App\Enums\WalletUsage;
use App\Enums\WorkTaskPriceChangeStatus;
use App\Http\Controllers\Controller;
use App\Models\Currency;
use App\Models\Order;
use App\Models\StripeCheckoutSession;
use App\Models\Wallet;
use App\Models\WorkTaskStage;
use App\Service\OrderService;
use App\Service\RateService;
use App\Service\Stripe\StripeCalcAmount;
use Auth;
use DB;
use Illuminate\Http\Request;

class WorkTaskPayController extends Controller
{
    public function preCalc(Request $request)
    {
        $request->validate([
            'work_task_id' => 'required|integer|exists:work_tasks,id',
            'type' => 'required|in:stage_pay,full_pay,price_change',
            'wallets' => 'array',
            'wallets.*' => 'integer|exists:wallets,id',
        ]);

        $work_task_id = $request->work_task_id;
        $user = Auth::user();
        $type = $request->type;

        $work_task = $user->workTasks()->find($work_task_id);
        if (! $work_task) {
            abort(400, 'Work task not found');
        }

        $work_task_stages = [];
        $priceChange = $work_task->priceChanges()
            ->where('status', WorkTaskPriceChangeStatus::WAIT_PAY)
            ->first();

        if ($type == 'price_change') {
            if (! $priceChange) {
                abort(400, 'No price change');
            }
        }

        if ($type == 'stage_pay' || $type == 'full_pay') {
            if ($priceChange) {
                abort(400, 'Has pending price change');
            }

            $work_task_stages = $work_task->workTaskStages()
                ->where('is_paid', WorkTaskStage::PayStatusUnpaid)
                ->orderBy('percent', 'asc')
                ->get();
            if ($work_task_stages->isEmpty()) {
                abort(400, 'No unpaid stage found');
            }
        }

        $wallets = [];
        if ($request->wallets) {
            $wallets = $user->wallets()
                ->where('usage', WalletUsage::Credit)
                ->whereIn('id', $request->wallets)
                ->orderByRaw('FIELD(id, ' . implode(',', $request->wallets) . ')')
                ->get();
        }

        $stripeCalcAmount = new StripeCalcAmount;
        $stripeCalcAmount->InitFromWorkTaskStage($work_task, $type, $priceChange);
        $stripeCalcAmount->CnyCurrencyConversion();
        $stripeCalcAmount->WalletBalanceDeduction($wallets);
        $stripeCalcAmount->ZeroDecimalCurrencyCeil();

        $process = $stripeCalcAmount->getProcess();
        $initAmount = $stripeCalcAmount->getInitAmount();
        $retAmount = $stripeCalcAmount->getRetAmount();

        return [
            'data' => [
                'before_amount' => $initAmount,
                'after_amount' => $retAmount,
                'process' => $process,
            ],
        ];
    }

    public function createWorkTaskCheckoutSession(Request $request)
    {
        $request->validate([
            'work_task_id' => 'required|integer|exists:work_tasks,id',
            'type' => 'required|in:stage_pay,full_pay,price_change',
            'wallets' => 'array',
            'wallets.*' => 'integer|exists:wallets,id',
        ]);

        $work_task_id = $request->work_task_id;
        $user = Auth::user();
        $type = $request->type;

        $work_task = $user->workTasks()->find($work_task_id);
        if (! $work_task) {
            abort(400, 'Work task not found');
        }

        $artist = $work_task->artist;

        // 提取支付类型验证逻辑
        $this->validatePaymentType($work_task, $type);

        // 检查Stripe账户连接情况
        $stripeAccount = $work_task->currency->code !== 'CNY'
            ? $this->getStripeAccount($artist)
            : null;

        // 取消现有支付中的订单
        $this->cancelExistingOrders($work_task);

        DB::beginTransaction();

        try {
            // 获取并锁定钱包
            $wallets = $this->getLockedWallets($user, $request->wallets);

            // 计算支付金额
            $stripeCalcAmount = $this->calculatePaymentAmount($work_task, $type, $wallets);
            $process = $stripeCalcAmount->getProcess();
            $retAmount = $stripeCalcAmount->getRetAmount();
            $initAmount = $stripeCalcAmount->getInitAmount();

            // 创建Stripe支付会话
            $checkout_session = $this->createStripeCheckoutSession(
                $work_task,
                $retAmount,
                $user,
                $stripeAccount
            );

            // 创建订单记录
            $order = $this->createOrderRecord(
                $user,
                $work_task,
                $type,
                $checkout_session,
                $initAmount,
                $retAmount,
                $stripeCalcAmount
            );

            // 处理钱包扣减
            $this->processWalletDeductions($process, $user);

            DB::commit();

            return ['data' => [
                'checkout_session_id' => $checkout_session->id,
                'client_secret' => $checkout_session->client_secret,
                'amount' => $retAmount->amount,
                'currency' => $work_task->currency,
            ]];
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    private function validatePaymentType($work_task, $type)
    {
        $priceChange = $work_task->priceChanges()
            ->where('status', WorkTaskPriceChangeStatus::WAIT_PAY)
            ->first();

        if ($type === 'price_change') {
            if (! $priceChange) {
                abort(400, 'No price change');
            }
            return;
        }

        if (in_array($type, ['stage_pay', 'full_pay'])) {
            if ($priceChange) {
                abort(400, 'Has pending price change');
            }

            $unpaidStages = $work_task->workTaskStages()
                ->where('is_paid', WorkTaskStage::PayStatusUnpaid)
                ->orderBy('percent', 'asc')
                ->get();

            if ($unpaidStages->isEmpty()) {
                return response()->json([
                    'code' => ErrorCode::WorkTaskNoUnpaidStage->value,
                    'message' => 'No unpaid stage found',
                ], 400);
            }
        }
    }

    private function getStripeAccount($artist)
    {
        $stripeAccount = $artist->user->withdrawAccounts()
            ->where('bind_status', UserWithdrawAccountStatus::Successed)
            ->where('type', UserWithdrawAccountType::Stripe)
            ->where('stripe_service_agreement', UserWithdrawStripeServiceAgreement::Full)
            ->first();

        if (! $stripeAccount) {
            abort(400, 'No Stripe connected account');
        }

        return $stripeAccount;
    }

    private function cancelExistingOrders($work_task)
    {
        $existingOrder = $work_task->orders()
            ->where('status', OrderStatus::Paying)
            ->first();

        if ($existingOrder) {
            OrderService::cancelOrder($existingOrder);
        }
    }

    private function getLockedWallets($user, $walletIds)
    {
        if (empty($walletIds)) {
            return collect();
        }

        return $user->wallets()
            ->where('usage', WalletUsage::Credit)
            ->whereIn('id', $walletIds)
            ->lockForUpdate()
            ->orderByRaw('FIELD(id, ' . implode(',', $walletIds) . ')')
            ->get();
    }

    private function calculatePaymentAmount($work_task, $type, $wallets)
    {
        $stripeCalcAmount = new StripeCalcAmount;
        $stripeCalcAmount->InitFromWorkTaskStage(
            $work_task,
            $type,
            $work_task->priceChanges()->where('status', WorkTaskPriceChangeStatus::WAIT_PAY)->first()
        );
        $stripeCalcAmount->CnyCurrencyConversion();
        $stripeCalcAmount->WalletBalanceDeduction($wallets);
        $stripeCalcAmount->ZeroDecimalCurrencyCeil();

        return $stripeCalcAmount;
    }

    private function createStripeCheckoutSession($work_task, $retAmount, $user, $stripeAccount = null)
    {
        $stripe = new \Stripe\StripeClient(config('stripe.secret'));
        $unit_amount = $retAmount->amount;
        
        if ($retAmount->currency->is_zero_decimal) {
            $unit_amount = $unit_amount / 100;
        }

        $params = [
            'line_items' => [[
                'price_data' => [
                    'currency' => $retAmount->currency->code,
                    'product_data' => [
                        'name' => "Work Task Payment: #{$work_task->id}",
                    ],
                    'unit_amount' => $unit_amount,
                ],
                'quantity' => 1,
            ]],
            'mode' => 'payment',
            'ui_mode' => 'embedded',
            'redirect_on_completion' => 'never',
            'customer_email' => $user->email,
            'metadata' => [
                'work_task_id' => $work_task->id,
                'artist_id' => $work_task->artist->id,
            ],
        ];

        $opts = [];

        if ($stripeAccount) {
            // TODO calc fee
            $params = array_merge($params, [
                'payment_intent_data' => ['application_fee_amount' => 123]
            ]);
            $opts = [
                'stripe_account' => $stripeAccount->stripe_connect_account_id,
            ];
        }

        return $stripe->checkout->sessions->create($params, $opts);
    }

    private function createOrderRecord($user, $work_task, $type, $checkout_session, $initAmount, $retAmount, $stripeCalcAmount)
    {
        $priceChange = $work_task->priceChanges()
            ->where('status', WorkTaskPriceChangeStatus::WAIT_PAY)
            ->first();

        return Order::create([
            'user_id' => $user->id,
            'artist_id' => $work_task->artist->id,
            'busable_type' => 'work_task',
            'busable_id' => $work_task->id,
            'status' => OrderStatus::Paying,
            'busable_info' => [
                'pay_type' => $type,
                'work_task_id' => $work_task->id,
                'price_change_id' => $priceChange ? $priceChange->id : null,
            ],
            'init_amount' => $initAmount->amount,
            'init_currency_id' => $initAmount->currency->id,
            'amount' => $retAmount->amount,
            'currency_id' => $retAmount->currency->id,
            'amount_calc_process' => $stripeCalcAmount->toArray(),
        ]);
    }

    private function processWalletDeductions($process, $user)
    {
        foreach ($process as $processStage) {
            if ($processStage->type !== 'wallet_balance_deduction') {
                continue;
            }

            Wallet::where('id', $processStage->wallet_id)->update([
                'balance' => $processStage->wallet_balance_after->amount,
            ]);

            $user->walletTransactions()->create([
                'wallet_id' => $processStage->wallet_id,
                'currency_id' => $processStage->wallet_balance->currency->id,
                'action' => WalletTransactionAction::Minus,
                'amount' => $processStage->wallet_minus->amount,
                'before_balance' => $processStage->wallet_balance->amount,
                'after_balance' => $processStage->wallet_balance_after->amount,
                'biz_type' => WalletTransactionBizType::WorkTask,
                'status' => WalletTransactionStatus::Successed,
            ]);

            //TODO 记录抵扣记录，用于补差
        }
    }

    private function calcAmount($type, $work_task, $priceChange = null)
    {
        $amount = 0;
        $paidAmount = $work_task->paid_amount;

        if ($type == 'full_pay') {
            $amount = $work_task->price - $paidAmount;
        }
        if ($type == 'stage_pay') {
            $work_task_stages = $work_task->workTaskStages;

            foreach ($work_task_stages as $stage) {
                if ($stage->is_paid && $stage->work_status == WorkTaskStage::WorkStatusPending) {
                    return 0;
                }
            }

            $tmp = 0;
            foreach ($work_task_stages as $stage) {
                if (! $stage->is_paid) {
                    $tmp += $stage->amount;
                    break;
                }
                $tmp += $stage->amount;
            }

            $amount = $tmp - $paidAmount;
        }
        if ($type == 'price_change') {
            $amount = $priceChange->need_pay_amount;
        }

        return $amount;
    }

    private function calcAmountByCurrencyConversion($amount, $target_currency)
    {
        // cny to usd
        if ($target_currency->code === 'CNY') {
            $amount = RateService::new()->amountExchange($amount, 'CNY', 'USD', 'ceil');
            $amount = (int) round($amount);

            $target_currency = Currency::query()->where('code', 'USD')->first();
        }
    }

    private function calcAmountByWallets($amount, $wallets, $target_currency)
    {
        $process = [];

        $amount_before = $amount;
        $amount_after = $amount;
        foreach ($wallets as $wallet) {
            if ($amount_after <= 0) {
                continue;
            }
            $wallet_balance = $wallet->balance;
            $wallet_balance_code = $wallet->currency->code;
            if ($wallet_balance <= 0) {
                continue;
            }

            $wallet_target_currency_balance = RateService::new()->amountExchange($wallet_balance, $wallet_balance_code, $target_currency->code, 'floor');

            if ($amount_before - $wallet_target_currency_balance > 0) {
                $amount_minus = $wallet_target_currency_balance;
                $amount_after = $amount_before - $amount_minus;
                $wallet_minus = $wallet_balance;
                $wallet_balance_after = 0;
            } else {
                $amount_minus = $amount_before;
                $amount_after = 0;
                $wallet_minus = RateService::new()->amountExchange($amount_minus, $target_currency->code, $wallet_balance_code, 'ceil');
                $wallet_balance_after = $wallet_balance - $wallet_minus;
            }

            $process[] = [
                'wallet_id' => $wallet->id,
                'wallet_currency_id' => $wallet->currency_id,
                'wallet_balance' => $wallet_balance,
                'wallet_balance_code' => $wallet_balance_code,
                'wallet_target_currency_balance' => $wallet_target_currency_balance,
                'wallet_target_currency_balance_code' => $target_currency->code,
                'wallet_minus' => $wallet_minus,
                'wallet_minus_code' => $wallet_balance_code,
                'wallet_balance_after' => $wallet_balance_after,
                'wallet_balance_after_code' => $wallet_balance_code,
                'amount_before' => $amount_before,
                'amount_before_code' => $target_currency->code,
                'amount_minus' => $amount_minus,
                'amount_minus_code' => $target_currency->code,
                'amount_after' => $amount_after,
                'amount_after_code' => $target_currency->code,
                'type' => 'wallet_balance',
            ];

            $amount_before = $amount_after;
        }

        return [
            'process' => $process,
            'amount_before' => $amount,
            'amount_after' => $amount_after,
        ];
    }
}
