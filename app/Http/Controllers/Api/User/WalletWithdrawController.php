<?php

namespace App\Http\Controllers\Api\User;

use App\Enums\WalletTransactionAction;
use App\Enums\WalletTransactionBizType;
use App\Enums\WalletTransactionStatus;
use App\Enums\WalletUsage;
use App\Enums\WalletWithdrewStatus;
use App\Http\Controllers\Controller;
use Auth;
use DB;
use Hash;
use Illuminate\Http\Request;

class WalletWithdrawController extends Controller
{
    public function list(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'status' => 'nullable|string|in:pending,cancelled,accepted,rejected',
            'wallet_id' => 'nullable|integer|exists:wallets,id',
            'page' => 'nullable|integer|min:1',
            'size' => 'nullable|integer|min:1|max:100',
        ]);

        $status = $request->input('status');
        $size = $request->input('size', 10);
        $wallet_id = $request->input('wallet_id');

        $withdraws = $user->walletWithdraws()
            ->with([
                'wallet',
                'withdrawAccount',
            ])
            ->when($wallet_id, function ($query) use ($wallet_id) {
                return $query->where('wallet_id', $wallet_id);
            })
            ->when($status, function ($query) use ($status) {
                return $query->where('status', $status);
            })
            ->orderBy('created_at', 'desc')
            ->paginate($size);

        return [
            'total' => $withdraws->total(),
            'data' => $withdraws->items(),
        ];
    }

    public function create(Request $request)
    {
        $request->validate([
            'wallet_id' => 'required|integer',
            'amount' => 'required|integer',
            'withdraw_account_id' => 'required|integer',
            'password' => 'required|string',
        ]);

        $user = Auth::user();

        // check password
        if (! Hash::check($request->password, $user->password)) {
            abort(400, 'Password error');
        }

        $wallet = $user->wallets()
            ->where('id', $request->wallet_id)
            ->with([
                'currency',
            ])
            ->first();

        if (! $wallet) {
            abort(404, 'Wallet not found');
        }

        if ($wallet->balance - $request->amount < 0) {
            abort(400, 'Insufficient balance');
        }

        if ($wallet->usage !== WalletUsage::Deposit) {
            abort(400, 'Wallet usage error');
        }

        $walletWithdrawAccount = $user->withdrawAccounts()
            ->where('id', $request->withdraw_account_id)
            ->with([
                'user:id,name,email',
                'currency',
            ])
            ->first();

        if (! $walletWithdrawAccount) {
            abort(404, 'Withdraw account not found');
        }

        DB::beginTransaction();

        $beforeBalance = $wallet->balance;
        $afterBalance = $beforeBalance - $request->amount;

        $withdraw = $user->walletWithdraws()->create([
            'wallet_id' => $wallet->id,
            'wallet_snapshot' => $wallet,
            'withdraw_account_id' => $walletWithdrawAccount->id,
            'withdraw_account_snapshot' => $walletWithdrawAccount,
            'status' => WalletWithdrewStatus::Pending,
            'amount' => $request->amount,
            'note' => $request->note ?? '',
        ]);

        $wallet->balance = $afterBalance;
        $wallet->save();

        $user->walletTransactions()->create([
            'user_id' => $user->id,
            'wallet_id' => $wallet->id,
            'currency_id' => $wallet->currency_id,
            'action' => WalletTransactionAction::Minus,
            'biz_type' => WalletTransactionBizType::Withdraw,
            'status' => WalletTransactionStatus::Pending,
            'amount' => $request->amount,
            'before_balance' => $beforeBalance,
            'after_balance' => $afterBalance,
            'withdraw_id' => $withdraw->id,
        ]);

        DB::commit();

        return [
            'ok' => true,
            'data' => $withdraw,
        ];
    }

    public function cancel(Request $request)
    {
        $request->validate([
            'id' => 'required|integer',
        ]);

        $user = Auth::user();

        $withdraw = $user->walletWithdraws()
            ->where('id', $request->id)
            ->where('status', WalletWithdrewStatus::Pending)
            ->first();

        if (! $withdraw) {
            abort(404, 'Withdrew not found');
        }

        DB::beginTransaction();

        $withdraw->status = WalletWithdrewStatus::Cancelled;
        $withdraw->save();

        $wallet = $withdraw->wallet;
        $wallet->balance += $withdraw->amount;
        $wallet->save();

        DB::commit();

        return [
            'ok' => true,
            'data' => $withdraw,
        ];
    }
}
