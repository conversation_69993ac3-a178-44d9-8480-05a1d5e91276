<?php

namespace App\Http\Controllers\Api\User;

use App\Enums\SystemNotificationScene;
use App\Http\Controllers\Controller;
use App\Jobs\TranslateJob;
use App\Models\Service;
use App\Models\ServiceRequest;
use App\Models\ServiceRequestFile;
use App\Notifications\SystemNotification;
use App\Service\Translate\TranslateDB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ServiceRequestController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function list(Request $request)
    {

        $request->validate([
            'page' => 'integer|min:1',
            'size' => 'integer|min:1,max:50',
        ]);

        $page = $request->input('page', 1);
        $size = $request->input('size', 15);

        $user = Auth::user();
        $ret = $user->serviceRequests()
            ->where('is_user_deleted', false)
            ->with([
                'service:id,name,currency_id' => [
                    'currency',
                ],
                'artist:id,name,avatar_id' => [
                    'avatar',
                ],
                'currency',
            ])
            ->orderByDesc('id')
            ->paginate($size);

        return [
            'data' => $ret->items(),
            'total' => $ret->total(),
        ];
    }

    public function info(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|exists:service_requests,id',
        ]);

        $user = Auth::user();
        $ret = $user->serviceRequests()->with([
            'serviceRequestFiles' => [
                'uploadImage',
            ],
            'service:id,name,currency_id' => [
                'currency',
            ],
            'artist:id,name,avatar_id' => [
                'avatar',
            ],
            'currency',
            'workTask' => [
                'workTaskStages',
                'payingOrder:id,status,busable_id,busable_type',
            ],
        ])->find($request->id);

        if (! $ret->user_is_read) {
            $ret->user_is_read = true;
            $ret->save();
        }

        return [
            'data' => $ret,
        ];
    }

    /**
     * Store a newly created resource in storage.
     */
    public function create(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'service_id' => 'required|integer|exists:services,id',
            'deadline' => 'required',
            'detail' => 'required',
            'budget' => 'required',
            'currency_id' => 'required|integer|exists:currencies,id',

            'files' => 'array',
            'files.*.name' => 'required|string',
            'files.*.type' => 'required|string|in:upload_image',
            'files.*.upload_image_id' => "integer|exists:upload_images,id,scene,service_requests,user_id,{$user->id}",
        ]);

        $service = Service::find($request->service_id);

        DB::beginTransaction();

        $sq = new ServiceRequest;
        $sq->fill([
            'user_id' => $user->id,
            'service_id' => $service->id,
            'artist_id' => $service->artist_id,
            'budget' => $request->get('budget'),
            'currency_id' => $request->get('currency_id'),
            'deadline' => $request->get('deadline'),
            'status' => ServiceRequest::StatusPending,
        ]);
        $sq->setTransByReq('detail', $request, true);
        $sq->save();

        foreach ($request->get('files') as $value) {
            $file = [
                'service_request_id' => $sq->id,
                'user_id' => $user->id,
                'service_id' => $request->service_id,
                'artist_id' => $service->artist_id,
                'file_name' => $value['name'] ?? null,
                'type' => $value['type'],
                'upload_image_id' => $value['upload_image_id'] ?? null,
            ];
            ServiceRequestFile::create($file);
        }

        DB::commit();

        $notify = SystemNotification::make()
            ->setScene(SystemNotificationScene::ServiceRequestCreated)
            ->setMeta([
                'service_request_id' => $sq->id,
            ]);
        $artist = $service->artist;
        $artist->notify($notify);

        // 翻译服务请求详情
        $this->translateServiceRequestDetail(false, $sq);

        $sq->refresh();

        return ['data' => $sq];
    }

    private function translateServiceRequestDetail($isAsync, $sq)
    {
        $source_lang = $sq->getTranslation('detail', '_lang', false);
        if (! $source_lang) {
            $source_lang = 'en';
        }

        $target_langs = [];
        $target_langs[] = $sq->artist->user->language->code ?? 'en';

        $translates = [];
        foreach ($target_langs as $target_lang) {
            if ($source_lang === $target_lang) {
                continue;
            }
            $translates[] = TranslateDB::createTranslateByModel($sq, 'detail', $source_lang, $target_lang, $sq->user_id);
        }
        $translate_ids = collect($translates)->pluck('id');

        if ($isAsync) {
            TranslateJob::dispatch($translate_ids->toArray())->onQueue('translate');
        } else {
            $job = new TranslateJob($translate_ids->toArray());
            $job->handle();
        }

    }

    /**
     * Display the specified resource.
     */
    public function cancel(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'id' => 'required|integer|exists:service_requests,id',
        ]);

        $sq = $user->serviceRequests()->where('id', $request->id)->first();
        if (! $sq) {
            return response()->json('not found', 404);
        }

        $canUpdateStatus = [ServiceRequest::StatusPending];
        if (! in_array($sq->status, $canUpdateStatus)) {
            return response()->json('can not cancel', 400);
        }

        $sq->status = ServiceRequest::StatusUserCanceled;
        $sq->save();

        $notify = SystemNotification::make()
            ->setScene(SystemNotificationScene::ServiceRequestUserCanceled)
            ->setMeta([
                'service_request_id' => $sq->id,
            ]);

        $artist = $sq->artist;
        $artist->notify($notify);

        return ['ok' => true];
    }

    public function delete(Request $request)
    {
        $user = Auth::user();
        $request->validate([
            'id' => 'required|integer|exists:service_requests,id',
        ]);
        $sq = $user->serviceRequests()->where('id', $request->id)->first();
        if (! $sq) {
            return response()->json('not found', 404);
        }

        $canUpdateStatus = [ServiceRequest::StatusArtistRejected, ServiceRequest::StatusUserCanceled];
        if (! in_array($sq->status, $canUpdateStatus)) {
            return response()->json('can not delete', 400);
        }

        $sq->is_user_deleted = true;
        $sq->save();

        return ['ok' => true, 'data' => $sq];
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ServiceRequest $serviceRequest)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ServiceRequest $serviceRequest)
    {
        //
    }
}
