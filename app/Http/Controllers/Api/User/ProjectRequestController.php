<?php

namespace App\Http\Controllers\Api\User;

use App\Enums\SystemNotificationScene;
use App\Events\Echo\GroupCreated;
use App\Http\Controllers\Controller;
use App\Models\Group;
use App\Models\ProjectRequest;
use App\Models\WorkTask;
use App\Models\WorkTaskStage;
use App\Notifications\SystemNotification;
use App\Service\ArtistService;
use App\Service\GroupService;
use App\Service\PaymentService;
use DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ProjectRequestController extends Controller
{
    public function chooseAndCreateWorkTask(Request $request)
    {
        $request->validate([
            'project_request_id' => 'required|integer|min:1|exists:project_requests,id',
            'deadline' => 'required',
        ]);

        $project_request_id = $request->project_request_id;
        $deadline = $request->deadline;

        $user = Auth::user();
        $pr = $user->projectRequests()->find($project_request_id);
        if (! $pr) {
            return response()->json(['message' => 'Not found.'], 404);
        }

        if (! in_array($pr->status, [ProjectRequest::StatusPending, ProjectRequest::StatusUserChosen])) {
            return response()->json(['message' => 'Can not choose.'], 400);
        }

        DB::beginTransaction();

        if ($pr->status === ProjectRequest::StatusPending) {
            $pr->status = ProjectRequest::StatusUserChosen;
            $pr->save();
        }

        $project = $pr->project;

        // create work_task
        $workTask = new WorkTask();
        $workTask->reqable()->associate($pr);
        $workTask->busable()->associate($project);
        $workTask->fill([
            'artist_id' => $pr->artist_id,
            'user_id' => $pr->user_id,
            'status' => WorkTask::StatusPending,
            'price' => $pr->budget,
            'currency_id' => $pr->currency_id,
            'deadline' => $deadline,
            'busable_snap' => $pr->project,
            'reqable_snap' => $pr,
        ]);
        $workTask->setTransByReq('name', $request, true);
        $workTask->save();

        $projectStages = $project->stages()->orderByPivot('percent')->get();

        $ps = new PaymentService();
        $stage_amounts = $ps->calcStageAmounts($projectStages, $pr->budget);

        foreach ($stage_amounts as $stage) {
            $workTask->workTaskStages()->create([
                'type' => WorkTaskStage::TypeProject,
                'project_id' => $project->id,
                'name' => $stage->name,
                'percent' => $stage->percent,
                'is_paid' => WorkTaskStage::PayStatusUnpaid,
                'work_status' => WorkTaskStage::WorkStatusPending,
                'amount' => $stage->amount,
            ]);
        }

        // create group chat
        $gs = new GroupService();
        $group = $gs->createGroupByWorkTask($workTask);
        GroupCreated::dispatch($group);

        DB::commit();

        // notify
        $notify = SystemNotification::make(SystemNotificationScene::ProjectRequestUserChoosen)
            ->setMeta([
                'work_task_id' => $workTask->id,
            ]);
        $workTask->artist->notify($notify);

        return ['ok' => true];
    }

    public function requestedList(Request $request)
    {
        $request->validate([
            'project_id' => 'required|integer|exists:projects,id',
            'page' => 'integer|min:1',
            'size' => 'integer|min:1,max:50',
        ]);

        $page = $request->input('page', 1);
        $size = $request->input('size', 15);

        $user = Auth::user();
        $project = $user->projects()->find($request->project_id);
        if (! $project) {
            return response()->json(['message' => 'project not found'], 404);
        }

        $query = $project->projectRequests();

        $ret = $query
            ->with([
                'currency',
                'artist:id,name,avatar_id' => [
                    'avatar',
                ],
                'artworks' => function ($query) {
                    $query
                        ->with([
                            'uploadImage',
                            'uploadVideo',
                        ])
                        ->take(4);
                },
                'workTasks' => [
                    'currency',
                ],
            ])
            ->paginate($size);

        $ret->through(function ($pr) {
            $pr->artist->rating_score = ArtistService::getRatingScore($pr->artist);
            $pr->can_cancel = $this->calcCancCancel($pr->workTasks);

            return $pr;
        });

        return [
            'data' => $ret->items(),
            'total' => $ret->total(),
        ];
    }

    private function calcCancCancel($workTasks): bool
    {
        if ($workTasks->isEmpty()) {
            return true;
        }

        $count = $workTasks
            ->filter(fn ($workTask) => $workTask->status == WorkTask::StatusWorking)
            ->count();

        return $count == 0;
    }

    public function choosedList(Request $request)
    {
        $request->validate([
            'project_id' => 'required|integer|exists:projects,id',
            'page' => 'integer|min:1',
            'size' => 'integer|min:1,max:50',
        ]);

        $page = $request->input('page', 1);
        $size = $request->input('size', 15);

        $user = Auth::user();
        $project = $user->projects()->find($request->project_id);
        if (! $project) {
            return response()->json(['message' => 'project not found'], 404);
        }

        $query = $project->projectRequests();
        $query->where('status', ProjectRequest::StatusUserChosen);
        $query->has('workTasks');

        $ret = $query
            ->with([
                'currency',
                'artist:id,name,avatar_id' => [
                    'avatar',
                ],
                'artworks' => function ($query) {
                    $query
                        ->with([
                            'uploadImage',
                            'uploadVideo',
                        ])
                        ->take(4);
                },
                'workTasks' => [
                    'currency',
                    'workTaskStages',
                    'payingOrder:id,status,busable_id,busable_type',
                ],
            ])
            ->paginate($size);

        $ret->through(function ($pr) {
            $pr->artist->rating_score = ArtistService::getRatingScore($pr->artist);

            return $pr;
        });

        return [
            'data' => $ret->items(),
            'total' => $ret->total(),
        ];
    }
}
