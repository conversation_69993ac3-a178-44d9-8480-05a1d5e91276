<?php

namespace App\Http\Controllers\Api\User;

use App\Enums\ChatMessageContentType;
use App\Enums\ErrorCode;
use App\Events\ChatMessageSentEvent;
use App\Events\Echo\ChatMessageCreated;
use App\Http\Controllers\Controller;
use App\Models\Chat;
use App\Models\ChatMessage;
use App\Models\ChatUserPivot;
use App\Models\UploadFile;
use App\Service\Translate\TranslateDB;
use App\Service\Translate\TranslationManager;
use App\Service\UploadFileService;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class ChatMessageController extends Controller
{
    public function list(Request $request)
    {
        $request->validate([
            'chat_id' => [
                'required',
                'integer',
                Rule::exists('chat_user_pivot', 'chat_id')->where('user_id', auth()->id()),
            ],
            'ids' => 'required|array',
            'ids.*' => 'integer|exists:chat_messages,id',
        ]);

        $ids = $request->input('ids');

        $messages = ChatMessage::where('chat_id', $request->input('chat_id'))
            ->whereIn('id', $ids)
            ->with(['translate', 'attachments', 'replyMessage'])
            ->get();

        return [
            'data' => $messages,
        ];
    }

    public function enterChat(Request $request)
    {
        $request->validate([
            'chat_id' => [
                'required',
                'integer',
                Rule::exists('chat_user_pivot', 'chat_id')->where('user_id', auth()->id()),
            ],
            'size' => 'integer|min:1|max:50',
        ]);

        $size = $request->input('size', 20);

        $chatUserPivot = ChatUserPivot::query()
            ->where('chat_id', $request->input('chat_id'))
            ->where('user_id', auth()->id())
            ->first();

        $last_id = $chatUserPivot?->last_read_cursor;

        if (! $last_id) {
            $last_id = 1;
            $chatUserPivot->update([
                'last_read_cursor' => $last_id,
            ]);
        }

        $query = ChatMessage::query();
        $query->where('chat_id', $request->input('chat_id'));
        $query->with(['translate', 'attachments', 'replyMessage']);

        $nextQuery = $query->clone();
        $prevQuery = $query->clone();
        $currentQuery = $query->clone();

        $nextData = $nextQuery->where('id', '>', $last_id)->orderBy('id')->limit($size)->get();
        $nextData = $nextData->reverse()->values();
        $prevData = $prevQuery->where('id', '<', $last_id)->orderByDesc('id')->limit($size)->get();
        $currentData = $currentQuery->find($last_id);

        if ($nextData->isNotEmpty()) {
            $chatUserPivot->update([
                'last_read_cursor' => $nextData->max('id'),
            ]);
        }

        return [
            'next' => $nextData,
            'prev' => $prevData,
            'current' => $currentData,
        ];
    }

    public function historyMessages(Request $request)
    {
        $request->validate([
            'chat_id' => [
                'required',
                'integer',
                Rule::exists('chat_user_pivot', 'chat_id')->where('user_id', auth()->id()),
            ],
            'cursor' => 'nullable|string',
            'last_id' => 'integer',
            'size' => 'integer|min:1|max:50',
        ]);

        $size = $request->input('size', 20);

        $query = ChatMessage::query();
        $query->where('chat_id', $request->input('chat_id'));
        if ($request->input('last_id')) {
            $query->where('id', '<', $request->input('last_id'));
        }
        $query->with(['translate', 'attachments', 'replyMessage']);
        $query->orderByDesc('id');

        $messages = $query->limit($size)->get();

        return [
            'data' => $messages,
            'next_last_id' => $messages->min('id'),
        ];
    }

    public function newMessages(Request $request)
    {
        $request->validate([
            'chat_id' => [
                'required',
                'integer',
                Rule::exists('chat_user_pivot', 'chat_id')->where('user_id', auth()->id()),
            ],
            'cursor' => 'nullable|string',
            'last_id' => 'nullable|integer',
            'size' => 'integer|min:1|max:50',
        ]);

        $size = $request->input('size', 20);

        $query = ChatMessage::query();
        $query->where('chat_id', $request->input('chat_id'));
        if ($request->input('last_id')) {
            $query->where('id', '>', $request->input('last_id'));
        }
        $query->with(['translate', 'attachments', 'replyMessage']);
        $query->orderBy('id');

        $messages = $query->limit($size)->get();

        $nextLastId = $messages->max('id');
        if ($nextLastId) {
            ChatUserPivot::where('chat_id', $request->input('chat_id'))
                ->where('user_id', auth()->id())
                ->update(['last_read_cursor' => $nextLastId]);
        }

        return [
            'data' => $messages->reverse()->values(),
            'next_last_id' => $nextLastId,
        ];
    }

    public function send(Request $request)
    {
        $user = auth()->user();

        $request->validate([
            'chat_id' => [
                'required',
                'integer',
                Rule::exists('chat_user_pivot', 'chat_id')->where('user_id', auth()->id()),
            ],
            'content' => '',
            'translate_lang' => 'nullable|string',
            'attachments' => 'nullable|array',
            'attachments.*' => 'integer|exists:upload_files,id',
            'reply_message_id' => 'nullable|integer|exists:chat_messages,id',
        ]);

        $chatId = $request->input('chat_id');
        $replyMsgId = $request->input('reply_message_id');
        $cId = $request->input('c_id');

        $chat = Chat::find($chatId);
        $chatUserIds = explode('_', $chat->key);
        $chatUserIds = collect($chatUserIds)->unique();
        $receiverUserId = $chatUserIds->first(fn ($userId) => $userId != $user->id);

        if ($receiverUserId) {
            $userMessageCount = ChatUserPivot::where('chat_id', $chatId)
                ->where('user_id', $user->id)
                ->value('send_message_count');

            $receiverMessageCount = ChatUserPivot::where('chat_id', $chatId)
                ->where('user_id', $receiverUserId)
                ->value('send_message_count');

            if ($userMessageCount === 1 && $receiverMessageCount === 0) {
                return response()->json([
                    'code' => ErrorCode::ChatMessageNeedReceiverReturnMessage->value,
                    'message' => 'You need to wait for the other party to reply before sending another message.',
                ], 400);
            }
        }

        $message = \DB::transaction(function () use ($user, $request, $chatId, $replyMsgId, $cId) {
            $chat = Chat::lockForUpdate()->findOrFail($chatId);

            $messageCount = $chat->message_count;
            $messageCount += 1;

            $message = new ChatMessage;
            $message->chat_id = $chatId;
            $message->sender_uid = $user->id;
            $message->content_type = ChatMessageContentType::Text;
            $message->setTransByReq('content', $request, true);
            $message->seq_id = $messageCount;
            $message->translate_id = null;
            $message->reply_message_id = $replyMsgId;
            $message->c_id = $cId;
            $message->save();

            ChatUserPivot::where('chat_id', $chatId)
                ->where('user_id', $user->id)
                ->update([
                    'send_message_count' => \DB::raw('send_message_count + 1'),
                    'order_at_ts' => now()->getTimestampMs(),
                ]);

            $chat->message_count = $messageCount;
            $chat->save();

            $event = new ChatMessageSentEvent($message, $request->all(), $user->id);
            $isAsync = false;
            if ($isAsync) {
                event($event);
            } else {
                $event->triggerChatUserPivot();
                $event->updateChatUserPivotVisableStatus();
                $event->updateLastReadCursor();
                $event->updateAttachments();
                $event->translateMessage(isAsync: false);
            }

            $message->refresh();
            $message->load(['translate', 'attachments', 'replyMessage']);

            ChatMessageCreated::dispatch($message);

            return $message;
        });

        return [
            'data' => $message,
        ];
    }

    public function downloadAttachment(Request $request)
    {
        $request->validate([
            'attachment_id' => 'required|integer|exists:upload_files,id',
        ]);

        $attachment = UploadFile::find($request->input('attachment_id'));
        if (! $attachment) {
            return abort(404, 'attachment not found');
        }

        $temporaryUrl = UploadFileService::downloadTemporaryUrl($attachment);

        return [
            'temporary_url' => $temporaryUrl,
        ];
    }

    public function translateMessage(Request $request)
    {
        $user = auth()->user();

        $request->validate([
            'chat_id' => [
                'required',
                'integer',
                Rule::exists('chat_user_pivot', 'chat_id')->where('user_id', auth()->id()),
            ],
            'message_id' => 'required|integer|exists:chat_messages,id',
            'target_langs' => 'required|array',
            'target_langs.*' => 'string',
        ]);

        $messageId = $request->input('message_id');

        $message = ChatMessage::find($messageId);

        $contentRaw = $message->getRawOriginal('content');
        $content = json_decode($contentRaw, true);
        $source_lang = $content['_lang'] ?? 'en';

        $target_langs = $request->input('target_langs');

        $translates = TranslateDB::createTranslatesForMultipleTargetLangs($message, 'content', $source_lang, $target_langs, $user->id);
        $translateIds = collect($translates)->pluck('id');
        TranslationManager::translateColumnByIds($translateIds);

        $message->translate_id = collect($translateIds)->last();
        $message->save();

        $message->refresh();
        $message->load(['translate', 'attachments', 'replyMessage']);

        return [
            'data' => $message,
        ];
    }

    public function undo(Request $request)
    {
        $request->validate([
            'chat_id' => [
                'required',
                'integer',
                Rule::exists('chat_user_pivot', 'chat_id')->where('user_id', auth()->id()),
            ],
            'message_id' => 'required|integer|exists:chat_messages,id',
        ]);

        $messageId = $request->input('message_id');

        $message = ChatMessage::find($messageId);

        if ($message->created_at->diffInMinutes() > 3) {
            return response()->json([
                'code' => ErrorCode::ChatMessageUndoTimeLimit->value,
                'message' => 'You can only undo the message within 3 minute after sending it.',
            ], 400);
        }

        $message->is_undo = true;

        $message->save();

        return [
            'data' => $message,
        ];
    }

    public function updateLastReadCursor(Request $request)
    {
        $request->validate([
            'chat_id' => [
                'required',
                'integer',
                Rule::exists('chat_user_pivot', 'chat_id')->where('user_id', auth()->id()),
            ],
            'message_id' => 'required|integer|exists:chat_messages,id',
        ]);

        $messageId = $request->input('message_id');

        ChatUserPivot::where('chat_id', $request->input('chat_id'))
            ->where('user_id', auth()->id())
            ->where('last_read_cursor', '<', $messageId)
            ->update(['last_read_cursor' => $messageId]);

        return [
            'data' => true,
        ];
    }
}
