<?php

namespace App\Http\Controllers\Api\User;

use App\Enums\UploadFileState;
use App\Enums\UploadImageScene;
use App\Http\Controllers\Controller;
use App\Http\Resources\UserInfoResource;
use App\Mail\EmailVerify;
use App\Models\EmailVerifyCode;
use App\Models\UploadFile;
use App\Models\UploadImage;
use App\Models\UploadVideo;
use App\Models\User;
use App\Service\UploadService;
use App\Utils\Utils;
use Browser;
use Carbon\Carbon;
use Http;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Illuminate\Validation\Rules\File;
use Illuminate\Validation\ValidationException;
use Storage;
use Symfony\Component\HttpFoundation\StreamedResponse;

class UserController extends Controller
{
    use AuthorizesRequests, DispatchesJobs, ValidatesRequests;

    public function sendSignUpCode(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
        ]);

        $user = User::where('email', $request->email)->first();
        if ($user) {
            return response()->json([
                'status' => 'fail',
                'message' => 'Email Already Exists',
                'code' => 400,
            ], 400);
        }

        $code = random_int(100000, 999999);
        $data = [
            'code' => $code,
        ];

        try {
            Mail::to(['email' => $request->email])->send(new EmailVerify($data));
            // code...
        } catch (\Throwable $th) {
            return response()->json([
                'status' => 'fail',
                'message' => 'Email send fail',
                'code' => 500,
            ], 500);
        }

        $old_sign_up_codes = EmailVerifyCode::where('email', $request->email)
            ->where('status', 'sent')
            ->where('type', 'sign_up')
            ->delete();

        $new_sign_up_code = EmailVerifyCode::create([
            'email' => $request->email,
            'code' => $code,
            'type' => 'sign_up',
            'status' => 'sent',
            // 'expired_at'=> date('Y-m-d H:i:s', strtotime('3600', time()))
        ]);

        return response()->json([
            'status' => 'success',
            'message' => 'Code Sent',
            'code' => 200,
        ], 200);

    }

    public function verifySignUpCode(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'code' => 'required',
        ]);

        $sent_code = EmailVerifyCode::where('email', $request->email)
            ->where('code', $request->code)
            ->where('status', 'sent')
            ->where('type', 'sign_up')
            ->first();
        if ($sent_code) {
            $token = Str::random(16);
            $sent_code->status = 'verified';
            $sent_code->token = $token;
            $sent_code->save();

            return response()->json([
                'status' => 'success',
                'message' => 'Verify Success',
                'code' => 200,
                'data' => [
                    'token' => $token,
                ],
            ], 200);
        } else {
            return response()->json([
                'status' => 'fail',
                'message' => 'Verify fail',
                'code' => 400,
            ], 400);
        }
    }

    public function signUp(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required',
            'token' => 'required',
            'language_id' => 'integer|exists:languages,id',
        ]);
        $verified_code = EmailVerifyCode::where('email', $request->email)->where('token', $request->token)->where('status', 'verified')->first();
        if ($verified_code) {
            $password_hash = Hash::make($request->password);

            $new_user = User::create([
                'email' => $request->email,
                'password' => $password_hash,
            ]);
            if ($request->language_id) {
                $user_id = $new_user->id;
                $new_user->language_id = $request->language_id;
                $new_user->name = "YAYA{$user_id}";
                $new_user->save();
            }

            $browser = Browser::parse($request->userAgent());
            $token = $new_user->createToken(
                'token',
                ['*'],
                $request->remember ?
                    now()->addMonth() :
                    now()->addDay()
            );

            $token->accessToken->ip = $request->ip();
            $token->accessToken->browser = $browser->userAgent();
            $token->accessToken->save();

            return response()->json([
                'status' => 'success',
                'message' => 'Sign Up Success',
                'code' => 200,
                'data' => [
                    'token' => $token->plainTextToken,
                ],
            ], 200);
        } else {
            return response()->json([
                'status' => 'fail',
                'message' => 'Token Not Matched',
                'code' => 400,
            ], 400);
        }

    }

    public function signIn(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);

        $user = User::where('email', $request->email)->first();

        if (! $user || ! Hash::check($request->password, $user->password)) {
            throw ValidationException::withMessages([
                'email' => ['The provided credentials are incorrect.'],
            ]);
        }

        $browser = Browser::parse($request->userAgent());

        $token = $user->createToken(
            'token',
            ['*'],
            $request->remember ?
                now()->addMonth() :
                now()->addWeek()
        );

        $token->accessToken->ip = $request->ip();
        $token->accessToken->browser = $browser->userAgent();
        $token->accessToken->save();

        return [
            'data' => [
                'token' => $token->plainTextToken,
            ],
        ];
    }

    public function signOut(Request $request)
    {
        $user = Auth::user();

        $tokenId = $request->user()->currentAccessToken()->id;
        $user->tokens()->where('id', $tokenId)->delete();

        return [
            'data' => [
                'status' => 'success',
            ],
        ];
    }

    public function sendForgetPswCode(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
        ]);

        $user = User::where('email', $request->email)->first();
        if (! $user) {
            return response()->json([
                'status' => 'fail',
                'message' => 'Email Not Found',
                'code' => 404,
            ], 404);
        }

        $code = random_int(100000, 999999);

        $data = [
            'code' => $code,
        ];

        try {
            Mail::to(['email' => $request->email])->send(new EmailVerify($data));
            // code...
        } catch (\Throwable $th) {
            return response()->json([
                'status' => 'fail',
                'message' => 'Email send fail',
                'code' => 500,
            ], 500);
        }

        $old_forget_codes = EmailVerifyCode::where('email', $request->email)
            ->where('status', 'sent')
            ->where('type', 'forget_psw')
            ->delete();
        $new_forget_code = EmailVerifyCode::create([
            'email' => $request->email,
            'code' => $code,
            'type' => 'forget_psw',
            'status' => 'sent',
            // 'expired_at'=> date('Y-m-d H:i:s', strtotime('3600', time()))
        ]);

        return response()->json([
            'status' => 'success',
            'message' => 'Code Sent',
            'code' => 200,
        ], 200);

    }

    public function verifyForgetPswCode(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'code' => 'required',
        ]);

        $sent_code = EmailVerifyCode::where('email', $request->email)
            ->where('code', $request->code)
            ->where('status', 'sent')
            ->where('type', 'forget_psw')
            ->first();

        if ($sent_code) {
            $token = Str::random(16);
            $sent_code->status = 'verified';
            $sent_code->token = $token;
            $sent_code->save();

            return response()->json([
                'status' => 'success',
                'message' => 'Verify Success',
                'code' => 200,
                'data' => [
                    'token' => $token,
                ],
            ], 200);
        } else {
            return response()->json([
                'status' => 'fail',
                'message' => 'Verify fail',
                'code' => 400,
            ], 400);
        }
    }

    public function forgetPswChange(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required',
            'token' => 'required',
        ]);

        $verified_code = EmailVerifyCode::where('email', $request->email)
            ->where('token', $request->token)
            ->where('type', 'forget_psw')
            ->where('status', 'verified')->first();
        if ($verified_code) {
            $user = User::where('email', $request->email)->first();
            $password_hash = Hash::make($request->password);
            $user->password = $password_hash;
            $user->save();

            return response()->json([
                'status' => 'success',
                'message' => 'Change Password Success',
                'code' => 200,
            ], 200);
        } else {
            return response()->json([
                'status' => 'fail',
                'message' => 'Token Not Matched',
                'code' => 400,
            ], 400);
        }
    }

    public function info()
    {
        $user = Auth::user();

        $user = $user->with([
            'language',
            'currency',
            'artist',
            'avatar',
            'artist.avatar',
            'artist.cover',
        ])->find($user->id);

        if ($user->artist) {
            $user->artist->makeVisible(['worktask_plat_fee_ratio', 'product_plat_fee_ratio', 'pay_fee_ratio']);
        }
        $user->is_artist = $user->roles->pluck('name')->contains('artist');
        $user->setAttribute('permissions', $user->getAllPermissions());

        return new UserInfoResource($user);
    }

    public function changeName(Request $request)
    {
        $user = Auth::user();
        if ($user->name_changed_at) {
            if (strtotime(date('Y-m-d H:i:s', time())) < strtotime($user->name_changed_at.'+5 seconds')) {
                return response()->json([
                    'status' => 'fail',
                    'message' => 'Too soon to change again',
                    'code' => 400,
                    'data' => [
                        'wait_time' => '3600',
                    ],
                ], 400);
            }
        }

        $user->name = $request->name;
        $user->name_changed_at = date('Y-m-d H:i:s', time());
        $user->save();

        return response()->json([
            'status' => 'success',
            'message' => 'Change User Name Success',
            'code' => 200,
        ], 200);
    }

    public function changePsw(Request $request)
    {
        $user = Auth::user();
        if (Hash::check($request->old_psw, $user->password)) {
            $password_hash = Hash::make($request->new_psw);
            $user->password = $password_hash;
            $user->save();

            return response()->json([
                'status' => 'success',
                'message' => 'Change Password Success',
                'code' => 200,
            ], 200);
            // The passwords match...
        } else {
            return response()->json([
                'status' => 'fail',
                'message' => 'Old password not correct',
                'code' => 400,
            ], 400);
        }
    }

    public function sendChangeEmailCode(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
        ]);

        $exist_user = User::where('email', $request->email)->first();
        if ($exist_user) {
            return response()->json([
                'status' => 'fail',
                'message' => 'Email Already Exists',
                'code' => 400,
            ], 400);
        }

        $user = Auth::user();
        $code = random_int(100000, 999999);
        $data = [
            'code' => $code,
        ];

        try {
            Mail::to(['email' => $request->email])->send(new EmailVerify($data));
            // code...
        } catch (\Throwable $th) {
            return response()->json([
                'status' => 'fail',
                'message' => 'Email send fail',
                'code' => 500,
            ], 500);
        }

        $old_change_email_codes = EmailVerifyCode::where('email', $request->email)
            ->where('status', 'sent')
            ->where('type', 'change_email')
            ->delete();

        $new_change_email_code = EmailVerifyCode::create([
            'email' => $request->email,
            'code' => $code,
            'type' => 'change_email',
            'status' => 'sent',
            // 'expired_at'=> date('Y-m-d H:i:s', strtotime('3600', time()))
        ]);

        return response()->json([
            'status' => 'success',
            'message' => 'Code Sent',
            'code' => 200,
        ], 200);

    }

    public function changeEmail(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'code' => 'required',
        ]);

        $exist_user = User::where('email', $request->email)->first();
        if ($exist_user) {
            return response()->json([
                'status' => 'fail',
                'message' => 'Email Already Exists',
                'code' => 400,
            ], 400);
        }

        $user = Auth::user();
        $sent_code = EmailVerifyCode::where('email', $request->email)
            ->where('code', $request->code)
            ->where('status', 'sent')
            ->where('type', 'change_email')
            ->first();
        if ($sent_code) {
            $user->email = $request->email;
            $user->save();

            $sent_code->status = 'used';
            $sent_code->save();

            return response()->json([
                'status' => 'success',
                'message' => 'Change Email Success',
                'code' => 200,
            ], 200);
        } else {
            return response()->json([
                'status' => 'fail',
                'message' => 'Code Not Match',
                'code' => 400,
            ], 400);
        }

    }

    public function changeAvatar(Request $request)
    {
        $request->validate([
            'upload_image_id' => 'required|exists:upload_images,id',
        ]);

        $user = Auth::user();

        $upload_image = $user->uploadImages()
            ->where('id', $request->upload_image_id)
            ->where('scene', UploadImageScene::UserAvatar)
            ->first();
        if (! $upload_image) {
            abort(400, 'Upload Image Not Found');
        }

        $user->avatar_id = $upload_image->id;
        $user->save();

        return ['ok' => true];
    }

    public function updateSetting(Request $request)
    {
        $request->validate([
            'currency_id' => 'exists:currencies,id',
            'language_id' => 'exists:languages,id',
            'locale' => 'string',
            'chat_language_code' => 'string',
            'chat_auto_translate' => 'boolean',
            'chat_minimize_on_redirect' => 'boolean',
        ]);

        $user = Auth::user();
        if ($request->has('currency_id')) {
            $user->currency_id = $request->get('currency_id');
        }
        if ($request->has('language_id')) {
            $user->language_id = $request->get('language_id');
        }

        $settings = $user->settings;
        if ($request->has('locale')) {
            $settings['locale'] = $request->get('locale');
        }
        if ($request->has('chat_language_code')) {
            $settings['chat_language_code'] = $request->get('chat_language_code');
        }
        if ($request->has('chat_auto_translate')) {
            $settings['chat_auto_translate'] = $request->get('chat_auto_translate');
        }
        if ($request->has('chat_minimize_on_redirect')) {
            $settings['chat_minimize_on_redirect'] = $request->get('chat_minimize_on_redirect');
        }
        $user->settings = $settings;

        $user->save();

        return [
            'ok' => true,
            'data' => $user,
        ];
    }

    public function uploadImage(Request $request)
    {
        $request->validate([
            'scene' => 'string',
            'file' => [
                'required',
                File::types(['jpg', 'png', 'jpeg', 'gif', 'webp'])
                    ->min('1kb')
                    ->max('50mb'),
            ],
        ]);
        $user = Auth::user();
        $uploadedFile = $request->file('file');

        $date = Carbon::now();
        $formattedDate = $date->format('Y-m-d');

        $scene = $request->get('scene', 'default');

        $topFolder = 'upload_images';
        $imageFolder = "{$topFolder}/user_{$user->id}/{$scene}/{$formattedDate}/";

        $uploadImage = new UploadImage;
        $uploadImage->user_id = $user->id;
        $uploadImage->scene = $scene;
        $uploadImage->mime = $uploadedFile->getMimeType();
        $uploadImage->size = $uploadedFile->getSize();

        $ins = new UploadService;
        $ins->processUploadImage($uploadedFile, $imageFolder, $uploadImage);

        return [
            'data' => $uploadImage,
        ];
    }

    public function uploadVideo(Request $request)
    {
        $request->validate([
            'scene' => 'string',
            'file' => [
                'required',
                File::types(['mp4'])
                    ->min('1kb')
                    ->max('50mb'),
            ],
        ]);
        $user = Auth::user();
        $uploadedFile = $request->file('file');

        $date = Carbon::now();
        $formattedDate = $date->format('Y-m-d');

        $scene = $request->get('scene', 'default');

        $topFolder = 'upload_videos';
        $imageFolder = "{$topFolder}/user_{$user->id}/{$scene}/{$formattedDate}/";

        $uploadVideo = new UploadVideo;
        $uploadVideo->user_id = $user->id;
        $uploadVideo->scene = $scene;
        $uploadVideo->mime = $uploadedFile->getMimeType();
        $uploadVideo->size = $uploadedFile->getSize();

        $ins = new UploadService;
        $ins->processUploadVideo($uploadedFile, $imageFolder, $uploadVideo);

        return [
            'data' => $uploadVideo,
        ];
    }

    public function uploadFileTemplateUrl(Request $request)
    {
        $request->validate([
            'scene' => 'nullable|string',
            'name' => 'required|string',
        ]);

        $user = Auth::user();

        $date = Carbon::now();
        $formattedDate = $date->format('Y-m-d');

        $scene = $request->get('scene', 'default');
        $name = $request->get('name', 'default');

        $topFolder = 'upload_files';
        $uploadFolder = "{$topFolder}/user_{$user->id}/{$scene}/{$formattedDate}/";
        $fileName = uniqid();

        $extension = pathinfo($name, PATHINFO_EXTENSION);

        $filePath = "{$uploadFolder}{$fileName}_og.{$extension}";

        $uploadFile = new UploadFile;
        $uploadFile->name = $name;
        $uploadFile->user_id = $user->id;
        $uploadFile->scene = $scene;
        $uploadFile->url_og = $filePath;
        $uploadFile->state = UploadFileState::Pending;
        $uploadFile->save();

        $temporaryUploadUrl = Storage::temporaryUploadUrl($filePath, now()->addHours(1));

        return [
            'data' => [
                'upload_file_id' => $uploadFile->id,
                'file_path' => $filePath,
                ...$temporaryUploadUrl,
            ],
        ];
    }

    public function uploadFileSuccess(Request $request)
    {
        $request->validate([
            'id' => 'required|exists:upload_files,id',
        ]);

        $uploadFile = UploadFile::find($request->id);

        $url_og = $uploadFile->getRawOriginal('url_og');

        if (! Storage::exists($url_og)) {
            abort(400, 'File Not Found');
        }

        $size = Storage::size($url_og);
        $mime = Storage::mimeType($url_og);

        $uploadFile->state = UploadFileState::Successed;
        $uploadFile->size = $size;
        $uploadFile->mime = $mime;
        $uploadFile->save();

        return [
            'data' => $uploadFile,
        ];
    }

    public function uploadFile(Request $request)
    {
        $request->validate([
            'scene' => 'string',
            'file' => [
                'required',
                File::defaults()
                    ->min('1kb')
                    ->max('2gb'),
            ],
        ]);

        $user = Auth::user();
        $uploadedFile = $request->file('file');
        $scene = $request->get('scene', 'default');

        $uploadFile = new UploadFile;
        $uploadFile->user_id = $user->id;
        $uploadFile->scene = $scene;
        $uploadFile->mime = $uploadedFile->getMimeType();
        $uploadFile->name = $uploadedFile->getClientOriginalName();
        $uploadFile->size = $uploadedFile->getSize();
        $uploadFile->state = UploadFileState::Successed;
        UploadService::new()->processUploadFile($uploadedFile, $uploadFile);

        return [
            'data' => $uploadFile,
        ];
    }

    public function proxyImage(Request $request)
    {
        $request->validate([
            'url' => 'required|url',
        ]);

        $url = $request->input('url');

        $response = Http::withHeaders([
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3',
        ])->withOptions(['stream' => true])->get($url);

        if ($response->successful()) {
            $contentType = $response->header('Content-Type');

            return new StreamedResponse(function () use ($response) {
                $body = $response->toPsrResponse()->getBody();
                while (! $body->eof()) {
                    echo $body->read(1024);
                    ob_flush();
                    flush();
                }
            }, 200, ['Content-Type' => $contentType]);
        }

        return response()->json(['error' => 'Failed to fetch image'], 500);
    }

    public function videoPreviewImage(Request $request)
    {
        $request->validate([
            'upload_type' => 'required|string|in:youtube_link,bilibili_link',
            'vid' => 'required|string',
        ]);

        return [
            'data' => Utils::videoPreviewImage($request->upload_type, $request->vid),
        ];
    }
}
