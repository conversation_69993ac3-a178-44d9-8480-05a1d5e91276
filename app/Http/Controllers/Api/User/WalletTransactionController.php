<?php

namespace App\Http\Controllers\Api\User;

use App\Http\Controllers\Controller;
use Auth;
use Illuminate\Http\Request;

class WalletTransactionController extends Controller
{
    public function list(Request $request)
    {
        $request->validate([
            'page' => 'integer|min:1',
            'size' => 'integer|min:1,max:50',
            'wallet_id' => 'integer|min:1',
        ]);

        $page = $request->input('page', 1);
        $size = $request->input('size', 15);

        $user = Auth::user();

        $ret = $user->walletTransactions()
            ->when($request->wallet_id, function ($query) use ($request) {
                return $query->where('wallet_id', $request->wallet_id);
            })
            ->with([
                'currency',
                'wallet',
            ])
            ->orderByDesc('id')
            ->paginate($size);

        return [
            'data' => $ret->items(),
            'total' => $ret->total(),
        ];
    }
}
