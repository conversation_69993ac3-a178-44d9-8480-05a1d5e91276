<?php

namespace App\Http\Controllers\Api\User;

use App\Http\Controllers\Controller;
use App\Models\Project;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ProjectExampleController extends Controller
{
    public function create(Request $request)
    {
        $request->validate([
        ]);

        $user = Auth::user();

        $requestData = $request->only('price', 'deadline', 'prod_format');
        $project = new Project;
        $project->fill($requestData);
        $project->user_id = $user->id;
        $project->setTransByReq('content', $request, true);
        $project->setTransByReq('name', $request, true);
        $project->save();

        if ($request->has('examples')) {
            $project->examples()->delete();
            $project->examples()->createMany($request->examples);
        }

        return ['data' => $project];
    }
}
