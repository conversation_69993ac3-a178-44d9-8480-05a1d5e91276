<?php

namespace App\Http\Controllers\Api\User;

use App\Enums\OrderStatus;
use App\Enums\SystemNotificationScene;
use App\Enums\WalletTransactionAction;
use App\Enums\WalletTransactionBizType;
use App\Enums\WalletTransactionStatus;
use App\Enums\WalletUsage;
use App\Http\Controllers\Controller;
use App\Models\Currency;
use App\Models\Wallet;
use App\Models\WorkTask;
use App\Models\WorkTaskStage;
use App\Notifications\SystemNotification;
use App\Service\RateService;
use App\Service\Stripe\Amount;
use App\Service\UploadFileService;
use App\Service\Wallet\PlatFeeWalletService;
use Cache;
use DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class WorkTaskController extends Controller
{
    public function list(Request $request)
    {
        $request->validate([
            'page' => 'integer|min:1',
            'size' => 'integer|min:1,max:50',
            'type' => 'nullable|string|in:service,project',
            'sort_by' => 'nullable|in:id,created_at,updated_at',
            'sort_order' => 'nullable|in:desc,asc',
            'status' => 'nullable|array',
        ]);

        $page = $request->input('page', 1);
        $size = $request->input('size', 15);

        $user = Auth::user();
        $query = $user->workTasks();

        if ($request->has('type')) {
            $query->where('type', $request->input('type'));
        }

        $query->with([
            'artist:id,name,avatar_id' => [
                'avatar',
            ],
            'busable',
            'reqable',
            'currency',
            'workTaskStages',
            'artistRecReview',
            'userRecReview',
        ])
            ->withCount([
                'workTaskFiles as unread_files_count' => function ($query) {
                    $query->where('user_is_read', false);
                },
            ])
            ->orderBy($request->input('sort_by', 'id'), $request->input('sort_order', 'desc'));

        if ($request->has('status') && $request->status) {
            $query->whereIn('status', $request->status);
        } else {
            $query->whereIn('status', [WorkTask::StatusWorking, WorkTask::StatusFinished]);
        }

        $ret = $query->paginate($size);

        return [
            'data' => $ret->items(),
            'total' => $ret->total(),
        ];
    }

    public function info(Request $request)
    {
        $request->validate([
            'id' => 'integer|min:1',
        ]);

        $ret = WorkTask::with([
            'user:id,name,avatar_id' => [
                'avatar',
            ],
            'artist:id,name,avatar_id' => [
                'avatar',
            ],
            'busable',
            'reqable',
            'workTaskStages',
            'workTaskFiles.uploadFile',
            'artistRecReview',
            'userRecReview',
            'currency',
            'payingOrder:id,status,busable_id,busable_type',
            'group',
            'priceChange',
        ])
            ->find($request->id);

        foreach ($ret->workTaskFiles as $workTaskFile) {
            $workTaskFile->user_is_read = true;
            $workTaskFile->save();
        }

        return [
            'data' => $ret,
        ];
    }

    public function cancel(Request $request)
    {
        $request->validate([
            'id' => 'integer|min:1|exists:work_tasks,id',
        ]);

        $workTask = Auth::user()->workTasks()->find($request->input('id'));
        if (! $workTask) {
            abort(404, 'Not found.');
        }

        if (! in_array($workTask->status, [WorkTask::StatusPending, WorkTask::StatusWaitPay])) {
            abort(403, 'Can not cancel.');
        }

        DB::beginTransaction();
        $workTask->status = WorkTask::StatusUserCanceled;
        $workTask->save();
        DB::commit();

        // notify
        $notify = SystemNotification::make(SystemNotificationScene::WorktaskUserCanceled)
            ->setMeta([
                'work_task_id' => $workTask->id,
            ]);
        $workTask->artist->notify($notify);
        if ($workTask->group) {
            $workTask->group->notify($notify);
        }

        return [
            'ok' => true,
        ];
    }

    public function confrimStageWorkStatus(Request $request)
    {
        $request->validate([
            'id' => 'integer|min:1|exists:work_tasks,id',
        ]);

        $user = Auth::user();

        $lockKey = "lock:confrimStageWorkStatus:{$request->id}";
        $lock = Cache::lock($lockKey, 30);
        if (! $lock->get()) {
            abort(429, '系统繁忙,请稍后重试');
        }

        DB::beginTransaction();
        $workTask = $user->workTasks()
            ->lockForUpdate()
            ->find($request->id);
        if (! $workTask) {
            abort(404, 'Not found');
        }

        $workTaskStage = $workTask->workTaskStages()
            ->where('is_paid', WorkTaskStage::PayStatusPaid)
            ->where('work_status', '!=', WorkTaskStage::WorkStatusFinished)
            ->orderBy('percent', 'asc')
            ->first();
        if (! $workTaskStage) {
            abort(400, 'No stage found');
        }

        $workTaskStage->work_status = WorkTaskStage::WorkStatusFinished;
        $workTaskStage->save();

        $nextStage = $workTask->workTaskStages()
            ->where('is_paid', WorkTaskStage::PayStatusPaid)
            ->where('work_status', WorkTaskStage::WorkStatusPending)
            ->orderBy('percent', 'asc')
            ->first();

        if ($nextStage) {
            $nextStage->work_status = WorkTaskStage::WorkStatusWorking;
            $nextStage->save();
        }

        // check all stage is finished
        $isAllFinished = $workTask->workTaskStages()
            ->where('is_paid', WorkTaskStage::PayStatusPaid)
            ->where('work_status', '!=', WorkTaskStage::WorkStatusFinished)
            ->doesntExist();

        if ($isAllFinished) {
            $workTask->status = WorkTask::StatusFinished;
            $workTask->save();
        }

        // transfer money to artist wallet
        if ($isAllFinished) {

            $currency = null;
            if ($workTask->currency->isCanWithdraw) {
                $currency = $workTask->currency;
            } else {
                $currency = Currency::query()->where('code', Currency::BaseCurrency)->first();
            }

            // if dont exists, create
            $artistWallet = Wallet::query()->firstOrCreate([
                'user_id' => $workTask->artist->user_id,
                'currency_id' => $currency->id,
                'usage' => WalletUsage::Deposit,
            ]);

            // lock for update artist wallet
            $artistWallet = Wallet::where('user_id', $workTask->artist->user_id)
                ->where('currency_id', $currency->id)
                ->where('usage', WalletUsage::Deposit)
                ->lockForUpdate()
                ->first();

            // # calc balance

            // ## plat fee ratio
            $platFeeRatio = $workTask->artist->worktask_plat_fee_ratio;
            if ($workTask->busable_type === WorkTask::TypeService || $workTask->busable_type === WorkTask::TypeProject) {
                $platFeeRatio = $workTask->artist->worktask_plat_fee_ratio;
            } elseif ($workTask->busable_type === WorkTask::TypeProduct) {
                $platFeeRatio = $workTask->artist->product_plat_fee_ratio;
            }

            // ## amount
            $totalAmount = 0;
            $payFee = 0;
            $platFee = 0;
            $amount = 0;

            if ($workTask->currency->isCanWithdraw) {
                $totalAmount = $workTask->price;

                $payFee = 0;
                $workTask->orders->each(function ($order) use (&$payFee) {
                    if ($order->status === OrderStatus::Paid) {
                        $payFee += $order->fee->amount;
                    }
                });
                $payFee = round($payFee, 2);

                $platFee = round($totalAmount * $platFeeRatio, 2);
                $platFee = ceil($platFee);
                $beforePlatFee = $platFee;

                $platFeeWalletService = new PlatFeeWalletService;
                $platFeeProcessStage = $platFeeWalletService->minus($workTask->artist->user, Amount::new($platFee, $currency));
                if ($platFeeProcessStage) {
                    $platFee = $platFeeProcessStage->after_amount->amount;
                    $platFeeWallet = Wallet::find($platFeeProcessStage->wallet_id);
                    $platFeeWallet->balance = $platFeeProcessStage->wallet_balance_after->amount;
                    $platFeeWallet->save();
                    $platFeeWallet->transactions()->create([
                        'user_id' => $workTask->artist->user->id,
                        'currency_id' => $platFeeProcessStage->wallet_balance->currency->id,
                        'action' => WalletTransactionAction::Minus,
                        'biz_type' => WalletTransactionBizType::PlatFeeDeduct,
                        'status' => WalletTransactionStatus::Successed,
                        'before_balance' => $platFeeProcessStage->wallet_balance->amount,
                        'after_balance' => $platFeeProcessStage->wallet_balance_after->amount,
                        'amount' => $platFeeProcessStage->wallet_minus->amount,
                    ]);
                }

                $amount = round($totalAmount - $payFee - $platFee, 2);
            } else {
                $totalAmount = 0;
                $payFee = 0;
                $workTask->orders->each(function ($order) use (&$totalAmount, &$payFee) {
                    if ($order->status === OrderStatus::Paid) {
                        if (strtoupper($order->payment_info->balance_transaction['currency']) !== Currency::BaseCurrency) {
                            abort(400, 'Currency not match');
                        }
                        $totalAmount += $order->payment_info->balance_transaction['amount'];
                        $payFee += $order->payment_info->balance_transaction['fee'];
                    }
                });

                $platFee = round($totalAmount * $platFeeRatio, 2);
                $platFee = ceil($platFee);
                $beforePlatFee = $platFee;

                $platFeeWalletService = new PlatFeeWalletService;
                $platFeeProcessStage = $platFeeWalletService->minus($workTask->artist->user, Amount::new($platFee, $currency));
                if ($platFeeProcessStage) {
                    $platFee = $platFeeProcessStage->after_amount->amount;
                    $platFeeWallet = Wallet::find($platFeeProcessStage->wallet_id);
                    $platFeeWallet->balance = $platFeeProcessStage->wallet_balance_after->amount;
                    $platFeeWallet->save();
                    $platFeeWallet->transactions()->create([
                        'user_id' => $workTask->artist->user->id,
                        'currency_id' => $platFeeProcessStage->wallet_balance->currency->id,
                        'action' => WalletTransactionAction::Minus,
                        'biz_type' => WalletTransactionBizType::PlatFeeDeduct,
                        'status' => WalletTransactionStatus::Successed,
                        'before_balance' => $platFeeProcessStage->wallet_balance->amount,
                        'after_balance' => $platFeeProcessStage->wallet_balance_after->amount,
                        'amount' => $platFeeProcessStage->wallet_minus->amount,
                    ]);
                }

                $amount = round($totalAmount - $payFee - $platFee, 2);
            }

            // ## zero decimal currency
            if ($workTask->currency->is_zero_decimal) {
                $amount = round($amount / 100) * 100;
            }

            // ## update balance
            $beforeBalance = $artistWallet->balance;
            $afterBalance = $beforeBalance + $amount;

            $artistWallet->balance = $afterBalance;
            $artistWallet->save();

            // ## create transaction
            $detail = [
                [
                    'type' => 'init',
                    'amount' => $totalAmount,
                    'biz_type' => WalletTransactionBizType::WorkTask->value,
                    'biz_info' => [
                        'work_task_id' => $workTask->id,
                    ],
                ],
                [
                    'type' => 'pay_fee',
                    'action' => WalletTransactionAction::Minus,
                    'amount' => $payFee,
                ],
                [
                    'type' => 'plat_fee',
                    'action' => WalletTransactionAction::Minus,
                    'amount' => $beforePlatFee,
                ],
                [
                    'type' => 'plat_fee',
                    'action' => WalletTransactionAction::Plus,
                    'amount' => $platFeeProcessStage ? $platFeeProcessStage->amount_minus->amount : 0,
                ],
                [
                    'type' => 'result',
                    'amount' => $amount,
                ],
            ];

            $artistWallet->transactions()->create([
                'user_id' => $workTask->artist->user_id,
                'currency_id' => $currency->id,
                'action' => WalletTransactionAction::Plus,
                'biz_type' => WalletTransactionBizType::WorkTask,
                'status' => WalletTransactionStatus::Successed,
                'before_balance' => $beforeBalance,
                'after_balance' => $afterBalance,
                'amount' => $amount,
                'detail' => $detail,
            ]);

            // ## service completed_tasks ++
            if ($workTask->busable_type === WorkTask::TypeService) {
                $service = $workTask->busable;
                $service->completed_tasks += 1;
                $service->save();
            }
            DB::commit();

            // notify
            $notify = SystemNotification::make(SystemNotificationScene::WorktaskFinished)
                ->setMeta([
                    'work_task_id' => $workTask->id,
                ]);
            $workTask->artist->notify($notify);
            $workTask->group->notify($notify);
            $workTask->user->notify($notify);
        } else {
            DB::commit();
        }

        $lock->release();

        return ['ok' => true];
    }

    private function deductPlatFee($user, $workTask, $platFee)
    {
        $platFeeWallet = $user->wallets()
            ->where('usage', WalletUsage::PlatFee)
            ->first();
        if (! $platFeeWallet) {
            return $platFee;
        }

        $rate = RateService::new()->getRate($platFeeWallet->currency->code, $workTask->currency->code);
        $platFeeBalance = RateService::new()->amountExchangeByRate($platFeeWallet->balance, $rate, 'ceil');

        if ($platFeeBalance >= $platFee) {
            $beforeBalance = $platFeeWallet->balance;
            $afterBalance = $platFeeWallet->balance - $platFee;
            $platFeeWallet->balance = $afterBalance;
            $platFeeWallet->save();

            $platFeeWallet->transactions()->create([
                'user_id' => $user->id,
                'currency_id' => $workTask->currency_id,
                'action' => WalletTransactionAction::Minus,
                'biz_type' => WalletTransactionBizType::PlatFeeDeduct,
                'status' => WalletTransactionStatus::Successed,
                'before_balance' => $beforeBalance,
                'after_balance' => $afterBalance,
                'amount' => $platFee,
            ]);
        } else {
            $platFee = $platFee - $platFeeBalance;
            $platFeeWallet->balance = 0;
            $platFeeWallet->save();

            $platFeeWallet->transactions()->create([
                'user_id' => $user->id,
                'currency_id' => $workTask->currency_id,
                'action' => WalletTransactionAction::Minus,
                'biz_type' => WalletTransactionBizType::PlatFeeDeduct,
                'status' => WalletTransactionStatus::Successed,
                'before_balance' => $platFeeWallet->balance,
                'after_balance' => 0,
                'amount' => $platFeeBalance,
            ]);
        }

        return $platFee;
    }

    public function download(Request $request)
    {
        $request->validate([
            'id' => 'integer|min:1|exists:work_task_files,id',
        ]);

        $user = Auth::user();
        $workTaskFile = $user->workTaskFiles()->find($request->id);
        if (! $workTaskFile) {
            abort(404, 'Not found');
        }

        $temporaryUrl = UploadFileService::downloadTemporaryUrl($workTaskFile->uploadFile);

        return [
            'temporary_url' => $temporaryUrl,
        ];
    }
}
