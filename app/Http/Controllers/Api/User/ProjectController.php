<?php

namespace App\Http\Controllers\Api\User;

use App\Enums\TranslateStatus;
use App\Enums\TranslateType;
use App\Http\Controllers\Controller;
use App\Models\ArtStyle;
use App\Models\Category;
use App\Models\ColorModel;
use App\Models\Format;
use App\Models\Project;
use App\Models\Stage;
use App\Models\StageTemplate;
use App\Models\Tag;
use App\Models\Translation;
use App\Service\StageService;
use Cache;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ProjectController extends Controller
{
    public function list(Request $request)
    {

        $request->validate([
            'page' => 'integer|min:1',
            'size' => 'integer|min:1,max:50',
            'is_archived' => 'boolean',
            'sort_by' => 'nullable|in:id,created_at,updated_at',
            'sort_order' => 'nullable|in:desc,asc',
        ]);

        $page = $request->input('page', 1);
        $size = $request->input('size', 15);

        $user = Auth::user();
        $projects = $user->projects()
            ->with([
                'examples' => function ($query) {
                    $query->take(8);
                },
                'categories',
                'currency',
            ])
            ->when($request->has('is_archived'), function ($query) use ($request) {
                $query->where('is_archived', $request->input('is_archived'));
            })
            ->withCount(['projectRequests'])
            ->orderBy($request->input('sort_by', 'id'), $request->input('sort_order', 'desc'))
            ->paginate($size);

        return [
            'data' => $projects->items(),
            'total' => $projects->total(),
        ];
    }

    public function create(Request $request)
    {
        $request->validate([
            'name' => 'required',
            'currency_id' => 'required|integer|exists:currencies,id',
            'content' => 'required',
            'price_start' => 'required|integer|min:0',
            'price_end' => 'required|integer|min:0',
            'deadline' => 'required',
            'examples' => 'array|max:8',
            'right_template_id' => 'integer',
        ]);

        $user = Auth::user();

        $requestData = $request->only('currency_id', 'right_template_id', 'price_start', 'price_end', 'use_range', 'feedback_intervals', 'deadline', 'prod_format', 'size_spec', 'is_private');
        $project = new Project;
        $project->fill($requestData);
        $project->user_id = $user->id;
        $project->setTransByReq('content', $request, true);
        $project->setTransByReq('name', $request, true);
        $project->save();

        if ($request->has('examples')) {
            $project->examples()->delete();
            $project->examples()->createMany($request->examples);
        }
        if ($request->has('categories')) {
            $project->categories()->sync($request->categories);
        }
        if ($request->has('color_models')) {
            $project->colorModels()->sync($request->color_models);
        }
        if ($request->has('formats')) {
            $project->formats()->sync($request->formats);
        }
        if ($request->has('stages')) {
            $project->stages()->sync(StageService::convertToSyncStages($request->stages, 'stage_id'));
        }
        if ($request->has('art_styles')) {
            $project->artStyles()->sync($request->art_styles);
        }
        if ($request->has('translations')) {
            $project->translations()->sync($request->translations);
        }
        if ($request->has('project_managers')) {
            $project->projectManagers()->sync($request->project_managers);
        }

        return ['data' => $project];
    }

    public function update(Request $request)
    {
        $request->validate([
            'id' => 'required|integer',
            'right_template_id' => 'integer',
            'is_archived' => 'boolean',
        ]);

        $user = Auth::user();
        $project = $user->projects()->find($request->id);
        if (! $project) {
            return response()->json([
                'status' => 'error',
            ], 403);
        }

        $requestData = $request->only('currency_id', 'right_template_id', 'price_start', 'price_end', 'use_range', 'feedback_intervals', 'deadline', 'prod_format', 'size_spec', 'is_private');
        $project->fill($requestData);
        if ($request->has('content')) {
            $project->setTransByReq('content', $request);
        }
        if ($request->has('name')) {
            $project->setTransByReq('name', $request);
        }
        if ($request->has('is_archived')) {
            $project->is_archived = $request->input('is_archived');
        }
        $project->save();

        if ($request->has('examples')) {
            $project->examples()->delete();
            $project->examples()->createMany($request->examples);
        }
        if ($request->has('categories')) {
            $project->categories()->sync($request->categories);
        }
        if ($request->has('color_models')) {
            $project->colorModels()->sync($request->color_models);
        }
        if ($request->has('formats')) {
            $project->formats()->sync($request->formats);
        }
        if ($request->has('stages')) {
            $project->stages()->sync(StageService::convertToSyncStages($request->stages, 'stage_id'));
        }
        if ($request->has('art_styles')) {
            $project->artStyles()->sync($request->art_styles);
        }
        if ($request->has('translations')) {
            $project->translations()->sync($request->translations);
        }
        if ($request->has('project_managers')) {
            $project->projectManagers()->sync($request->project_managers);
        }

        return ['ok' => true];
    }

    public function delete(Request $request)
    {
        $request->validate([
            'id' => 'required|integer',
        ]);

        $user = Auth::user();
        $project = $user->projects()->find($request->id);
        if (! $project) {
            return response()->json([
                'status' => 'error',
            ], 403);
        }

        $project->delete();

        return ['ok' => true];
    }

    public function info(Request $request)
    {
        $request->validate([
            'id' => 'integer|min:1',
        ]);

        $user = Auth::user();
        $ret = $user->projects()->with([
            'user:id,name,avatar_id' => [
                'avatar',
            ],
            'currency:id,name,code,symbol',
            'categories',
            'colorModels',
            'formats',
            'stages',
            'artStyles',
            'examples',
            'rightTemplate',
            'translates' => function ($query) {
                $query->select('id', 'busable_id', 'busable_field', 'source_lang', 'target_lang')
                    ->where('status', TranslateStatus::Pending)
                    ->where('translate_type', TranslateType::Human);
            },
        ])->find($request->input('id'));

        return [
            'data' => $ret,
        ];
    }

    public function meta()
    {
        Cache::delete('user_api_project_meta');
        $data = Cache::remember('user_api_project_meta', 60 * 60 * 24, function () {
            $tags = Tag::where(['display' => 1])->get(['id', 'name', 'color']);
            $categories = Category::tree()->get()->toTree();
            $stages = Stage::query()->get(['id', 'name', 'sort', 'is_end_stage']);
            $artStyles = ArtStyle::get(['id', 'name']);
            $stageTemplates = StageTemplate::with(['stages:id,name'])->get();
            $formats = Format::get(['id', 'name', 'sort']);
            $colorModels = ColorModel::get(['id', 'name', 'sort']);
            $useRanges = [
                '需双方协商',
            ];
            $feedback_intervals = [
                3, 5, 7,
            ];
            $translations = Translation::with(['targetLanguage'])->get();

            return [
                'tags' => $tags,
                'categories' => $categories,
                'stages' => $stages,
                'art_styles' => $artStyles,
                'formats' => $formats,
                'color_models' => $colorModels,
                'use_ranges' => $useRanges,
                'feedback_intervals' => $feedback_intervals,
                'stage_templates' => $stageTemplates,
                'translations' => $translations,
            ];
        });

        return [
            'data' => $data,
        ];

    }
}
