<?php

namespace App\Http\Controllers\Api\User;

use App\Enums\TranslateStatus;
use App\Http\Controllers\Controller;
use App\Http\Resources\TranslateAiCreateResource;
use App\Http\Resources\TranslateCreateResource;
use App\Models\Translate;
use App\Service\Translate\KeyInfo;
use App\Service\Translate\TokenCalculator;
use App\Service\Translate\TranslateDB;
use App\Service\Translate\TranslationManager;
use Auth;
use Illuminate\Http\Request;
use Str;

class TranslateController extends Controller
{
    public function create(Request $request)
    {
        $request->validate([
            'keys' => 'required|array',
            'keys.*' => 'string',
            'source_lang' => 'required|string',
            'target_langs' => 'required|array',
            'translate_type' => 'required|string|in:gpt,human',
        ]);

        $user = Auth::user();
        $keys = $request->keys;
        $source_lang = $request->source_lang;
        $target_langs = $request->target_langs;
        $translate_type = $request->translate_type;

        if ($translate_type === 'human') {
            foreach ($keys as $key) {
                $keyInfo = KeyInfo::decrypt($key);
                $langs = [$source_lang, ...$target_langs];
                $exists = Translate::where('busable_type', Str::singular($keyInfo->table))
                    ->where('busable_id', $keyInfo->id)
                    ->where('busable_field', $keyInfo->column)
                    ->where('status', TranslateStatus::Pending)
                    ->whereIn('source_lang', $langs)
                    ->whereIn('target_lang', $langs)
                    ->first();
                if ($exists) {
                    return abort(422, 'Translate already exists');
                }
            }
            $translates = TranslateDB::createHumanTranslates($keys, $source_lang, $target_langs, $user->id);
        } else {
            $translates = TranslateDB::createGptTranslates($keys, $source_lang, $target_langs, $user->id);
            $translateIds = collect($translates)->pluck('id');
            TranslationManager::translateColumnByIds($translateIds);

            $translates = Translate::whereIn('id', $translateIds)->get();
        }

        return TranslateCreateResource::collection($translates);
    }

    public function createAiTranslate(Request $request)
    {
        $request->validate([
            'source_lang' => 'required|string',
            'target_langs' => 'required|array',
            'target_langs.*' => 'required|string',
            'contents' => 'required|array',
            'contents.*.content' => 'nullable|string',
            'contents.*.busable_table' => 'nullable|string',
            'contents.*.busable_field' => 'nullable|string',
        ]);

        $user = Auth::user();
        $source_lang = $request->source_lang;
        $target_langs = $request->target_langs;
        $contents = $request->contents;

        $translates = TranslateDB::createGptTranslateByContents($contents, $source_lang, $target_langs, $user->id);
        $translateIds = collect($translates)->pluck('id');
        TranslationManager::translateColumnByIds($translateIds);

        $translates = Translate::whereIn('id', $translateIds)->get();

        return TranslateAiCreateResource::collection($translates);
    }

    public function preCalcBatteries(Request $request)
    {
        $request->validate([
            'contents' => 'required|array',
            'contents.*' => 'nullable|string',
        ]);

        $total_batteries = 0;
        $total_tokens = 0;
        foreach ($request->contents as $content) {
            if (empty($content)) {
                continue;
            }
            $batteries = TokenCalculator::preCalcBatteries($content);
            $countTokens = TokenCalculator::countTokens($content);

            $total_batteries += $batteries;
            $total_tokens += $countTokens;
        }

        return [
            'data' => [
                'total_batteries' => $total_batteries,
                'total_tokens' => $total_tokens,
            ],
        ];

    }

    public function cancelHumanTranslate(Request $request)
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'required|integer',
        ]);

        $user = Auth::user();

        TranslateDB::cancelHumanTranslates($user->id, $request->ids);

        return ['ok' => true];
    }
}
