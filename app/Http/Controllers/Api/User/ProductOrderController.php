<?php

namespace App\Http\Controllers\Api\User;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\ProductOrder;
use Auth;
use DB;
use Illuminate\Http\Request;

class ProductOrderController extends Controller
{
    public function create(Request $request)
    {
        $request->validate([
            'product_ids' => 'required|array|exists:products,id',
            'currency_id' => 'required|integer|exists:currencies,id',
        ]);

        $product_ids = $request->product_ids;
        $currency_id = $request->currency_id;

        $products = Product::whereIn('id', $product_ids)
            ->with([
                'showcases',
                'currency',
            ])
            ->get();

        $amount = $this->calcProductAmount($products);

        // createProductOrder
        DB::beginTransaction();
        $order_number = $this->generateOrderNumber(Auth::id());

        $po = ProductOrder::create([
            'order_number' => $order_number,
            'user_id' => Auth::id(),
            'amount' => $amount,
            'currency_id' => $currency_id,
            'status' => ProductOrder::STATUS_PENDING,
        ]);

        foreach ($products as $product) {
            $quantity = 1;
            $poItem = $po->items()->create([
                'product_id' => $product->id,
                'product_snap' => $product,
                'price' => $product->price,
                'quantity' => $quantity,
                'total_price' => $product->price * $quantity,
            ]);
            $poItem->files()->sync($product->files->pluck('id'));
        }
        DB::commit();

        return [
            'data' => $po,
        ];
    }

    private function calcProductAmount($products)
    {
        $amount = $products->sum('price');

        return $amount;
    }

    public function generateOrderNumber($userId): string
    {
        $timestamp = date('ymdhis');
        $randomDigits1 = rand(10, 99);
        $randomDigits2 = rand(10, 99);

        return sprintf('%d%d%d%d', $randomDigits1, $timestamp, $randomDigits2, $userId);
    }

    public function list(Request $request)
    {
        $request->validate([
            'page' => 'integer|min:1',
            'size' => 'integer|min:1,max:50',
            'status' => 'string|in:'.implode(',', [ProductOrder::STATUS_PENDING, ProductOrder::STATUS_PAID]),
        ]);

        $page = $request->input('page', 1);
        $size = $request->input('size', 15);

        $user = Auth::user();
        $query = $user->productOrders();
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }
        $orders = $query
            ->with([
                'items',
                // 'currency',
            ])
            ->paginate($size);

        return [
            'data' => $orders->items(),
            'total' => $orders->total(),
        ];
    }

    public function delete(Request $request)
    {
        $request->validate([
            'order_number' => 'string|max:255|exists:product_orders,order_number',
        ]);

        $user = Auth::user();
        $order = $user->productOrders()->where('order_number', $request->order_number)->first();
        if (! $order) {
            abort(404, 'order not found');
        }

        $order->delete();

        return ['ok' => true];
    }
}
