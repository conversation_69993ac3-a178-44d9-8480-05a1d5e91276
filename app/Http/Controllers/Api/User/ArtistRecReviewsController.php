<?php

namespace App\Http\Controllers\Api\User;

use App\Enums\SystemNotificationScene;
use App\Http\Controllers\Controller;
use App\Models\ArtistRecReview;
use App\Models\WorkTask;
use App\Notifications\SystemNotification;
use App\Service\ArtistService;
use Auth;
use Cache;
use Illuminate\Http\Request;

class ArtistRecReviewsController extends Controller
{
    public function workTaskReview(Request $request)
    {
        $request->validate([
            'work_task_id' => 'required|integer|exists:work_tasks,id',
            'rating_score' => 'required|numeric|between:0,5',
            'rating_content' => 'required',
        ]);

        $user = Auth::user();

        $workTask = $user->workTasks()->find($request->input('work_task_id'));
        if (! $workTask) {
            return response()->json(['msg' => 'Not found'], 404);
        }

        $exists = $workTask->artistRecReview()->exists();
        if ($exists) {
            return response()->json(['msg' => 'Already exists'], 403);
        }

        $artistRecReview = new ArtistRecReview([
            'user_id' => $user->id,
            'artist_id' => $workTask->artist_id,
            'work_task_id' => $workTask->id,
            'type' => $workTask->busable_type,
            'rating_score' => $request->input('rating_score'),
        ]);
        $artistRecReview->setTransByReq('rating_content', $request, true);
        $type = $workTask->busable_type;
        if (in_array($type, [WorkTask::TypeService, WorkTask::TypeProject])) {
            $artistRecReview->{"$type".'_id'} = $workTask->busable_id;
        }
        $artistRecReview->save();

        Cache::delete(ArtistService::getRatingScoreKey($workTask->artist_id));

        // notify
        $notify = SystemNotification::make(SystemNotificationScene::ArtistRecReviewCreated)
            ->setMeta([
                'work_task_id' => $workTask->id,
            ]);
        $workTask->artist->notify($notify);

        return ['ok' => true, 'data' => $artistRecReview];
    }

    public function workTaskReviewInfo(Request $request)
    {
        $request->validate([
            'work_task_id' => 'required|integer|exists:work_tasks,id',
        ]);

        $ret = Auth::user()->artistRecReviews()
            ->where('work_task_id', $request->input('work_task_id'))
            ->first();

        return ['data' => $ret];
    }

    public function deleteWorkTaskReview(Request $request)
    {
        $request->validate([
            'work_task_id' => 'required|integer|exists:work_tasks,id',
        ]);

        $artistRecReview = Auth::user()->artistRecReviews()
            ->where('work_task_id', $request->input('work_task_id'))
            ->first();

        if (! $artistRecReview) {
            return response()->json([
                'msg' => 'Not found',
            ], 403);
        }

        $artistRecReview->update([
            'is_deleted' => true,
        ]);

        Cache::delete(ArtistService::getRatingScoreKey($artistRecReview->artist_id));

        return ['ok' => true];
    }
}
