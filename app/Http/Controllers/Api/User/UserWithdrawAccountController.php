<?php

namespace App\Http\Controllers\Api\User;

use App\Enums\UserWithdrawAccountStatus;
use App\Enums\UserWithdrawAccountType;
use App\Http\Controllers\Controller;
use App\Mail\EmailVerify;
use App\Models\Currency;
use App\Models\EmailVerifyCode;
use Auth;
use Illuminate\Http\Request;
use Mail;

class UserWithdrawAccountController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function list(Request $request)
    {

        $request->validate([
            'type' => 'nullable|string|in:alipay,stripe',
        ]);

        $user = Auth::user();
        $query = $user->withdrawAccounts();

        $query->where('bind_status', UserWithdrawAccountStatus::Successed);

        if ($request->has('type')) {
            $query->where('type', $request->type);
        }

        $query = $query->with('currency');

        $query = $query->orderBy('created_at', 'desc');

        $accounts = $query->get();

        return [
            'data' => $accounts,
        ];
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        $request->validate([
            'alipay_account' => 'required|string',
            'alipay_name' => 'required|string',
            'verify_code' => 'required|string',
        ]);

        $user = Auth::user();

        $code = (int) $request->verify_code;
        $email = $user->email;

        $sent_code = EmailVerifyCode::where('email', $email)
            ->where('code', $code)
            ->where('status', 'sent')
            ->where('type', 'manage_withdraw_account')
            ->first();

        if (! $sent_code) {
            return abort(422, 'Verify code is invalid');
        }
        $sent_code->status = 'verified';
        $sent_code->save();

        $currency = Currency::where('code', 'CNY')->first();

        $user->withdrawAccounts()->create([
            'type' => UserWithdrawAccountType::Alipay,
            'bind_status' => UserWithdrawAccountStatus::Successed,
            'currency_id' => $currency->id,
            'alipay_account' => $request->alipay_account,
            'alipay_name' => $request->alipay_name,
        ]);

        // if wallet not exists, create
        $wallet = $user->wallets()->where('currency_id', $currency->id)->first();
        if (! $wallet) {
            $user->wallets()->create([
                'currency_id' => $currency->id,
                'balance' => 0,
            ]);
        }

        return [
            'ok' => true,
        ];
    }

    /**
     * Remove the specified resource from storage.
     */
    public function delete(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|exists:user_withdraw_accounts,id',
        ]);

        $user = Auth::user();

        $user->withdrawAccounts()->findOrFail($request->id)->delete();

        return [
            'ok' => true,
        ];
    }

    public function sendEmailVerifyCode(Request $request)
    {
        $user = Auth::user();
        $code = random_int(100000, 999999);

        $mail = $user->email;

        Mail::to($mail)->send(new EmailVerify([
            'name' => $user->name,
            'code' => $code,
        ]));

        $old_sign_up_codes = EmailVerifyCode::where('email', $mail)
            ->where('status', 'sent')
            ->where('type', 'manage_withdraw_account')
            ->delete();

        $new_sign_up_code = EmailVerifyCode::create([
            'email' => $mail,
            'code' => $code,
            'type' => 'manage_withdraw_account',
            'status' => 'sent',
        ]);

        return [
            'ok' => true,
        ];
    }
}
