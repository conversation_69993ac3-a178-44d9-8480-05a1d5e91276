<?php

namespace App\Http\Controllers\Api\User;

use App\Enums\ArtistInviteCodeStatus;
use App\Enums\UploadImageBusableType;
use App\Enums\UserApplicantStatus;
use App\Http\Controllers\Controller;
use App\Models\ArtistInviteCode;
use App\Models\UploadImage;
use App\Models\UserApplicant;
use App\Service\UserApplicantService;
use Auth;
use DB;
use Illuminate\Http\Request;

class ArtistApplicationController extends Controller
{
    private function verifyInviteCode(Request $request)
    {
        $user = Auth::user();
        if ($user->roles->pluck('name')->contains('artist')) {
            return abort(403, 'Already an artist');
        }

        $invite_code = ArtistInviteCode::where('code', $request->code)->first();
        if (! $invite_code) {
            abort(404, 'Invite code not found');
        }

        if ($invite_code->status === ArtistInviteCodeStatus::WaitConfirm) {
            abort(403, 'Invite code need confirm');
        }

        if ($invite_code->status !== ArtistInviteCodeStatus::CanUse) {
            abort(403, 'Invite code is not available');
        }

        return $invite_code;
    }

    public function checkCode(Request $request)
    {
        $request->validate([
            'code' => 'required|string',
        ]);

        $invite_code = $this->verifyInviteCode($request);

        $ret = ArtistInviteCode::query()->with([
            'inviterArtist' => function ($query) {
                $query->select('id', 'name', 'avatar_id')->with([
                    'avatar',
                ]);
            },
            'inviterUser' => function ($query) {
                $query->select('id', 'name', 'email');
            },
            'inviteeArtist' => function ($query) {
                $query->select('id', 'name', 'avatar_id')->with([
                    'avatar',
                ]);
            },
            'inviteeUser' => function ($query) {
                $query->select('id', 'name', 'email');
            },
            'inviteCodeType' => [
                'inviteGifts' => [
                    'inviterCurrency',
                    'inviteeCurrency',
                ],
            ],
        ])->find($invite_code->id);

        return [
            'data' => $ret,
        ];
    }

    public function applyByCode(Request $request)
    {
        $request->validate([
            'code' => 'required|string',
        ]);

        $user = Auth::user();

        $invite_code = $this->verifyInviteCode($request);

        if ($invite_code->inviteCodeType && $invite_code->inviteCodeType->need_confirm) {
            $request->merge([
                'invite_code_id' => $invite_code->id,
            ]);

            return $this->applicantArtist($request);
        }

        DB::beginTransaction();
        UserApplicantService::becomeArtist($user, $invite_code);
        DB::commit();

        return response()->json([
            'status' => 'success',
            'message' => 'Apply For Artist Success',
            'code' => 200,
        ], 200);
    }

    public function applicantArtist(Request $request)
    {
        $request->validate([
            'upload_image_ids' => 'required|array',
            'social_id' => 'required|string',
            'social_link' => 'required|string',
            'code' => 'nullable|string',
        ]);

        if ($request->code) {
            $invite_code = $this->verifyInviteCode($request);
        }
        
        $user = Auth::user();
        if ($user->roles->pluck('name')->contains('artist')) {
            return abort(403, 'Already an artist');
        }

        $applicant = $user->applicants()->orderBy('created_at', 'desc')->first();
        if ($applicant) {
            $status = $applicant ? $applicant->status : null;
            if ($status === UserApplicantStatus::Pending) {
                return abort(403, 'Already send application');
            }
        }

        DB::beginTransaction();
        $ua = UserApplicant::create([
            'user_id' => Auth::user()->id,
            'social_id' => $request->social_id,
            'social_link' => $request->social_link,
            'status' => UserApplicantStatus::Pending,
        ]);

        if ($request->code) {
            $invite_code->update([
                'status' => ArtistInviteCodeStatus::WaitConfirm,
            ]);

            $ua->artist_invite_code_id = $invite_code->id;
            $ua->save();
        }

        UploadImage::where('user_id', Auth::user()->id)
            ->where('busable_type', '')
            ->whereIn('id', $request->upload_image_ids)
            ->update([
                'busable_type' => UploadImageBusableType::UserApplicant,
                'busable_id' => $ua->id,
            ]);
        DB::commit();

        return [
            'ok' => true,
        ];
    }

    public function cancelApplicantArtist(Request $request)
    {
        $request->validate([
            'id' => 'required|exists:user_applicants,id',
        ]);

        $ua = UserApplicant::find($request->id);
        if ($ua->status !== UserApplicantStatus::Pending) {
            abort(403, 'Applicant is not pending');
        }

        DB::beginTransaction();
        if ($ua->artistInviteCode && $ua->artistInviteCode->status === ArtistInviteCodeStatus::WaitConfirm) {
            $ua->artistInviteCode->update([
                'status' => ArtistInviteCodeStatus::CanUse,
            ]);
        }

        $ua->update([
            'status' => UserApplicantStatus::Canceled,
        ]);
        DB::commit();

        return [
            'ok' => true,
        ];
    }

    public function applicantArtistList(Request $request)
    {
        $request->validate([
            'status' => 'nullable|string',
        ]);

        $user = Auth::user();

        $query = $user->applicants()->orderBy('created_at', 'desc');
        if ($request->status) {
            $query->where('status', $request->status);
        }
        $applicants = $query->get();

        return [
            'data' => $applicants,
            'total' => $applicants->count(),
        ];
    }
}
