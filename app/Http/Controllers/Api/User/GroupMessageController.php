<?php

namespace App\Http\Controllers\Api\User;

use App\Enums\ErrorCode;
use App\Enums\GroupMessageContentType;
use App\Events\Echo\GroupMessageCreated;
use App\Events\GroupMessageSentEvent;
use App\Http\Controllers\Controller;
use App\Models\Group;
use App\Models\GroupMessage;
use App\Models\GroupUserPivot;
use App\Models\UploadFile;
use App\Service\Translate\TranslateDB;
use App\Service\Translate\TranslationManager;
use App\Service\UploadFileService;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class GroupMessageController extends Controller
{
    public function list(Request $request)
    {
        $request->validate([
            'group_id' => [
                'required',
                'integer',
                Rule::exists('group_user_pivot', 'group_id')->where('user_id', auth()->id()),
            ],
            'ids' => 'required|array',
            'ids.*' => 'integer|exists:group_messages,id',
        ]);

        $ids = $request->input('ids');

        $query = GroupMessage::query();
        $query->where('group_id', $request->input('group_id'))
            ->whereIn('id', $ids);
        $this->withQuery($query);
        $messages = $query->get();

        return [
            'data' => $messages,
        ];
    }

    private function withQuery($query)
    {
        $query->with([
            'translate',
            'attachments',
            'replyMessage',
        ]);
    }

    public function enterGroup(Request $request)
    {
        $request->validate([
            'group_id' => [
                'required',
                'integer',
                Rule::exists('group_user_pivot', 'group_id')->where('user_id', auth()->id()),
            ],
            'size' => 'integer|min:1|max:50',
        ]);

        $size = $request->input('size', 20);

        $groupUserPivot = GroupUserPivot::query()
            ->where('group_id', $request->input('group_id'))
            ->where('user_id', auth()->id())
            ->first();

        $last_id = $groupUserPivot?->last_read_cursor;

        if (! $last_id) {
            $last_id = 1;
            $groupUserPivot->update([
                'last_read_cursor' => $last_id,
            ]);
        }

        $query = GroupMessage::query();
        $query->where('group_id', $request->input('group_id'));
        $this->withQuery($query);

        $nextQuery = $query->clone();
        $prevQuery = $query->clone();
        $currentQuery = $query->clone();

        $nextData = $nextQuery->where('id', '>', $last_id)->orderBy('id')->limit($size)->get();
        $nextData = $nextData->reverse()->values();
        $prevData = $prevQuery->where('id', '<', $last_id)->orderByDesc('id')->limit($size)->get();
        $currentData = $currentQuery->find($last_id);

        if ($nextData->isNotEmpty()) {
            $groupUserPivot->update([
                'last_read_cursor' => $nextData->max('id'),
            ]);
        }

        return [
            'next' => $nextData,
            'prev' => $prevData,
            'current' => $currentData,
        ];
    }

    public function historyMessages(Request $request)
    {
        $request->validate([
            'group_id' => [
                'required',
                'integer',
                Rule::exists('group_user_pivot', 'group_id')->where('user_id', auth()->id()),
            ],
            'cursor' => 'nullable|string',
            'last_id' => 'integer',
            'size' => 'integer|min:1|max:50',
        ]);

        $size = $request->input('size', 20);

        $query = GroupMessage::query();
        $query->where('group_id', $request->input('group_id'));
        if ($request->input('last_id')) {
            $query->where('id', '<', $request->input('last_id'));
        }
        $this->withQuery($query);
        $query->orderByDesc('id');

        $messages = $query->limit($size)->get();

        return [
            'data' => $messages,
            'next_last_id' => $messages->min('id'),
        ];
    }

    public function newMessages(Request $request)
    {
        $request->validate([
            'group_id' => [
                'required',
                'integer',
                Rule::exists('group_user_pivot', 'group_id')->where('user_id', auth()->id()),
            ],
            'cursor' => 'nullable|string',
            'last_id' => 'integer',
            'size' => 'integer|min:1|max:50',
        ]);

        $size = $request->input('size', 20);

        $query = GroupMessage::query();
        $query->where('group_id', $request->input('group_id'));
        if ($request->input('last_id')) {
            $query->where('id', '>', $request->input('last_id'));
        }
        $this->withQuery($query);
        $query->orderBy('id');

        $messages = $query->limit($size)->get();

        $nextLastId = $messages->max('id');
        if ($nextLastId) {
            GroupUserPivot::where('group_id', $request->input('group_id'))
                ->where('user_id', auth()->id())
                ->update(['last_read_cursor' => $nextLastId]);
        }

        return [
            'data' => $messages->reverse()->values(),
            'next_last_id' => $nextLastId,
        ];
    }

    public function send(Request $request)
    {
        $user = auth()->user();

        $request->validate([
            'group_id' => [
                'required',
                'integer',
                Rule::exists('group_user_pivot', 'group_id')->where('user_id', auth()->id()),
            ],
            'content' => '',
            'content_lang' => '',
            'translate_lang' => 'nullable|string',
            'attachments' => 'nullable|array',
            'attachments.*' => 'integer|exists:upload_files,id',
            'reply_message_id' => 'nullable|integer|exists:group_messages,id',
        ]);

        $groupId = $request->input('group_id');
        $replyMsgId = $request->input('reply_message_id');
        $cId = $request->input('c_id');
        

        $message = \DB::transaction(function () use ($user, $request, $groupId, $replyMsgId, $cId) {
            $group = Group::lockForUpdate()->findOrFail($groupId);

            $group->message_count += 1;
            $group->save();

            $newMessageCount = $group->message_count;

            $message = new GroupMessage;
            $message->group_id = $groupId;
            $message->sender_uid = $user->id;
            $message->content_type = GroupMessageContentType::Text;
            $message->setTransByReq('content', $request, true);
            $message->seq_id = $newMessageCount;
            $message->translate_id = null;
            $message->reply_message_id = $replyMsgId;
            $message->c_id = $cId;
            $message->save();

            $event = new GroupMessageSentEvent($message, $request->all(), $user->id);
            $isAsync = false;
            if ($isAsync) {
                event($event);
            } else {
                $event->triggerGroupUserPivot();
                $event->updateLastReadCursor();
                $event->updateAttachments();
                $event->translateMessage(isAsync: false);
            }

            $message->refresh();
            $message->load(['translate', 'attachments', 'replyMessage']);

            GroupMessageCreated::dispatch($message);

            return $message;
        });

        return [
            'data' => $message,
        ];
    }

    public function downloadAttachment(Request $request)
    {
        $request->validate([
            'attachment_id' => 'required|integer|exists:upload_files,id',
        ]);

        $attachment = UploadFile::find($request->input('attachment_id'));
        if (! $attachment) {
            return abort(404, 'attachment not found');
        }

        $temporaryUrl = UploadFileService::downloadTemporaryUrl($attachment);

        return [
            'temporary_url' => $temporaryUrl,
        ];
    }

    public function translateMessage(Request $request)
    {
        $user = auth()->user();

        $request->validate([
            'group_id' => [
                'required',
                'integer',
                Rule::exists('group_user_pivot', 'group_id')->where('user_id', auth()->id()),
            ],
            'message_id' => 'required|integer|exists:group_messages,id',
            'target_langs' => 'required|array',
            'target_langs.*' => 'string',
        ]);

        $messageId = $request->input('message_id');

        $message = GroupMessage::find($messageId);

        $contentRaw = $message->getRawOriginal('content');
        $content = json_decode($contentRaw, true);
        $source_lang = $content['_lang'] ?? 'en';

        $target_langs = $request->input('target_langs');

        $translates = TranslateDB::createTranslatesForMultipleTargetLangs($message, 'content', $source_lang, $target_langs, $user->id);
        $translateIds = collect($translates)->pluck('id');
        TranslationManager::translateColumnByIds($translateIds);

        $message->translate_id = collect($translateIds)->last();
        $message->save();

        $message->refresh();
        $message->load(['translate', 'attachments', 'replyMessage']);

        return [
            'data' => $message,
        ];
    }

    public function undo(Request $request)
    {
        $request->validate([
            'group_id' => [
                'required',
                'integer',
                Rule::exists('group_user_pivot', 'group_id')->where('user_id', auth()->id()),
            ],
            'message_id' => 'required|integer|exists:group_messages,id',
        ]);

        $messageId = $request->input('message_id');

        $message = GroupMessage::find($messageId);

        if ($message->created_at->diffInMinutes() > 3) {
            return response()->json([
                'code' => ErrorCode::GroupMessageUndoTimeLimit->value,
                'message' => 'You can only undo the message within 3 minute after sending it.',
            ], 400);
        }

        $message->is_undo = true;

        $message->save();

        return [
            'data' => $message,
        ];
    }

    public function updateLastReadCursor(Request $request)
    {
        $request->validate([
            'group_id' => [
                'required',
                'integer',
                Rule::exists('group_user_pivot', 'group_id')->where('user_id', auth()->id()),
            ],
            'message_id' => 'required|integer|exists:group_messages,id',
        ]);

        $messageId = $request->input('message_id');

        GroupUserPivot::query()->where('group_id', $request->input('group_id'))
            ->where('user_id', auth()->id())
            ->where('last_read_cursor', '<', $messageId)
            ->update(['last_read_cursor' => $messageId]);

        return [
            'ok' => true,
        ];
    }
}
