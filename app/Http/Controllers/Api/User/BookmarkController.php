<?php

namespace App\Http\Controllers\Api\User;

use App\Http\Controllers\Controller;
use App\Models\Artist;
use App\Models\Artwork;
use App\Models\Product;
use App\Models\Service;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class BookmarkController extends Controller
{
    //

    public function saveArtist(Request $request)
    {
        $request->validate([
            'artist_id' => 'required|integer|min:1',
            'action' => 'required|string|in:save,unsave',
        ]);

        $artist = Artist::where('id', $request->artist_id)->first();
        $user = Auth::user();

        if (! $artist) {
            return response()->json([
                'status' => 'success',
                'message' => 'Artist Not Found',
                'code' => 404,
            ], 404);
        }

        if ($request->action == 'save') {
            if ($user->savedArtists()->where('artists.id', $request->artist_id)->doesntExist()) {
                $user->savedArtists()->syncWithoutDetaching($request->artist_id);
                $artist->followers = $artist->followers + 1;
                $artist->save();

                return response()->json([
                    'status' => 'success',
                    'message' => 'Save Artist Success',
                    'code' => 200,
                ], 200);
            } else {
                return response()->json([
                    'status' => 'success',
                    'message' => 'Already saved',
                    'code' => 202,
                ], 202);
            }
        }

        if ($request->action == 'unsave') {
            if ($user->savedArtists()->where('artists.id', $request->artist_id)->exists()) {
                $user->savedArtists()->detach($request->artist_id);
                $artist->followers = $artist->followers - 1;
                $artist->save();

                return response()->json([
                    'status' => 'success',
                    'message' => 'Unsave Artist Success',
                    'code' => 200,
                ], 200);
            } else {
                return response()->json([
                    'status' => 'success',
                    'message' => 'No saved record',
                    'code' => 202,
                ], 202);
            }
        }
    }

    public function saveArtwork(Request $request)
    {
        $request->validate([
            'artwork_id' => 'required|integer|min:1',
            'action' => 'required|string|in:save,unsave',
        ]);

        $artwork = Artwork::where('id', $request->artwork_id)->first();
        $user = Auth::user();

        if (! $artwork) {
            return response()->json([
                'status' => 'success',
                'message' => 'Artwork Not Found',
                'code' => 404,
            ], 404);
        }

        if ($request->action == 'save') {
            if ($user->savedArtworks()->where('artworks.id', $request->artwork_id)->doesntExist()) {
                $user->savedArtworks()->syncWithoutDetaching($request->artwork_id);
                $artwork->likes = $artwork->likes + 1;
                $artwork->save();

                return response()->json([
                    'status' => 'success',
                    'message' => 'Save Artwork Success',
                    'code' => 200,
                ], 200);
            } else {
                return response()->json([
                    'status' => 'success',
                    'message' => 'Already saved',
                    'code' => 202,
                ], 202);
            }
        }

        if ($request->action == 'unsave') {
            if ($user->savedArtworks()->where('artworks.id', $request->artwork_id)->exists()) {
                $user->savedArtworks()->detach($request->artwork_id);
                $artwork->likes = $artwork->likes - 1;
                $artwork->save();

                return response()->json([
                    'status' => 'success',
                    'message' => 'Unsave Artwork Success',
                    'code' => 200,
                ], 200);
            } else {
                return response()->json([
                    'status' => 'success',
                    'message' => 'No saved record',
                    'code' => 202,
                ], 202);
            }
        }

    }

    public function saveService(Request $request)
    {
        $request->validate([
            'service_id' => 'required|integer|min:1',
            'action' => 'required|string|in:save,unsave',
        ]);

        $service = Service::where('id', $request->service_id)->first();
        $user = Auth::user();

        if (! $service) {
            return response()->json([
                'status' => 'success',
                'message' => 'Service Not Found',
                'code' => 404,
            ], 404);
        }

        if ($request->action == 'save') {
            if ($user->savedServices()->where('services.id', $request->service_id)->doesntExist()) {
                $user->savedServices()->syncWithoutDetaching($request->service_id);
                $service->followers = $service->followers + 1;
                $service->save();

                return response()->json([
                    'status' => 'success',
                    'message' => 'Save Service Success',
                    'code' => 200,
                ], 200);
            } else {
                return response()->json([
                    'status' => 'success',
                    'message' => 'Already saved',
                    'code' => 202,
                ], 202);
            }
        }

        if ($request->action == 'unsave') {
            if ($user->savedServices()->where('services.id', $request->service_id)->exists()) {
                $user->savedServices()->detach($request->service_id);
                $service->followers = $service->followers - 1;
                $service->save();

                return response()->json([
                    'status' => 'success',
                    'message' => 'Unsave Service Success',
                    'code' => 200,
                ], 200);
            } else {
                return response()->json([
                    'status' => 'success',
                    'message' => 'No saved record',
                    'code' => 202,
                ], 202);
            }

        }

    }

    public function saveProduct(Request $request)
    {
        $request->validate([
            'product_id' => 'required|integer|min:1',
            'action' => 'required|string|in:save,unsave',
        ]);

        $product = Product::where('id', $request->product_id)->first();
        $user = Auth::user();

        if (! $product) {
            return response()->json([
                'status' => 'success',
                'message' => 'Product Not Found',
                'code' => 404,
            ], 404);
        }

        if ($request->action == 'save') {
            if ($user->savedProducts()->where('products.id', $request->product_id)->doesntExist()) {
                $user->savedProducts()->syncWithoutDetaching($request->product_id);
                $product->followers = $product->followers + 1;
                $product->save();

                return response()->json([
                    'status' => 'success',
                    'message' => 'Save Product Success',
                    'code' => 200,
                ], 200);
            } else {
                return response()->json([
                    'status' => 'success',
                    'message' => 'Already saved',
                    'code' => 202,
                ], 202);
            }
        }

        if ($request->action == 'unsave') {
            if ($user->savedProducts()->where('products.id', $request->product_id)->exists()) {
                $user->savedProducts()->detach($request->product_id);
                $product->followers = $product->followers - 1;
                $product->save();

                return response()->json([
                    'status' => 'success',
                    'message' => 'Unsave Product Success',
                    'code' => 200,
                ], 200);
            } else {
                return response()->json([
                    'status' => 'success',
                    'message' => 'No saved record',
                    'code' => 202,
                ], 202);
            }

        }
    }

    public function savedArtists(Request $request)
    {
        $request->validate([
            'page' => 'integer|min:1',
            'size' => 'integer|min:1,max:50',
        ]);
        $size = $request->input('size', 15);

        $user = Auth::user();
        $savedArtworks = $user->savedArtists()
            ->withSavedStatus()
            ->with([
                'artworks' => function ($query) {
                    $query->with(['tags', 'uploadImage', 'uploadVideo', 'artStyles', 'categories'])->take(4);
                },
                'avatar',
            ])
            ->orderByPivot('created_at', 'desc')
            ->paginate($size);

        return response()->json([
            'status' => 'success',
            'message' => 'Get Saved Artists success',
            'code' => 200,
            'data' => $savedArtworks->items(),
            'total' => $savedArtworks->total(),
        ], 200);
    }

    public function savedArtworks(Request $request)
    {
        $request->validate([
            'page' => 'integer|min:1',
            'size' => 'integer|min:1,max:50',
        ]);
        $size = $request->input('size', 15);

        $user = Auth::user();
        $savedArtworks = $user->savedArtworks()
            ->withSavedStatus()
            ->with([
                'uploadImage',
                'uploadVideo',
                'artist.avatar',
                'tags',
                'artStyles',
                'categories',
            ])
            ->orderByPivot('created_at', 'desc')
            ->paginate($size);

        return response()->json([
            'status' => 'success',
            'message' => 'Get Saved Artworks success',
            'code' => 200,
            'data' => $savedArtworks->items(),
            'total' => $savedArtworks->total(),
        ], 200);
    }

    public function savedServices(Request $request)
    {
        $request->validate([
            'page' => 'integer|min:1',
            'size' => 'integer|min:1,max:50',
        ]);
        $size = $request->input('size', 15);

        $user = Auth::user();
        $savedServices = $user->savedServices()
            ->withSavedStatus()
            ->with([
                'artist.avatar', 'showcase', 'currency',
            ])
            ->orderByPivot('created_at', 'desc')
            ->paginate($size);

        return response()->json([
            'status' => 'success',
            'message' => 'Get Saved Services success',
            'code' => 200,
            'data' => $savedServices->items(),
            'total' => $savedServices->total(),
        ], 200);
        // return $savedServices;
    }

    public function savedProducts(Request $request)
    {
        $request->validate([
            'page' => 'integer|min:1',
            'size' => 'integer|min:1,max:50',
        ]);
        $size = $request->input('size', 15);

        $user = Auth::user();
        $savedProducts = $user->savedProducts()
            ->withSavedStatus()
            ->with(['currency', 'tags', 'categories', 'showcase'])
            ->orderByPivot('created_at', 'desc')
            ->paginate($size);

        return response()->json([
            'status' => 'success',
            'message' => 'Get Saved Products success',
            'code' => 200,
            'data' => $savedProducts->items(),
            'total' => $savedProducts->total(),
        ], 200);
        // return $savedServices;
    }
}
