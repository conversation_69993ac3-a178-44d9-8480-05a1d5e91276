<?php

namespace App\Http\Controllers\Api\User;

use App\Http\Controllers\Controller;
use App\Models\ProductOrder;
use App\Models\ProductOrderItem;
use App\Service\DownloadService;
use Auth;
use Illuminate\Http\Request;

class ProductOrderItemController extends Controller
{
    public function list(Request $request)
    {
        $request->validate([
            'page' => 'integer|min:1',
            'size' => 'integer|min:1,max:50',
        ]);

        $page = $request->input('page', 1);
        $size = $request->input('size', 15);

        $query = ProductOrderItem::query();

        $query->whereHas('productOrder', function ($query) {
            $query->where('status', ProductOrder::STATUS_PAID)
                ->where('user_id', Auth::id());
        });

        $orders = $query
            ->with([
                'files' => function ($query) {
                    $query->withTrashed();
                },
            ])
            ->paginate($size);

        return [
            'data' => $orders->items(),
            'total' => $orders->total(),
        ];
    }

    public function downloadFile(Request $request)
    {
        $request->validate([
            'product_item_id' => 'integer|min:1',
            'product_item_file_id' => 'integer|min:1',
        ]);

        $item = ProductOrderItem::whereHas('productOrder', function ($query) {
            $query->where('status', ProductOrder::STATUS_PAID)
                ->where('user_id', Auth::id());
        })->where('id', $request->product_item_id)->first();

        if (! $item) {
            return abort(404, 'item not found');
        }

        $file = $item->files()->withTrashed()
            ->where('product_file_id', $request->product_item_file_id)
            ->first();

        if (! $file) {
            return abort(404, 'file not found');
        }

        $key = DownloadService::generateDownloadKey($file->path);

        return [
            'key' => $key,
        ];
    }
}
