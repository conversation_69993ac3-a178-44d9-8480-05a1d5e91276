<?php

namespace App\Http\Controllers\Api\User;

use App\Enums\WalletTransactionAction;
use App\Enums\WalletTransactionBizType;
use App\Enums\WalletTransactionStatus;
use App\Enums\WalletUsage;
use App\Http\Controllers\Controller;
use Auth;
use DB;
use Illuminate\Http\Request;

class WalletController extends Controller
{
    public function list(Request $request)
    {
        $user = Auth::user();

        $ret = $user->wallets()
            ->with(['currency'])
            ->get();

        return [
            'data' => $ret,
        ];
    }

    public function createWithdrew(Request $request)
    {
        $request->validate([
            'wallet_id' => 'required|integer',
            'amount' => 'required|integer',
        ]);

        $user = Auth::user();

        $wallet = $user->wallets()
            ->where('id', $request->wallet_id)
            ->first();

        if (! $wallet) {
            abort(404, 'Wallet not found');
        }

        if ($wallet->balance < $request->amount) {
            abort(400, 'Insufficient balance');
        }

        if ($wallet->usage !== WalletUsage::Deposit) {
            abort(400, 'Wallet usage error');
        }

        DB::beginTransaction();

        $user->walletTransactions()->create([
            'currency_id' => $request->currency_id,
            'action' => WalletTransactionAction::Minus,
            'status' => WalletTransactionStatus::Pending,
            'biz_type' => WalletTransactionBizType::Withdraw,
            'amount' => $request->amount,
            'balance' => $wallet->balance - $request->amount,
            'note' => $request->note,
        ]);

        $wallet->balance -= $request->amount;
        $wallet->save();

        DB::commit();

        return $wallet;
    }
}
