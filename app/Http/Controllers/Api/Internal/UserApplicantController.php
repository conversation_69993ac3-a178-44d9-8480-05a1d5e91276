<?php

namespace App\Http\Controllers\Api\Internal;

use App\Enums\ArtistInviteCodeStatus;
use App\Enums\SystemNotificationScene;
use App\Enums\UserApplicantStatus;
use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\UserApplicant;
use App\Notifications\SystemNotification;
use App\Service\UserApplicantService;
use DB;
use Illuminate\Http\Request;

class UserApplicantController extends Controller
{
    public function accept(Request $request)
    {
        $request->validate([
            'id' => 'required|exists:user_applicants,id',
        ]);

        $userApplicant = UserApplicant::find($request->input('id'));
        if ($userApplicant->status !== UserApplicantStatus::Pending) {
            abort(400, 'Applicant is not pending');
        }

        $user = $userApplicant->user;
        $invite_code = $userApplicant->artistInviteCode;

        DB::beginTransaction();
        UserApplicantService::becomeArtist($user, $invite_code, $userApplicant);

        // 通知 user
        $notify = SystemNotification::make(SystemNotificationScene::UserApplicantArtistAccepted)
            ->setMeta([
                'user_applicant_id' => $userApplicant->id,
            ]);

        $user->notify($notify);

        DB::commit();

        return [
            'ok' => true,
        ];
    }

    public function reject(Request $request)
    {
        $request->validate([
            'id' => 'required|exists:user_applicants,id',
            'reject_content' => 'required',
        ]);

        $userApplicant = UserApplicant::find($request->input('id'));
        if ($userApplicant->status !== UserApplicantStatus::Pending) {
            abort(400, 'Applicant is not pending');
        }

        $rejectContent = $request->input('reject_content');

        DB::beginTransaction();
        $userApplicant->update([
            'status' => UserApplicantStatus::Rejected,
            'reject_content' => $rejectContent,
        ]);

        $userApplicant->artistInviteCode()->update([
            'status' => ArtistInviteCodeStatus::CanUse,
        ]);

        // 通知 user
        $user = $userApplicant->user;
        $notify = SystemNotification::make(SystemNotificationScene::UserApplicantArtistRejected)
            ->setMeta([
                'user_applicant_id' => $userApplicant->id,
                'reject_content' => $rejectContent,
            ]);
        $user->notify($notify);

        DB::commit();

        return [
            'ok' => true,
        ];
    }
}
