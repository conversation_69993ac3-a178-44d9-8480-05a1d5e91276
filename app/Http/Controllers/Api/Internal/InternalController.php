<?php

namespace App\Http\Controllers\Api\Internal;

use App\Http\Controllers\Controller;
use App\Models\ArtistInviteCode;
use App\Models\User;
use App\Models\UserApplicant;
use App\Service\UserApplicantService;
use Illuminate\Http\Request;
use DB;

class InternalController extends Controller
{

    public function becomeArtist(Request $request)
    {
        $request->validate([
            'user_id' => 'required|integer|exists:users,id',
            'invite_code_id' => 'nullable|exists:artist_invite_codes,id',
            'user_applicant_id' => 'nullable|exists:user_applicants,id',
        ]);

        $user = User::find($request->user_id);
        $invite_code = $request->invite_code_id ? ArtistInviteCode::find($request->invite_code_id) : null;
        $userApplicant = $request->user_applicant_id ? UserApplicant::find($request->user_applicant_id) : null;

        DB::beginTransaction();
        UserApplicantService::becomeArtist($user, $invite_code, $userApplicant);
        DB::commit();

        return [
            'ok' => true,
        ];
    }
}
