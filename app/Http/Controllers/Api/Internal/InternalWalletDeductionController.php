<?php

namespace App\Http\Controllers\Api\Internal;

use App\Http\Controllers\Controller;
use App\Models\WalletDeductionRecord;
use App\Service\Stripe\StripeTransferService;
use App\Enums\WalletDeductionStatus;
use Illuminate\Http\Request;
use Exception;

class InternalWalletDeductionController extends Controller
{
    protected $transferService;

    public function __construct(StripeTransferService $transferService)
    {
        $this->transferService = $transferService;
    }

    /**
     * 获取补差记录列表
     */
    public function list(Request $request)
    {
        $request->validate([
            'status' => 'nullable|in:pending,processing,completed,failed,cancelled',
            'user_id' => 'nullable|integer',
            'artist_user_id' => 'nullable|integer',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date',
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:100',
        ]);

        $query = WalletDeductionRecord::with([
            'user:id,name,email',
            'artistUser:id,name,email',
            'currency:id,code,symbol',
            'order:id,busable_type,busable_id',
        ]);

        // 筛选条件
        if ($request->status) {
            $query->where('status', $request->status);
        }

        if ($request->user_id) {
            $query->where('user_id', $request->user_id);
        }

        if ($request->artist_user_id) {
            $query->where('artist_user_id', $request->artist_user_id);
        }

        if ($request->start_date) {
            $query->whereDate('created_at', '>=', $request->start_date);
        }

        if ($request->end_date) {
            $query->whereDate('created_at', '<=', $request->end_date);
        }

        $records = $query->orderBy('created_at', 'desc')
            ->paginate($request->per_page ?? 20);

        return response()->json([
            'data' => $records->items(),
            'pagination' => [
                'current_page' => $records->currentPage(),
                'last_page' => $records->lastPage(),
                'per_page' => $records->perPage(),
                'total' => $records->total(),
            ],
        ]);
    }

    /**
     * 手动补差
     */
    public function manualCompensate(Request $request, $id)
    {
        $record = WalletDeductionRecord::findOrFail($id);

        try {
            $result = $this->transferService->transferToArtist($record);

            return response()->json([
                'success' => true,
                'message' => '补差成功',
                'data' => $result,
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '补差失败: ' . $e->getMessage(),
            ], 422);
        }
    }

    /**
     * 批量处理
     */
    public function batchProcess(Request $request)
    {
        $request->validate([
            'limit' => 'nullable|integer|min:1|max:100',
        ]);

        try {
            $result = $this->transferService->processPendingRecords($request->limit ?? 50);

            return response()->json([
                'success' => true,
                'message' => '批量处理完成',
                'data' => $result,
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '批量处理失败: ' . $e->getMessage(),
            ], 422);
        }
    }

    /**
     * 统计信息
     */
    public function statistics(Request $request)
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date',
        ]);

        $query = WalletDeductionRecord::query();

        if ($request->start_date) {
            $query->whereDate('created_at', '>=', $request->start_date);
        }

        if ($request->end_date) {
            $query->whereDate('created_at', '<=', $request->end_date);
        }

        $statistics = [
            'total_records' => $query->count(),
            'pending_count' => $query->clone()->where('status', WalletDeductionStatus::Pending)->count(),
            'processing_count' => $query->clone()->where('status', WalletDeductionStatus::Processing)->count(),
            'completed_count' => $query->clone()->where('status', WalletDeductionStatus::Completed)->count(),
            'failed_count' => $query->clone()->where('status', WalletDeductionStatus::Failed)->count(),
            'total_amount' => $query->clone()->sum('amount'),
            'completed_amount' => $query->clone()->where('status', WalletDeductionStatus::Completed)->sum('amount'),
            'pending_amount' => $query->clone()->where('status', WalletDeductionStatus::Pending)->sum('amount'),
        ];

        return response()->json([
            'data' => $statistics,
        ]);
    }
}
