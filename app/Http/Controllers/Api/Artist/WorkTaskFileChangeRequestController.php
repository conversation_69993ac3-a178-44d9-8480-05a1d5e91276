<?php

namespace App\Http\Controllers\Api\Artist;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class WorkTaskFileChangeRequestController extends Controller
{
    public function list(Request $request)
    {
        $request->validate([
            'work_task_file_id' => 'required|integer|min:1|exists:work_task_files,id',
        ]);

        $request->validate([
            'page' => 'integer|min:1',
            'size' => 'integer|min:1,max:50',
        ]);

        $page = $request->input('page', 1);
        $size = $request->input('size', 15);

        $user = Auth::user();
        $artist = $user->artist;

        $workTaskFile = $artist->workTaskFiles()->find($request->work_task_file_id);

        if (! $workTaskFile) {
            return response()->json([
                'message' => 'Work task file not found',
            ], 404);
        }

        $ret = $workTaskFile->workTaskFileChangeRequests()->with([
            'uploadImages',
        ])
            ->where('work_task_file_id', $request->work_task_file_id)->paginate($size);

        foreach ($ret->items() as $item) {
            if (! $item->artist_is_read) {
                $item->artist_is_read = true;
                $item->save();
            }
        }

        return [
            'data' => $ret->items(),
            'total' => $ret->total(),
        ];
    }
}
