<?php

namespace App\Http\Controllers\Api\Artist;

use App\Enums\SystemNotificationScene;
use App\Http\Controllers\Controller;
use App\Models\UserRecReview;
use App\Notifications\SystemNotification;
use Auth;
use Illuminate\Http\Request;

class UserRecReviewsController extends Controller
{
    public function createWorkTaskReview(Request $request)
    {
        $request->validate([
            'work_task_id' => 'required|integer|exists:work_tasks,id',
            'type' => 'required|in:service,task,product',
            'rating_score' => 'required|numeric|between:0,5',
            'rating_content' => 'required',
        ]);

        $user = Auth::user();
        $artist = $user->artist;

        $workTask = $artist->workTasks()->find($request->input('work_task_id'));
        if (! $workTask) {
            abort(404, 'Work task not found');
        }

        $exists = $workTask->userRecReview()->exists();
        if ($exists) {
            abort(403, 'Already exists');
        }

        $userRecReview = new userRecReview([
            'user_id' => $workTask->user_id,
            'work_task_id' => $workTask->id,
            'artist_id' => $artist->id,
            'service_id' => $workTask->service_id,
            'rating_score' => $request->input('rating_score'),
            'type' => $request->input('type'),
        ]);
        $userRecReview->setTransByReq('rating_content', $request);
        $userRecReview->save();
        $workTask->userRecReview()->save($userRecReview);

        // notify
        $notify = SystemNotification::make(SystemNotificationScene::UserRecReviewCreated)
            ->setMeta([
                'work_task_id' => $workTask->id,
            ]);
        $workTask->user->notify($notify);

        return ['ok' => true, 'data' => $userRecReview];
    }

    public function workTaskReviewInfo(Request $request)
    {
        $request->validate([
            'work_task_id' => 'required|integer|exists:work_tasks,id',
            'type' => 'required|in:service,task,product',
        ]);

        $user = Auth::user();
        $artist = $user->artist;

        $ret = $artist->userRecReviews()
            ->where('type', $request->input('type'))
            ->where('work_task_id', $request->input('work_task_id'))
            ->first();

        return ['data' => $ret];
    }

    public function deleteWorkTaskReview(Request $request)
    {
        $request->validate([
            'work_task_id' => 'required|integer|exists:work_tasks,id',
            'type' => 'required|in:service,task,product',
        ]);

        $user = Auth::user();
        $artist = $user->artist;

        $artist->userRecReviews()
            ->where('type', $request->input('type'))
            ->where('work_task_id', $request->input('work_task_id'))
            ->update([
                'is_deleted' => true,
            ]);

        return ['ok' => true];
    }
}
