<?php

namespace App\Http\Controllers\Api\Artist;

use App\Enums\SystemNotificationScene;
use App\Http\Controllers\Controller;
use App\Models\ProjectRequest;
use App\Models\WorkTask;
use App\Notifications\SystemNotification;
use Auth;
use DB;
use Illuminate\Http\Request;

class WorkTaskController extends Controller
{
    public function list(Request $request)
    {
        $user = Auth::user();
        $artist = $user->artist;

        $request->validate([
            'busable_type' => 'nullable|in:service,project',
            'page' => 'integer|min:1',
            'size' => 'integer|min:1,max:50',
            'sort_by' => 'nullable|in:id,created_at,updated_at',
            'sort_order' => 'nullable|in:desc,asc',
            'status' => 'nullable|array',
        ]);

        $page = $request->input('page', 1);
        $size = $request->input('size', 15);

        $query = $artist->workTasks();

        if ($request->busable_type) {
            $query->where('busable_type', $request->busable_type);
        }

        $query->with([
            'user:id,name,avatar_id' => [
                'avatar',
            ],
            'workTaskStages',
            'currency',
            'artistRecReview',
            'userRecReview',
        ])
            ->withCount([
                'workTaskFileChangeRequests as unread_change_requests_count' => function ($query) {
                    $query->where('artist_is_read', false);
                },
            ]);

        if ($request->has('status') && $request->status) {
            $query->whereIn('status', $request->status);
        } else {
            $query->whereIn('status', [WorkTask::StatusWorking, WorkTask::StatusFinished]);
        }

        $query->orderBy($request->input('sort_by', 'id'), $request->input('sort_order', 'desc'));

        $ret = $query->paginate($size);

        return ['data' => $ret->items(), 'total' => $ret->total()];
    }

    public function info(Request $request)
    {
        $request->validate([
            'id' => 'integer|min:1',
        ]);

        $ret = WorkTask::with([
            'user:id,name,avatar_id' => [
                'avatar',
            ],
            'workTaskStages',
            'workTaskFiles' => function ($query) {
                $query
                    ->with('uploadFile')
                    ->withCount([
                        'workTaskFileChangeRequests as unread_change_requests_count' => function ($query) {
                            $query->where('artist_is_read', false);
                        },
                    ]);
            },
            'artistRecReview',
            'userRecReview',
            'currency',
            'group',
            'priceChange',
        ])
            ->withCount([
                'workTaskFileChangeRequests as unread_change_requests_count' => function ($query) {
                    $query->where('artist_is_read', false);
                },
            ])
            ->find($request->id);

        return ['data' => $ret];
    }

    public function reject(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|exists:work_tasks,id',
            'reject_type' => 'required',
        ]);

        $workTask = Auth::user()->artist->workTasks()->find($request->input('id'));
        if (! $workTask) {
            abort(404, 'Work task not found');
        }

        if ($workTask->status !== WorkTask::StatusPending) {
            abort(403, 'Can not reject.');
        }

        DB::beginTransaction();
        $workTask->status = WorkTask::StatusArtistRejected;
        $workTask->fill($request->only('reject_type'));
        $workTask->save();

        if ($workTask->busable_type === 'project') {
            $projectRequest = $workTask->reqable;
            $workTasks = $projectRequest->workTasks;

            $count = $workTasks->filter(fn ($workTask) => in_array($workTask->status, [WorkTask::StatusWorking, WorkTask::StatusWaitPay]))->count();
            if ($count <= 0) {
                $projectRequest->status = ProjectRequest::StatusPending;
                $projectRequest->save();
            }
        }

        DB::commit();

        // notify
        $notify = SystemNotification::make(SystemNotificationScene::ProjectRequestArtistRejected)
            ->setMeta([
                'project_request_id' => $workTask->reqable_id,
                'work_task_id' => $workTask->id,
            ]);
        $workTask->user->notify($notify);

        return [
            'ok' => true,
        ];

    }

    public function accept(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|exists:work_tasks,id',
        ]);

        $workTask = Auth::user()->artist->workTasks()->find($request->input('id'));
        if (! $workTask) {
            abort(404, 'Work task not found');
        }

        if ($workTask->status !== WorkTask::StatusPending) {
            abort(403, 'Can not accept.');
        }

        DB::beginTransaction();
        $workTask->status = WorkTask::StatusWaitPay;
        $workTask->save();

        DB::commit();

        // notify
        $notify = SystemNotification::make(SystemNotificationScene::ProjectRequestArtistAccepted)
            ->setMeta([
                'project_request_id' => $workTask->reqable_id,
                'work_task_id' => $workTask->id,
            ]);
        $workTask->user->notify($notify);

        return [
            'ok' => true,
        ];
    }

    public function cancel(Request $request)
    {
        $request->validate([
            'id' => 'integer|min:1|exists:work_tasks,id',
        ]);

        $workTask = Auth::user()->artist->workTasks()->find($request->input('id'));
        if (! $workTask) {
            abort(404, 'Work task not found');
        }

        if (! in_array($workTask->status, [WorkTask::StatusPending, WorkTask::StatusWaitPay])) {
            abort(403, 'Can not cancel.');
        }

        DB::beginTransaction();
        $workTask->status = WorkTask::StatusArtistCanceled;
        $workTask->save();
        DB::commit();

        // notify
        $notify = SystemNotification::make(SystemNotificationScene::WorktaskArtistCanceled)
            ->setMeta([
                'work_task_id' => $workTask->id,
            ]);
        $workTask->user->notify($notify);
        if ($workTask->group) {
            $workTask->group->notify($notify);
        }

        return [
            'ok' => true,
        ];
    }
}
