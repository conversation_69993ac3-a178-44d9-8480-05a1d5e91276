<?php

namespace App\Http\Controllers\Api\Artist;

use App\Enums\TranslateStatus;
use App\Enums\TranslateType;
use App\Enums\UploadImageScene;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ArtistCenterController extends Controller
{
    public function profile()
    {
        $user = Auth::user();
        $artist = $user->artist()->with([
            'avatar',
            'cover',
            'tags',
            'translates' => function ($query) {
                $query->select('id', 'busable_id', 'busable_field', 'source_lang', 'target_lang')
                    ->where('status', TranslateStatus::Pending)
                    ->where('translate_type', TranslateType::Human);
            },
        ])->first();

        return [
            'data' => $artist,
        ];
    }

    public function updateProfile(Request $request)
    {
        $request->validate([
            'name' => 'string|nullable',
            'link_youtube' => 'string|nullable',
            'link_instagram' => 'string|nullable',
            'link_twitter' => 'string|nullable',
            'link_facebook' => 'string|nullable',
            'link_twitch' => 'string|nullable',
            'link_bilibili' => 'string|nullable',
            'link_pixiv' => 'string|nullable',
            'is_open' => 'boolean|nullable',
            'tag_ids' => 'array',
            'tag_ids.*' => 'integer',
        ]);

        $user = Auth::user();
        $artist = $user->artist;

        if ($request->has('intro')) {
            $artist->setTransByReq('intro', $request);
        }
        if ($request->has('contract')) {
            $artist->setTransByReq('contract', $request);
        }
        if ($request->has('tag_ids')) {
            $artist->tags()->sync($request->tag_ids);
        }

        $artist->update($request->only([
            'name',
            'link_youtube', 'link_instagram', 'link_twitter', 'link_facebook',
            'link_twitch', 'link_bilibili', 'link_pixiv',
            'is_open',
        ]));

        return ['ok' => true];
    }

    public function updateCover(Request $request)
    {
        $request->validate([
            'upload_image_id' => 'required|exists:upload_images,id',
        ]);

        $user = Auth::user();
        $artist = $user->artist;

        $upload_image = $user->uploadImages()
            ->where('id', $request->upload_image_id)
            ->where('scene', UploadImageScene::ArtistCover)
            ->first();
        if (! $upload_image) {
            abort(400, 'Upload Image Not Found');
        }

        $artist->cover_id = $upload_image->id;
        $artist->save();

        return ['ok' => true];
    }

    public function updateAvatar(Request $request)
    {
        $request->validate([
            'upload_image_id' => 'required|exists:upload_images,id',
        ]);

        $user = Auth::user();
        $artist = $user->artist;

        $upload_image = $user->uploadImages()
            ->where('id', $request->upload_image_id)
            ->where('scene', UploadImageScene::ArtistAvatar)
            ->first();
        if (! $upload_image) {
            abort(400, 'Upload Image Not Found');
        }

        $artist->avatar_id = $upload_image->id;
        $artist->save();

        return ['ok' => true];
    }
}
