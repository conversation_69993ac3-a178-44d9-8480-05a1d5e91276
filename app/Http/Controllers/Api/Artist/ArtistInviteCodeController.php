<?php

namespace App\Http\Controllers\Api\Artist;

use App\Enums\ArtistInviteCodeStatus;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ArtistInviteCodeController extends Controller
{
    public function list(Request $request)
    {

        $request->validate([
            'page' => 'integer|min:1',
            'size' => 'integer|min:1,max:50',
            'status' => 'array',
            'status.*' => 'string|in:'.ArtistInviteCodeStatus::rules(),
        ]);

        $page = $request->input('page', 1);
        $size = $request->input('size', 15);

        $user = Auth::user();
        $artist = $user->artist;

        $query = $artist->inviterArtistCodes();

        if ($request->input('status')) {
            $query->whereIn('status', $request->input('status'));
        }

        $inviterArtistCodes = $query
            ->with([
                'inviterArtist' => function ($query) {
                    $query->select('id', 'name', 'avatar_id')->with([
                        'avatar',
                    ]);
                },
                'inviterUser' => function ($query) {
                    $query->select('id', 'name', 'email');
                },
                'inviteeArtist' => function ($query) {
                    $query->select('id', 'name', 'avatar_id')->with([
                        'avatar',
                    ]);
                },
                'inviteeUser' => function ($query) {
                    $query->select('id', 'name', 'email');
                },
                'inviteCodeType' => [
                    'inviteGifts' => [
                        'inviterCurrency',
                        'inviteeCurrency',
                    ],
                ],
            ])
            ->orderByDesc('id')
            ->paginate($size);

        return [
            'data' => $inviterArtistCodes->items(),
            'total' => $inviterArtistCodes->total(),
        ];
    }
}
