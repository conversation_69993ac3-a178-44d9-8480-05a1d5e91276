<?php

namespace App\Http\Controllers\Api\Artist;

use App\Enums\SystemNotificationScene;
use App\Http\Controllers\Controller;
use App\Models\WorkTask;
use App\Notifications\SystemNotification;
use App\Service\UploadFileService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class WorkTaskFileController extends Controller
{
    public function create(Request $request)
    {
        $request->validate([
            'upload_file_id' => 'required|exists:upload_files,id',
            'work_task_id' => 'required|exists:work_tasks,id',
            'work_task_stage_id' => 'required|exists:work_task_stages,id',
        ]);

        $user = Auth::user();
        $artist = $user->artist;

        $uploadFile = $user->uploadFiles()->find($request->upload_file_id);
        if (! $uploadFile) {
            abort(404, 'Upload file not found');
        }

        $workTask = $artist->workTasks()->find($request->work_task_id);
        if (! $workTask) {
            abort(404, 'Work task not found');
        }

        $workTaskFile = $artist->workTaskFiles()->create([
            'work_task_id' => $request->get('work_task_id'),
            'work_task_stage_id' => $request->get('work_task_stage_id'),
            'user_id' => $workTask->user_id,
        ]);

        $workTaskFile->uploadFiles()->save($uploadFile);

        // notify
        $notify = SystemNotification::make(SystemNotificationScene::WorktaskFileCreated)
            ->setMeta([
                'work_task_id' => $workTask->id,
            ]);
        $workTask->user->notify($notify);

        return ['ok' => true, 'data' => $workTaskFile];

    }

    public function delete(Request $request)
    {
        $request->validate([
            'id' => 'required|exists:work_task_files,id',
        ]);

        $user = Auth::user();
        $artist = $user->artist;

        $workTaskFile = $artist->workTaskFiles()->find($request->get('id'));
        if (! $workTaskFile) {
            abort(404, 'Work task file not found');
        }

        $workTask = $workTaskFile->workTask;
        if ($workTask->status === WorkTask::StatusFinished) {
            abort(400, 'work task is finished');
        }

        $workTaskFile->delete();

        return ['ok' => true];
    }

    public function download(Request $request)
    {
        $request->validate([
            'id' => 'required|exists:work_task_files,id',
        ]);

        $user = Auth::user();
        $artist = $user->artist;

        $workTaskFile = $artist->workTaskFiles()->find($request->get('id'));
        if (! $workTaskFile) {
            abort(404, 'Work task file not found');
        }

        $temporaryUrl = UploadFileService::downloadTemporaryUrl($workTaskFile->uploadFile);

        return [
            'temporary_url' => $temporaryUrl,
        ];
    }
}
