<?php

namespace App\Http\Controllers\Api\Artist;

use App\Enums\ServiceStatus;
use App\Enums\TranslateStatus;
use App\Enums\TranslateType;
use App\Http\Controllers\Controller;
use App\Models\ArtStyle;
use App\Models\Category;
use App\Models\CreationType;
use App\Models\Right;
use App\Models\RightTemplate;
use App\Models\Service;
use App\Models\ServiceShowcase;
use App\Models\Stage;
use App\Models\StageTemplate;
use App\Models\Tag;
use App\Models\WorkflowType;
use App\Service\ServiceService;
use App\Service\StageService;
use DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class ServiceController extends Controller
{
    public function list(Request $request)
    {
        $user = Auth::user();
        $artist = $user->artist;
        $services = Service::where('artist_id', $artist->id)
            ->with([
                'showcases' => function ($query) {
                    $query->take(1);
                },
                'currency',
            ])
            ->withCount([
                'savedUsers',
                'serviceRequests as unread_service_requests_count' => function ($query) {
                    $query->where('artist_is_read', false);
                },
            ])
            ->orderByDesc('id')->get();

        return [
            'data' => $services,
        ];

    }

    public function info(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|exists:services,id',
        ]);

        $user = Auth::user();
        $artist = $user->artist;
        $service = $artist->services()->where('id', $request->id)->with([
            'showcases',
            'preferTags',
            'cantdoTags',
            'stages' => function ($query) {
                $query->orderByPivot('percent');
            },
            'categories',
            'artStyles',
            'currency',
            'rightTemplate' => [
                'rightPivots:right_template_id,right_id',
            ],
            'translates' => function ($query) {
                $query->select('id', 'busable_id', 'busable_field', 'source_lang', 'target_lang')
                    ->where('status', TranslateStatus::Pending)
                    ->where('translate_type', TranslateType::Human);
            },
        ])->first();

        return [
            'data' => $service,
        ];
    }

    public function update(Request $request)
    {
        $request->validate([
            'id' => 'required|integer',
            'showcase_ids' => 'array',
            'showcase_ids.*' => [
                'integer',
                Rule::exists('service_showcases', 'id')->where('artist_id', Auth::user()->artist->id),
            ],
            'category_ids' => 'array',
            'category_ids.*' => 'integer',
            'art_style_ids' => 'array',
            'art_style_ids.*' => 'integer',
            'workflow_type_id' => 'integer',
            'creation_type_id' => 'integer',
            'right_template_id' => 'integer',
            'prefer_tag_ids' => 'array',
            'prefer_tag_ids.*' => 'integer',
            'cantdo_tag_ids' => 'array',
            'cantdo_tag_ids.*' => 'integer',
        ]);

        DB::beginTransaction();

        $user = Auth::user();
        $artist = $user->artist;
        $service = $artist->services()->where('id', $request->id)->first();
        if (! $service) {
            abort(404, 'Service not found');
        }

        $service->update($request->only([
            'status', 'price', 'plat_fee_type', 'stock',
            'days_need', 'usage', 'review_count', 'rating_score',
            'color_mode', 'size_spec', 'prod_format', 'use_range',
            'currency_id',
        ]));

        if ($request->has('category_ids')) {
            $service->categories()->sync($request->category_ids);
        }
        if ($request->has('art_style_ids')) {
            $service->artStyles()->sync($request->art_style_ids);
        }
        if ($request->has('workflow_type_id')) {
            $service->workflow_type_id = $request->workflow_type_id;
        }
        if ($request->has('creation_type_id')) {
            $service->creation_type_id = $request->creation_type_id;
        }
        if ($request->has('stages')) {
            $service->stages()->sync(StageService::convertToSyncStages($request->stages, 'stage_id'));
        }
        if ($request->has('right_template_id')) {
            $service->right_template_id = $request->right_template_id;
        }
        if ($request->has('prefer_tag_ids')) {
            $service->preferTags()->sync($request->prefer_tag_ids);
        }
        if ($request->has('cantdo_tag_ids')) {
            $service->cantdoTags()->sync($request->cantdo_tag_ids);
        }
        if ($request->has('showcase_ids')) {
            ServiceShowcase::whereNotIn('id', $request->showcase_ids)
                ->where('service_id', $service->id)
                ->update([
                    'service_id' => null,
                ]);
            ServiceShowcase::whereIn('id', $request->showcase_ids)
                ->where('service_id', null)
                ->update([
                    'service_id' => $service->id,
                ]);
            foreach ($request->showcase_ids as $index => $showcase_id) {
                ServiceShowcase::where('service_id', $service->id)
                    ->where('id', $showcase_id)
                    ->update([
                        'sort' => $index,
                    ]);
            }
        }
        if ($request->has('name')) {
            $service->setTransByReq('name', $request);
        }
        if ($request->has('content')) {
            $service->setTransByReq('content', $request);
        }
        $service->save();

        ServiceService::updateServiceBasePrice($service);

        DB::commit();

        return ['ok' => true];
    }

    public function meta()
    {
        $tags = Tag::query()->where(['display' => 1])->get(['id', 'name', 'color']);

        $categories = Category::tree()->get()->toTree();

        $stages = Stage::get(['id', 'name', 'sort', 'is_end_stage']);
        $stage_templates = StageTemplate::with(['stages:id,name'])->get();

        $art_styles = ArtStyle::tree()->get()->toTree();
        $workflow_types = WorkflowType::get();
        $creation_types = CreationType::get();

        $rights = Right::tree()->get()->toTree();

        $right_templates = RightTemplate::with(['rightPivots:right_template_id,right_id'])->get();

        return [
            'data' => [
                'tags' => $tags,
                'categories' => $categories,
                'stages' => $stages,
                'stage_templates' => $stage_templates,
                'art_styles' => $art_styles,
                'workflows_types' => $workflow_types,
                'creation_types' => $creation_types,
                'right_templates' => $right_templates,
                'rights' => $rights,
            ],
        ];

    }

    public function create(Request $request)
    {
        $request->validate([
            'creation_type_id' => 'required|integer|exists:creation_types,id',
            'workflow_type_id' => 'required|integer|exists:workflow_types,id',
            'showcase_ids' => 'array',
            'showcase_ids.*' => [
                'integer',
                Rule::exists('service_showcases', 'id')->where('artist_id', Auth::user()->artist->id),
            ],
            'category_ids' => 'array',
            'category_ids.*' => 'integer',
            'art_style_ids' => 'array',
            'art_style_ids.*' => 'integer',
            'workflow_type_ids' => 'array',
            'workflow_type_ids.*' => 'integer',
            'creation_type_ids' => 'array',
            'creation_type_ids.*' => 'integer',
            'right_template_id' => 'integer',
            'prefer_tag_ids' => 'array',
            'prefer_tag_ids.*' => 'integer',
            'cantdo_tag_ids' => 'array',
            'cantdo_tag_ids.*' => 'integer',
        ]);

        $user = Auth::user();
        $artist = $user->artist;

        $requestData = $request->only([
            'status', 'price', 'plat_fee_type', 'stock',
            'creation_type_id', 'workflow_type_id',
            'days_need', 'usage', 'review_count', 'rating_score',
            'color_mode', 'size_spec', 'prod_format', 'use_range',
        ]);

        DB::beginTransaction();

        $service = new Service;
        $service->fill($requestData);
        $service->artist_id = $artist->id;
        $service->currency_id = $request->currency_id;
        $service->setTransByReq('name', $request, true);
        $service->setTransByReq('content', $request, true);
        $service->save();

        if ($request->has('category_ids')) {
            $service->categories()->sync($request->category_ids);
        }
        if ($request->has('art_style_ids')) {
            $service->artStyles()->sync($request->art_style_ids);
        }
        if ($request->has('workflow_type_ids')) {
            $service->workflowTypes()->sync($request->workflow_type_ids);
        }
        if ($request->has('creation_type_ids')) {
            $service->creationTypes()->sync($request->creation_type_ids);
        }
        if ($request->has('stages')) {
            $service->stages()->sync(StageService::convertToSyncStages($request->stages, 'stage_id'));
        }
        if ($request->has('right_template_id')) {
            $service->right_template_id = $request->right_template_id;
        }
        if ($request->has('showcase_ids')) {
            ServiceShowcase::whereIn('id', $request->showcase_ids)
                ->where('service_id', null)
                ->update([
                    'service_id' => $service->id,
                ]);
            foreach ($request->showcase_ids as $index => $showcase_id) {
                ServiceShowcase::where('service_id', $service->id)
                    ->where('id', $showcase_id)
                    ->update([
                        'sort' => $index,
                    ]);
            }
        }
        if ($request->has('prefer_tag_ids')) {
            $service->preferTags()->sync($request->prefer_tag_ids);
        }
        if ($request->has('cantdo_tag_ids')) {
            $service->cantdoTags()->sync($request->cantdo_tag_ids);
        }

        $service->save();

        ServiceService::updateServiceBasePrice($service);

        DB::commit();

        return ['data' => $service];
    }

    public function delete(Request $request)
    {
        $request->validate([
            'id' => 'required|integer',
        ]);

        $user = Auth::user();
        $artist = $user->artist;
        $service = $artist->services()->where('id', $request->id)->first();

        $service->showcases()->delete();
        $service->delete();

        return ['ok' => true];
    }

    public function updateAllIsOpen(Request $request)
    {
        $request->validate([
            'is_open' => 'required|boolean',
        ]);

        $user = Auth::user();
        $artist = $user->artist;

        $artist->services()->update(['status' => ServiceStatus::fromBool($request->is_open)]);

        return ['ok' => true];
    }
}
