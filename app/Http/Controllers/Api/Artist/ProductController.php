<?php

namespace App\Http\Controllers\Api\Artist;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\Product;
use App\Models\ProductFile;
use App\Models\Stage;
use App\Models\Tag;
use App\Models\TagCategory;
use App\Service\DownloadService;
use Carbon\Carbon;
use DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ProductController extends Controller
{
    public function meta()
    {
        $tags = Tag::where(['display' => 1])->get(['id', 'name', 'color']);

        $categories = Category::tree()->get()->toTree();

        $tagCategories = TagCategory::whereIn('id', [5, 6])->with('tags')->get();

        $stages = Stage::query()->get(['id', 'name', 'sort', 'is_end_stage']);

        return [
            'data' => [
                'tags' => $tags,
                'categories' => $categories,
                'tagCategories' => $tagCategories,
                'stages' => $stages,
            ],
        ];

    }

    public function create(Request $request)
    {
        $user = Auth::user();
        $artist = $user->artist;

        $request->validate([
            'categories' => 'required',
            'tags' => 'required',
            'price' => 'required|integer|min:0',
            'stock' => 'required|integer|min:0',
            'files' => 'required|array',
            'files.*.id' => "required|exists:product_files,id,user_id,{$user->id}",
        ]);

        DB::beginTransaction();
        $product = new Product();
        $product->fill($request->only('price', 'stock', 'plat_fee_type'));
        $product->currency_id = $user->currency_id;
        $product->artist_id = $artist->id;
        $product->setTransByReq('name', $request, true);
        $product->setTransByReq('detail', $request, true);
        $product->save();
        if ($request->has('categories')) {
            $product->categories()->sync($request->categories);
        }
        if ($request->has('tags')) {
            $product->tags()->sync($request->tags);
        }
        if ($request->has('showcases')) {
            $product->showcases()->delete();
            $product->showcases()->createMany($request->showcases);
        }

        foreach ($request->get('files') as $file) {
            $file_id = $file['id'];
            ProductFile::where('id', $file_id)
                ->where('user_id', $user->id)
                ->first()
                ->update(['product_id' => $product->id]);
        }
        DB::commit();

        return [
            'data' => $product,
        ];
    }

    public function list(Request $request)
    {

        $request->validate([
            'page' => 'integer|min:1',
            'size' => 'integer|min:1,max:50',
        ]);

        $page = $request->input('page', 1);
        $size = $request->input('size', 15);

        $user = Auth::user();
        $artist = $user->artist;

        $ret = $artist->products()
            ->with([
                'currency',
                'tags',
                'categories',
                'showcases',
                'files',
            ])
            ->withCount('savedUsers')
            ->orderByDesc('id')->paginate($size);

        return [
            'data' => $ret->items(),
            'total' => $ret->total(),
        ];
    }

    public function info(Request $request)
    {
        $request->validate([
            'id' => 'integer|min:1|exists:products,id',
        ]);

        $user = Auth::user();
        $artist = $user->artist;

        $product = $artist->products()->find($request->id);
        if (! $product) {
            abort(404, 'product not found');
        }

        $ret = $artist->products()->where('id', $request->id)
            ->with([
                'currency',
                'tags',
                'categories',
                'showcases',
                'files',
            ])
            ->first();

        return [
            'data' => $ret,
        ];
    }

    public function update(Request $request)
    {
        $request->validate([
            'id' => 'required',
            'categories' => 'required',
            'tags' => 'required',
            'use_range' => 'required',
            'price' => 'required|integer|min:0',
            'stock' => 'required|integer|min:0',
        ]);

        $user = Auth::user();
        $artist = $user->artist;

        $product = $artist->products()->find($request->id);
        if (! $product) {
            abort(404, 'Product not found');
        }

        $product->fill($request->only('price', 'stock', 'plat_fee_type'));
        if ($request->has('name')) {
            $product->setTransByReq('name', $request);
        }
        if ($request->has('detail')) {
            $product->setTransByReq('detail', $request);
        }
        if ($request->has('categories')) {
            $product->categories()->sync($request->categories);
        }
        if ($request->has('tags')) {
            $product->tags()->sync($request->tags);
        }
        if ($request->has('showcases')) {
            $product->showcases()->delete();
            $product->showcases()->createMany($request->showcases);
        }
        $product->save();

        return ['ok' => true];
    }

    public function delete(Request $request)
    {
        $request->validate([
            'id' => 'integer|min:1|exists:products,id',
        ]);

        $user = Auth::user();
        $artist = $user->artist;

        $product = $artist->products()->find($request->id);
        if (! $product) {
            abort(404, 'product not found');
        }

        $product->showcases()->delete();

        $product->delete();

        return [
            'ok' => true,
        ];

    }

    public function upload_product_file(Request $request)
    {
        $request->validate([
            'name' => '',
            'file' => 'required',
        ]);

        $user = Auth::user();
        $artist = $user->artist;

        $file = $request->file('file');

        $date = Carbon::now();
        $formatedDate = $date->format('Y-m-d');
        $savePath = "product_files/user_{$user->id}/{$formatedDate}/";

        $uniq = uniqid();
        $extension = $file->getClientOriginalExtension();
        $filename = "{$uniq}.{$extension}";

        $filePath = $file->storeAs($savePath, $filename);

        $productFile = new ProductFile();
        $productFile->fill([
            'user_id' => $user->id,
            'artist_id' => $artist->id,
            'path' => $filePath,
        ]);

        if (! $request->has('name')) {
            $request->merge([
                'name' => $file->getClientOriginalName(),
            ]);
        }

        $productFile->setTransByReq('name', $request, true);

        $productFile->save();

        return [
            'ok' => true,
            'data' => $productFile,
        ];
    }

    public function download_product_file(Request $request)
    {
        $request->validate([
            'id' => 'required|exists:product_files,id',
        ]);

        $user = Auth::user();
        $artist = $user->artist;

        $productFile = $artist->productFiles()->find($request->id);

        $path = $productFile->path;

        $key = DownloadService::generateDownloadKey($path);

        return [
            'key' => $key,
        ];
    }
}
