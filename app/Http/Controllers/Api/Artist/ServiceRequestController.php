<?php

namespace App\Http\Controllers\Api\Artist;

use App\Enums\SystemNotificationScene;
use App\Events\Echo\GroupCreated;
use App\Http\Controllers\Controller;
use App\Models\Group;
use App\Models\ServiceRequest;
use App\Models\WorkTask;
use App\Models\WorkTaskStage;
use App\Notifications\SystemNotification;
use App\Service\GroupService;
use App\Service\PaymentService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class ServiceRequestController extends Controller
{
    public function list(Request $request)
    {

        $request->validate([
            'page' => 'integer|min:1',
            'size' => 'integer|min:1,max:50',
            'service_id' => 'integer',
            'status' => 'string|nullable',
        ]);

        $page = $request->input('page', 1);
        $size = $request->input('size', 15);

        $user = Auth::user();
        $artist = $user->artist;

        $query = $artist->serviceRequests()
            ->where('is_artist_deleted', false)
            ->with([
                'user:id,name,avatar_id' => [
                    'avatar',
                ],
                'serviceRequestFiles:id,service_request_id,upload_image_id' => [
                    'uploadImage',
                ],
                'currency',
            ]);

        $service = null;
        if ($request->has('service_id')) {
            $service = $artist->services()->where('id', $request->service_id)->first();
            $query->where('service_id', $request->service_id);
        }
        if ($request->status) {
            $query->where('status', $request->status);
        }

        $ret = $query
            ->orderByDesc('id')
            ->paginate($size);

        return [
            'data' => $ret->items(),
            'total' => $ret->total(),
            'meta' => [
                'service' => $service,
            ],
        ];
    }

    public function info(Request $request)
    {

        $user = Auth::user();
        $artist = $user->artist;

        $ret = $artist->serviceRequests()->where('id', $request->id)
            ->with([
                'user:id,name,avatar_id' => [
                    'avatar',
                ],
                'serviceRequestFiles:id,service_request_id,upload_image_id' => [
                    'uploadImage',
                ],
                'currency',
                'service',
                'workTask' => [
                    'workTaskStages',
                ],
            ])->first();

        if ($ret && ! $ret->artist_is_read) {
            $ret->artist_is_read = true;
            $ret->save();

            $notification = $artist->notifications()
                ->where('data->scene', SystemNotificationScene::ServiceRequestCreated)
                ->where('data->meta->service_request_id', $ret->id)
                ->first();

            if ($notification) {
                $notification->markAsRead();
            }
        }

        return [
            'data' => $ret,
        ];
    }

    public function accept(Request $request)
    {
        $request->validate([
            'id' => 'required|exists:service_requests,id',
            'accept_price' => 'required',
            'accept_days_need' => 'required',
            'accept_start_time' => 'required',
            'accept_content' => '',
            'accept_content_lang' => '',
        ]);

        $user = Auth::user();
        $artist = $user->artist;

        $sr = $artist->serviceRequests()->where('id', $request->get('id'))->first();
        if (! $sr) {
            abort(404, 'service request not found');
        }

        $canUpdateStatus = [ServiceRequest::StatusPending];
        if (! in_array($sr->status, $canUpdateStatus)) {
            abort(403, 'can not accept');
        }

        DB::beginTransaction();

        $sr->status = ServiceRequest::StatusArtistAccepted;
        $sr->fill($request->only('accept_price', 'accept_days_need', 'accept_start_time'));
        $sr->accept_currency_id = $sr->service->currency_id;
        if ($request->has('accept_content')) {
            $sr->setTransByReq('accept_content', $request, true);
        }
        $sr->save();

        $startTime = Carbon::createFromDate($sr->accept_start_time);
        $endTime = $startTime->addDays($sr->accept_days_need + 1)->subSecond();

        // create work_task
        $workTask = new WorkTask;
        $workTask->reqable()->associate($sr);
        $workTask->busable()->associate($sr->service);
        $workTask->fill([
            'artist_id' => $sr->artist_id,
            'user_id' => $sr->user_id,
            'status' => WorkTask::StatusWaitPay,
            'price' => $request->accept_price,
            'currency_id' => $sr->accept_currency_id,
            'deadline' => $endTime->format('Y-m-d H:i:s'),
            'busable_snap' => $sr->service,
            'reqable_snap' => $sr,
        ]);
        $workTask->save();

        $service_stages = $sr->service->stages()->orderByPivot('percent')->get();

        $ps = new PaymentService;
        $stage_amounts = $ps->calcStageAmounts($service_stages, $request->accept_price);

        foreach ($stage_amounts as $stage) {
            $workTask->workTaskStages()->create([
                'type' => WorkTaskStage::TypeService,
                'service_id' => $sr->service_id,
                'name' => $stage->name,
                'percent' => $stage->percent,
                'is_paid' => WorkTaskStage::PayStatusUnpaid,
                'work_status' => WorkTaskStage::WorkStatusPending,
                'amount' => $stage->amount,
            ]);
        }

        // notify user
        $notify = SystemNotification::make()
            ->setScene(SystemNotificationScene::ServiceRequestArtistAccepted)
            ->setMeta([
                'service_request_id' => $sr->id,
                'work_task_id' => $workTask->id,
            ]);
        $sr->user->notify($notify);

        // create group chat
        $gs = new GroupService;
        $group = $gs->createGroupByWorkTask($workTask);
        GroupCreated::dispatch($group);

        DB::commit();

        return ['ok' => true];

    }

    public function reject(Request $request)
    {
        $request->validate([
            'id' => 'required|exists:service_requests,id',
            'reject_type' => 'required',
            'reject_content' => '',
            'reject_content_lang' => '',
        ]);

        $user = Auth::user();
        $artist = $user->artist;

        $sr = $artist->serviceRequests()->where('id', $request->get('id'))->first();
        if (! $sr) {
            abort(404, 'service request not found');
        }

        $canUpdateStatus = [ServiceRequest::StatusPending];
        if (! in_array($sr->status, $canUpdateStatus)) {
            abort(403, 'can not reject');
        }

        $sr->status = ServiceRequest::StatusArtistRejected;
        $sr->fill($request->only('reject_type'));
        if ($request->has('reject_content')) {
            $sr->setTransByReq('reject_content', $request);
        }
        $sr->save();

        // notify user
        $notify = SystemNotification::make()
            ->setScene(SystemNotificationScene::ServiceRequestArtistRejected)
            ->setMeta([
                'service_request_id' => $sr->id,
            ]);
        $sr->user->notify($notify);

        return ['ok' => true, 'data' => $sr];
    }

    public function createWorkTaskFile(Request $request)
    {
        $request->validate([
            'upload_image_id' => 'required|exists:upload_images,id',
            'work_task_id' => 'required|exists:work_tasks,id',
            'work_task_stage_id' => 'required|exists:work_task_stages,id',
        ]);

        $user = Auth::user();
        $artist = $user->artist;

        $uploadImage = $artist->uploadImages()->find($request->upload_image_id);
        if (! $uploadImage) {
            abort(404, 'Upload image not found');
        }

        $workTask = $artist->workTasks()->find($request->work_task_id);
        if (! $workTask) {
            abort(404, 'Work task not found');
        }

        $workTaskFile = $artist->workTaskFiles()->create([
            'work_task_id' => $request->get('work_task_id'),
            'work_task_stage_id' => $request->get('work_task_stage_id'),
            'user_id' => $workTask->user_id,
        ]);

        $workTaskFile->uploadImage()->save($uploadImage);

        return ['ok' => true, 'data' => $workTaskFile];

    }

    public function markRead(Request $request)
    {
        $user = Auth::user();
        $artist = $user->artist;

        $request->validate([
            'ids' => 'required|array',
            'ids.*' => ['required', Rule::exists('service_requests', 'id')->where('artist_id', $artist->id)],
        ]);

        $artist->serviceRequests()->whereIn('id', $request->ids)->update(['artist_is_read' => true]);

        return ['ok' => true];
    }

    public function delete(Request $request)
    {
        $request->validate([
            'id' => 'required|exists:service_requests,id',
        ]);

        $user = Auth::user();
        $artist = $user->artist;

        $sr = $artist->serviceRequests()->where('id', $request->get('id'))->first();
        if (! $sr) {
            abort(404, 'service request not found');
        }

        $canUpdateStatus = [ServiceRequest::StatusArtistRejected, ServiceRequest::StatusUserCanceled];
        if (! in_array($sr->status, $canUpdateStatus)) {
            abort(403, 'can not reject');
        }

        $sr->is_artist_deleted = true;
        $sr->save();

        return ['ok' => true, 'data' => $sr];
    }
}
