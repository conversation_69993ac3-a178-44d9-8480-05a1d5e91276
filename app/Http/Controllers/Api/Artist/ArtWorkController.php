<?php

namespace App\Http\Controllers\Api\Artist;

use App\Enums\UploadImageScene;
use App\Enums\UploadVideoScene;
use App\Http\Controllers\Controller;
use App\Models\ArtStyle;
use App\Models\Artwork;
use App\Models\Category;
use App\Models\ProductShowcase;
use App\Models\ServiceShowcase;
use App\Service\UploadService;
use App\Utils\Utils;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;
use Log;

class ArtWorkController extends Controller
{
    public function list()
    {
        $user = Auth::user();
        $artist = $user->artist;

        $artist_works = Artwork::where('artist_id', $artist->id)
            ->orderBy('sort', 'asc')
            ->orderBy('id', 'desc')
            ->with([
                'tags',
                'uploadImage',
                'uploadVideo',
            ])->get();
        foreach ($artist_works as $work) {
            $work->tag_ids = $work->tags->pluck('id')->toArray();
        }

        return [
            'data' => $artist_works,
        ];
    }

    public function info(Request $request)
    {
        $request->validate([
            'id' => 'required|exists:artworks,id',
        ]);

        $user = Auth::user();
        $artist = $user->artist;

        $artwork = Artwork::where('artist_id', $artist->id)
            ->where('id', $request->id)
            ->with([
                'tags',
                'uploadImage',
                'uploadVideo',
                'categories',
                'artStyles',
            ])->first();

        $artwork->tag_ids = $artwork->tags->pluck('id')->toArray();

        return [
            'data' => $artwork,
        ];
    }

    public function update(Request $request)
    {
        $request->validate([
            'id' => 'required|exists:artworks,id',
            'is_show' => 'boolean',
            'video_preview_image' => 'nullable|string',
            'category_ids' => 'array',
            'category_ids.*' => 'integer',
            'tag_ids' => 'array',
            'tag_ids.*' => 'integer',
            'art_style_ids' => 'array',
            'art_style_ids.*' => 'integer',
        ]);

        $user = Auth::user();
        $artist = $user->artist;

        $artwork = Artwork::where('id', $request->id)->where('artist_id', $artist->id)->first();
        if (! $artwork) {
            abort(404, 'artwork not found');
        }

        if ($request->has('tag_ids')) {
            $artwork->tags()->sync($request->tag_ids);
        }
        if ($request->has('art_style_ids')) {
            $artwork->artStyles()->sync($request->art_style_ids);
        }
        if ($request->has('title')) {
            $artwork->setTransByReq('title', $request);
        }
        if ($request->has('detail')) {
            $artwork->setTransByReq('detail', $request);
        }
        if ($request->has('category_ids')) {
            $artwork->categories()->sync($request->category_ids);
        }

        $artwork->update($request->only(['is_show', 'video_preview_image']));
        $artwork->save();

        return ['ok' => true];
    }

    public function updateSort(Request $request)
    {
        $user = Auth::user();
        $artist = $user->artist;

        $sorted_ids = $request['sorted_ids'];

        foreach ($sorted_ids as $index => $id) {
            $artwork = Artwork::where('id', $id)->where('artist_id', $artist->id)
                ->first();
            if (! $artwork) {
                continue;
            }
            $artwork->update(['sort' => $index]);
        }

        return ['ok' => true];
    }

    public function create(Request $request)
    {
        $request->validate([
            'upload_type' => 'string|in:upload_image,upload_video',
            'upload_image_id' => [
                'required_if:upload_type,upload_image',
                'integer',
                Rule::exists('upload_images', 'id')
                    ->where('user_id', Auth::id())
                    ->where('scene', UploadImageScene::ArtistWorks),
            ],
            'upload_video_id' => [
                'required_if:upload_type,upload_video',
                'integer',
                Rule::exists('upload_videos', 'id')
                    ->where('user_id', Auth::id())
                    ->where('scene', UploadVideoScene::ArtistWorks),
            ],
            'tag_ids' => 'array',
            'tag_ids.*' => 'integer',
            'category_ids' => 'array',
            'category_ids.*' => 'integer',
            'art_style_ids' => 'array',
            'art_style_ids.*' => 'integer',
            'title' => '',
            'detail' => '',
            'is_show' => 'boolean',
        ]);

        $user = Auth::user();
        $artist = $user->artist;

        $newArtwork = [
            'artist_id' => $artist->id,
            'upload_type' => $request->get('upload_type', 'upload_image'),
            'upload_image_id' => $request->upload_image_id,
            'upload_video_id' => $request->upload_video_id,
            'video_preview_image' => $request->video_preview_image,
        ];
        $artwork = Artwork::create($newArtwork);

        if ($request->has('tag_ids')) {
            $artwork->tags()->sync($request->tag_ids);
        }
        if ($request->has('title')) {
            $artwork->setTransByReq('title', $request, true);
        }
        if ($request->has('detail')) {
            $artwork->setTransByReq('detail', $request, true);
        }
        if ($request->has('category_ids')) {
            $artwork->categories()->sync($request->category_ids);
        }
        if ($request->has('art_style_ids')) {
            $artwork->artStyles()->sync($request->art_style_ids);
        }

        $artwork->update($request->only(['is_show']));
        $artwork->save();

        return [
            'data' => $artwork,
        ];
    }

    public function createByLink(Request $request)
    {
        $request->validate([
            'upload_type' => 'required|string|in:youtube_link,bilibili_link',
            'video_url' => 'required|string',
            'vid' => 'required|string',
            'video_preview_image' => 'string',
        ]);

        $user = Auth::user();
        $artist = $user->artist;

        $newArtwork = [
            'artist_id' => $artist->id,
            'upload_type' => $request->upload_type,
            'video_url' => $request->video_url,
        ];

        if ($request->has('video_preview_image')) {
            $newArtwork['video_preview_image'] = $request->video_preview_image;
        } else {
            $newArtwork['video_preview_image'] = Utils::videoPreviewImage($request->upload_type, $request->vid);
        }

        $artwork = Artwork::create($newArtwork);

        if ($request->has('tag_ids')) {
            $artwork->tags()->sync($request->tag_ids);
        }
        if ($request->has('title')) {
            $artwork->setTransByReq('title', $request, true);
        }
        if ($request->has('detail')) {
            $artwork->setTransByReq('detail', $request, true);
        }
        if ($request->has('category_ids')) {
            $artwork->categories()->sync($request->category_ids);
        }
        if ($request->has('art_style_ids')) {
            $artwork->artStyles()->sync($request->art_style_ids);
        }

        $artwork->update($request->only(['is_show']));
        $artwork->save();

        return [
            'data' => $artwork,
        ];
    }

    public function delete(Request $request)
    {

        $request->validate([
            'id' => 'required|exists:artworks,id',
        ]);

        $user = Auth::user();
        $artist = $user->artist;

        $serviceShowcaseExists = ServiceShowcase::where('type', 'artwork')
            ->where('artwork_id', $request->id)
            ->where('service_id', '!=', null)
            ->first();
        if ($serviceShowcaseExists) {
            Log::info('该作品正在使用中 service '.$serviceShowcaseExists->id);

            return response()->json(['message' => '该作品正在使用中'], 403);
        }

        $productShowcaseExists = ProductShowcase::where('type', 'artwork')
            ->where('artwork_id', $request->id)
            ->where('product_id', '!=', null)
            ->first();
        if ($productShowcaseExists) {
            Log::info('该作品正在使用中 product '.$productShowcaseExists->id);

            return response()->json(['message' => '该作品正在使用中'], 403);
        }

        Log::info('删除作品', ['id' => $request->id]);
        $artist_work = Artwork::where('id', $request->id)->where('artist_id', $artist->id)->first();
        $artist_work->delete();

        if ($artist_work->is_used_asset === 0) {
            UploadService::deleteUploadImage($artist_work->upload_image_id);
            UploadService::deleteUploadVideo($artist_work->upload_video_id);
        }

        return ['ok' => true];
    }

    public function meta()
    {
        $categories = Category::tree()->get()->toTree();
        $art_styles = ArtStyle::tree()->get()->toTree();

        return [
            'data' => [
                'categories' => $categories,
                'art_styles' => $art_styles,
            ],
        ];

    }
}
