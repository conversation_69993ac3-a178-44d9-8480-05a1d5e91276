<?php

namespace App\Http\Controllers\Api\Artist;

use App\Enums\SystemNotificationScene;
use App\Http\Controllers\Controller;
use App\Models\Project;
use App\Models\ProjectRequest;
use App\Models\WorkTask;
use App\Notifications\SystemNotification;
use Auth;
use DB;
use Illuminate\Http\Request;

class ProjectRequestController extends Controller
{
    public function create(Request $request)
    {
        $request->validate([
            'project_id' => 'required|integer|exists:projects,id',
            'currency_id' => 'required|integer|exists:currencies,id',
            'detail' => 'required',
            'artworks' => 'required|array|max:5',
            'budget' => 'required',
            'days_need' => 'required|integer',
        ]);

        $user = Auth::user();
        $artist = $user->artist;

        $project = Project::where('is_private', false)->find($request->project_id);
        if ($project->user_id === $user->id) {
            abort(400, 'Can not request your own project.');
        }

        $exists = $artist->projectRequests()
            ->where('project_id', $project->id)
            ->whereIn('status', [ProjectRequest::StatusPending, ProjectRequest::StatusUserChosen])
            ->exists();
        if ($exists) {
            abort(400, 'Already requested. Please cancel first.');
        }

        DB::beginTransaction();

        $pr = $artist->projectRequests()->create([
            'user_id' => $project->user_id,
            'project_id' => $project->id,
            'currency_id' => $request->input('currency_id'),
            'budget' => $request->input('budget'),
            'days_need' => $request->input('days_need'),
            'status' => ProjectRequest::StatusPending,
        ]);

        $pr->setTransByReq('detail', $request, true);
        $pr->save();

        $artworks = $artist->artworks()->whereIn('id', $request->artworks)->get();
        $pr->artworks()->sync($artworks);

        DB::commit();

        // notify
        $notify = SystemNotification::make()
            ->setScene(SystemNotificationScene::ProjectRequestCreated)
            ->setMeta([
                'project_request_id' => $pr->id,
            ]);
        $pr->user->notify($notify);
        $pr->artist->notify($notify);

        return ['ok' => true, 'data' => $pr];
    }

    public function cancel(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|exists:project_requests,id',
        ]);

        $user = Auth::user();
        $artist = $user->artist;

        $pr = $artist->projectRequests()->find($request->input('id'));
        if (! $pr) {
            abort(404, 'Project request not found');
        }

        $workTasks = $pr->workTasks;
        $count = $workTasks->filter(fn ($workTask) => in_array($workTask->status, [WorkTask::StatusWorking, WorkTask::StatusWaitPay]))->count();
        if ($count > 0) {
            abort(400, 'Can not cancel.');
        }

        $pr->status = ProjectRequest::StatusArtistCanceled;
        $pr->save();

        // notify
        $notify = SystemNotification::make()
            ->setScene(SystemNotificationScene::ProjectRequestArtistCanceled)
            ->setMeta([
                'project_request_id' => $pr->id,
            ]);
        $pr->user->notify($notify);

        return ['ok' => true];
    }

    public function list(Request $request)
    {
        $request->validate([
            'page' => 'integer|min:1',
            'size' => 'integer|min:1,max:50',
        ]);

        $page = $request->input('page', 1);
        $size = $request->input('size', 15);

        $user = Auth::user();
        $artist = $user->artist;

        $projectRequests = $artist->projectRequests()->with([
            'currency',
            'artist:id,name,avatar_id' => [
                'avatar',
            ],
            'artworks',
            'project' => function ($query) {
                $query->withCount(['projectRequests'])
                    ->with([
                        'categories',
                        'currency',
                        'examples',
                    ]);
            },
        ])->orderByDesc('id')
            ->paginate($size);

        return [
            'data' => $projectRequests->items(),
            'total' => $projectRequests->total(),
        ];
    }

    public function list_by_chosen(Request $request)
    {
        $request->validate([
            'page' => 'integer|min:1',
            'size' => 'integer|min:1,max:50',
        ]);

        $page = $request->input('page', 1);
        $size = $request->input('size', 15);

        $user = Auth::user();
        $artist = $user->artist;

        $projectRequests = $artist->projectRequests()
            ->where('status', ProjectRequest::StatusUserChosen)
            ->with([
                'currency',
                'artist:id,name,avatar_id' => [
                    'avatar',
                ],
                'artworks',
                'project' => [
                    'categories',
                    'currency',
                    'examples',
                ],
                'workTasks',
                'workTasks.currency',
            ])
            ->orderByDesc('id')
            ->paginate($size);

        return [
            'data' => $projectRequests->items(),
            'total' => $projectRequests->total(),
        ];
    }
}
