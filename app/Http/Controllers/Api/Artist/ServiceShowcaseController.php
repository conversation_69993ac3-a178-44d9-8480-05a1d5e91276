<?php

namespace App\Http\Controllers\Api\Artist;

use App\Enums\UploadImageScene;
use App\Enums\UploadVideoScene;
use App\Http\Controllers\Controller;
use App\Models\ServiceShowcase;
use App\Utils\Utils;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class ServiceShowcaseController extends Controller
{
    public function create(Request $request)
    {
        $request->validate([
            'type' => 'required|in:artwork,upload_image,youtube_link,bilibili_link,upload_video',
            'artwork_ids' => 'array|required_if:type,artwork',
            'artwork_ids.*' => [
                Rule::exists('artworks', 'id')->where('artist_id', Auth::user()->artist->id),
            ],
            'upload_image_id' => [
                'required_if:type,upload_image',
                Rule::exists('upload_images', 'id')
                    ->where('scene', UploadImageScene::ServiceShowcases)
                    ->where('user_id', Auth::id()),
            ],
            'video_url' => 'required_if:type,youtube_link,bilibili_link',
            'vid' => 'required_if:type,youtube_link,bilibili_link',
            'video_preview_image' => 'string',
            'upload_video_id' => [
                'required_if:type,upload_video',
                'integer',
                Rule::exists('upload_videos', 'id')
                    ->where('user_id', Auth::id())
                    ->where('scene', UploadVideoScene::ServiceShowcases),
            ],
        ]);

        $user = Auth::user();
        $artist = $user->artist;

        $showcase = new ServiceShowcase();
        $showcase->artist_id = $artist->id;
        $showcase->type = $request->type;
        if ($request->has('video_preview_image')) {
            $showcase->video_preview_image = $request->video_preview_image;
        } else {
            $showcase->video_preview_image = Utils::videoPreviewImage($request->type, $request->vid);
        }

        $showcases = [];

        if ($request->type == 'artwork') {
            foreach ($request->artwork_ids as $artwork_id) {
                $tmp = $showcase->replicate();
                $tmp->artwork_id = $artwork_id;
                $tmp->save();
                $showcases[] = $tmp;
            }
        } elseif ($request->type == 'upload_image') {
            $tmp = $showcase->replicate();
            $tmp->upload_image_id = $request->upload_image_id;
            $tmp->save();
            $showcases[] = $tmp;
        } elseif ($request->type == 'youtube_link' || $request->type == 'bilibili_link') {
            $tmp = $showcase->replicate();
            $tmp->video_url = $request->video_url;
            $tmp->save();
            $showcases[] = $tmp;
        } elseif ($request->type == 'upload_video') {
            $tmp = $showcase->replicate();
            $tmp->upload_video_id = $request->upload_video_id;
            $tmp->save();
            $showcases[] = $tmp;
        }

        $showcases = collect($showcases)->map(function (ServiceShowcase $showcase) {
            $showcase->load([
                'artwork' => [
                    'uploadImage',
                    'uploadVideo',
                ],
                'uploadImage:id,url_sm,url_md,url_lg',
                'uploadVideo:id,url_sm,url_md,url_lg',
            ]);

            return $showcase;
        });

        return ['data' => $showcases];
    }

    public function delete(Request $request)
    {
        $request->validate([
            'id' => 'required|integer',
        ]);

        $user = Auth::user();
        $artist = $user->artist;

        $showcase = $artist->showcases()->where('id', $request->id)->first();

        if (! $showcase) {
            abort(404, 'Not Found Showcase');
        }

        $showcase->delete();

        return ['ok' => true];
    }
}
