<?php

namespace App\Http\Controllers\Api\Artist;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ProductOrderController extends Controller
{
    public function list(Request $request)
    {
        $request->validate([
            'page' => 'integer|min:1',
            'size' => 'integer|min:1,max:50',
        ]);

        $page = $request->input('page', 1);
        $size = $request->input('size', 15);

        $user = Auth::user();
        $artist = $user->artist;

        $orders = $artist->productOrders()
            ->with([
                'items',
                // 'currency',
            ])
            ->paginate($size);

        return [
            'data' => $orders->items(),
            'total' => $orders->total(),
        ];
    }
}
