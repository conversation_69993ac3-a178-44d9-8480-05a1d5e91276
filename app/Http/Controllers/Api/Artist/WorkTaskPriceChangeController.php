<?php

namespace App\Http\Controllers\Api\Artist;

use App\Enums\SystemNotificationScene;
use App\Http\Controllers\Controller;
use App\Models\WorkTaskPriceChange;
use App\Notifications\SystemNotification;
use App\Service\WorkTaskPriceChangeService;
use DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class WorkTaskPriceChangeController extends Controller
{
    public function list(Request $request)
    {
        $request->validate([
            'page' => 'integer|min:1',
            'size' => 'integer|min:1,max:50',
            'work_task_id' => 'required|exists:work_tasks,id',
        ]);

        $page = $request->input('page', 1);
        $size = $request->input('size', 15);

        $user = Auth::user();
        $artist = $user->artist;
        $query = $artist->workTaskPriceChanges();

        if ($request->input('work_task_id')) {
            $query->where('work_task_id', $request->input('work_task_id'));
        }

        $ret = $query->paginate($size);

        return [
            'data' => $ret->items(),
            'total' => $ret->total(),
        ];
    }

    public function info(Request $request)
    {
        $request->validate([
            'id' => 'required|exists:work_task_price_changes,id',
        ]);

        $user = Auth::user();
        $artist = $user->artist;
        $priceChange = $artist->workTaskPriceChanges()->find($request->input('id'));

        if (! $priceChange) {
            abort(404, 'Price change not found');
        }

        return [
            'data' => $priceChange,
        ];
    }

    public function create(Request $request)
    {
        $request->validate([
            'work_task_id' => 'required|exists:work_tasks,id',
            'price' => 'required|numeric|min:0',
        ]);

        $user = Auth::user();
        $artist = $user->artist;

        $workTask = $artist->workTasks()->find($request->input('work_task_id'));
        if (! $workTask) {
            abort(404, 'Work task not found');
        }

        // 检查是否有未完成的改价申请
        $pendingChange = $workTask->priceChanges()
            ->whereIn('status', ['pending', 'wait_pay'])
            ->first();
        if ($pendingChange) {
            abort(400, 'has pending price change');
        }

        $oldPrice = $workTask->price;
        $paidAmount = $workTask->paid_amount;
        $newPrice = $request->input('price');

        if ($paidAmount > $newPrice) {
            abort(400, 'paid amount is greater than new price');
        }

        DB::beginTransaction();

        $priceChange = WorkTaskPriceChangeService::Create($workTask, $newPrice, $artist);
        if ($newPrice < $oldPrice) {
            $priceChange = WorkTaskPriceChangeService::Approve($priceChange, $user);
            $priceChange = WorkTaskPriceChangeService::Paid($priceChange, $paidAmount);
        }

        // notify
        $workTask = $priceChange->workTask;
        $notify = SystemNotification::make(SystemNotificationScene::WorkTaskPriceChangeArtistCreated)
            ->setMeta([
                'work_task_id' => $priceChange->workTask->id,
                'price_change_id' => $priceChange->id,
            ]);

        $workTask->artist->notify($notify);
        $workTask->user->notify($notify);
        $workTask->group->notify($notify);

        DB::commit();

        return [
            'data' => $priceChange,
        ];

    }

    public function approve(Request $request)
    {
        $request->validate([
            'id' => 'required|exists:work_task_price_changes,id',
        ]);

        $priceChange = WorkTaskPriceChange::query()
            ->where('initiator_type', 'user')
            ->where('id', $request->input('id'))->first();
        if (! $priceChange) {
            abort(404, 'Price change not found');
        }

        $user = Auth::user();
        $artist = $user->artist;
        $paidAmount = $priceChange->workTask->paid_amount;

        DB::beginTransaction();
        $priceChange = WorkTaskPriceChangeService::Approve($priceChange, $artist);
        if ($priceChange->new_price < $priceChange->old_price) {
            $priceChange = WorkTaskPriceChangeService::Paid($priceChange, $paidAmount);
        }
        DB::commit();

        // notify
        $workTask = $priceChange->workTask;
        $notify = SystemNotification::make(SystemNotificationScene::WorkTaskPriceChangeWaitPay)
            ->setMeta([
                'work_task_id' => $priceChange->workTask->id,
                'price_change_id' => $priceChange->id,
            ]);
        $workTask->user->notify($notify);
        $workTask->group->notify($notify);

        return [
            'data' => $priceChange,
        ];
    }

    public function reject(Request $request)
    {
        $request->validate([
            'id' => 'required|exists:work_task_price_changes,id',
        ]);

        $priceChange = WorkTaskPriceChange::query()
            ->where('initiator_type', 'user')
            ->where('id', $request->input('id'))->first();
        if (! $priceChange) {
            abort(404, 'Price change not found');
        }

        $priceChange = WorkTaskPriceChangeService::Reject($priceChange);

        // notify
        $workTask = $priceChange->workTask;
        $notify = SystemNotification::make(SystemNotificationScene::WorkTaskPriceChangeRejected)
            ->setMeta([
                'work_task_id' => $priceChange->workTask->id,
                'price_change_id' => $priceChange->id,
            ]);
        $workTask->artist->notify($notify);
        $workTask->user->notify($notify);
        $workTask->group->notify($notify);

        return [
            'data' => $priceChange,
        ];
    }

    public function cancel(Request $request)
    {
        $request->validate([
            'id' => 'required|exists:work_task_price_changes,id',
        ]);

        $user = Auth::user();
        $artist = $user->artist;
        $priceChange = $artist->workTaskPriceChanges()->find($request->input('id'));
        if (! $priceChange) {
            abort(404, 'Price change not found');
        }

        $priceChange = WorkTaskPriceChangeService::Cancel($priceChange);

        // notify
        $workTask = $priceChange->workTask;
        $notify = SystemNotification::make(SystemNotificationScene::WorkTaskPriceChangeCanceled)
            ->setMeta([
                'work_task_id' => $priceChange->workTask->id,
                'price_change_id' => $priceChange->id,
            ]);
        $workTask->artist->notify($notify);
        $workTask->user->notify($notify);
        $workTask->group->notify($notify);

        return [
            'data' => $priceChange,
        ];
    }
}
