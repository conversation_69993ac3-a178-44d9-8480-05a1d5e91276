<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

use App\Models\Commission;
use App\Models\Artist;
use App\Models\ComPrefArtist;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\OrderPayment;
use App\Models\User;
use App\Models\ComAvaArtist;
use App\Models\ComReview;
use App\Models\Product;
use App\Models\ProductAttribute;
use App\Models\ProductAttributeOption;
use App\Models\ProductSkuItem;


class BackendController extends Controller
{


  public function commissions(){
    $user = Auth::user();
    if ($user->role_id == 1) {
      $commissions = Commission::orderBy('created_at','DESC')->get();
      $commissions = Commission::statusRead($commissions);
      $commissions = Commission::getUser($commissions);
      return view('backend/commissions',compact('commissions'));
    }
    else{
     return 'permission denied.';
    }
  }


  public function commissionDetail($com_id){
    $user = Auth::user();
    $commission = Commission::where('id',$com_id)->first();
    $commission = Commission::statusRead($commission,0);
    $commission->detail_objs = json_decode($commission->detail);
    $commission->ref_files = json_decode($commission->ref_files);
    if($user->role_id == 1){
      $pref_artists = Artist::join('com_pref_artists','com_pref_artists.artist_id','=','artists.id')
                            ->where('com_pref_artists.com_id',$com_id)
                            ->select('artists.*')
                            ->orderBy('sort')
                            ->get();
      $ava_artists = Artist::join('com_ava_artists','com_ava_artists.artist_id','=','artists.id')
                          ->where('com_ava_artists.com_id',$com_id)
                          // ->select('artists.*','com_ava_artists.*')
                          ->get();
      
      $order = Order::where('com_id',$com_id)->first();
      if($order){
        $order_items = OrderItem::where('order_id',$order->id)->get();
        $order_payments = OrderPayment::where('order_id',$order->id)->get();
        $order_payments = OrderPayment::statusRead($order_payments);
      }
      else{
        $order_items = [];
        $order_payments = [];
      }

      if($commission->status >= 9){
        foreach($ava_artists as $ava_artist){
          $review = ComReview::where('com_id',$com_id)->where('artist_id', $ava_artist->artist_id)->first();
          $ava_artist->review = $review;
        }
      }

      $pm = User::where('id',$commission->pm_user_id)->first();
      if($pm){
        $pm->review = ComReview::where('com_id',$com_id)->where('pm_user_id', $pm['id'])->first();
      }

      
      return view('backend/com_detail',compact('commission','pref_artists','ava_artists','order','order_items','order_payments','pm'));
   }
   else{
     return redirect('/backend/commissions');
   }
  }

  public function deleteAvaArtist(Request $request){
    $user = Auth::user();
    if($user->role_id == 1){
      $delete_id = $request->id;
      ComAvaArtist::where('id',$delete_id)->first()->delete();
      $return_json =  array('status'=>'success','message'=>'delete success','code'=>200);
      return $return_json;
    }
  }

  public function addAvaArtist(Request $request){
    $user = Auth::user();
    if($user->role_id == 1){
      $new_ava_art_data = array('com_id'=>$request['com_id'],'user_id'=>$request['user_id'],
                                'artist_id'=>$request['artist_id'],'role'=>$request['role'],
                                'price_est'=>$request['price_est'],'start_date'=>$request['start_date'],'time_need'=>$request['time_need']);
      ComAvaArtist::create($new_ava_art_data);
      
      $return_json =  array('status'=>'success','message'=>'add success','code'=>200);
      return $return_json;
    }
  }

  public function saveAvaArtist(Request $request){
    $user = Auth::user();
    if($user->role_id == 1){
      $ava_art_data = array('price_est'=>$request['price_est'],'start_date'=>$request['start_date'],'time_need'=>$request['time_need'],'role'=>$request['role']);
      // ComAvaArtist::update($new_ava_art_data);
      $com_ava_artist = ComAvaArtist::where('id',$request['id'])->first();
      $com_ava_artist->update($ava_art_data);
      $return_json =  array('status'=>'success','message'=>'update success','code'=>200);
      return $return_json;
    }
  }

  public function deleteOrderItem(Request $request){
    $user = Auth::user();
    if($user->role_id == 1){
      OrderItem::where('id',$request['id'])->first()->delete();
      $new_order_price = OrderItem::where('order_id',$request['order_id'])->sum('total_price');
      $order = Order::where('id',$request['order_id'])->first();
      $order->price = $new_order_price;
      $order->save();
      $return_json =  array('status'=>'success','message'=>'delete success','code'=>200);
      return $return_json;
    }
  }

  public function addOrderItem(Request $request){
    $user = Auth::user();
    if($user->role_id == 1){
      if($request['order_id']){
        $new_item_data = array('com_id'=>$request['com_id'],'user_id'=>$request['user_id'],'order_id'=>$request['order_id'],
                               'name'=>$request['name'],'unit_price'=>$request['unit_price'],'quantity'=>$request['quantity'],'total_price'=>$request['total_price']);
        OrderItem::create($new_item_data);
        $new_order_price = OrderItem::where('order_id',$request['order_id'])->sum('total_price');
        $order = Order::where('id',$request['order_id'])->first();
        $order->price = $new_order_price;
        $order->save();
        $return_json =  array('status'=>'success','message'=>'add success','code'=>200);
        return $return_json;
      }
      else{
        $new_order_data = array('com_id'=>$request['com_id'],'user_id'=>$request['user_id'],'status'=>1,'pay_status'=>0,'price'=>$request['total_price'],'paid'=>0);
        $new_order = Order::create($new_order_data);
        $new_item_data = array('com_id'=>$request['com_id'],'user_id'=>$request['user_id'],'order_id'=>$new_order->id,
                               'name'=>$request['name'],'unit_price'=>$request['unit_price'],'quantity'=>$request['quantity'],'total_price'=>$request['total_price']);
        OrderItem::create($new_item_data);
        $return_json =  array('status'=>'success','message'=>'add success','code'=>200);
        return $return_json;

      }
    }
  }

  public function saveOrderItem(Request $request){
    $user = Auth::user();
    if($user->role_id == 1){
      $item_data = array('name'=>$request['name'],'unit_price'=>$request['unit_price'],'quantity'=>$request['quantity'],'total_price'=>$request['total_price']);
      
      $order_item = OrderItem::where('id',$request['id'])->first();
      $order_item->update($item_data);

      $new_order_price = OrderItem::where('order_id',$request['order_id'])->sum('total_price');
      $order = Order::where('id',$request['order_id'])->first();
      $order->price = $new_order_price;
      $order->save();

      $return_json =  array('status'=>'success','message'=>'update success','code'=>200);
      return $return_json;
    }
  }

  public function addOrderPayment(Request $request){
    $user = Auth::user();
    if($user->role_id == 1){
      $new_payment_data = array('order_id'=>$request['order_id'],'user_id'=>$request['user_id'],
                                'name'=>$request['name'],'pay_amount'=>$request['pay_amount'],'pay_status'=>0,'status'=>$request['status']);
      $new_payment = OrderPayment::create($new_payment_data);
      $return_json =  array('status'=>'success','message'=>'add success','code'=>200);
      return $return_json;
    }
  }

  public function deleteOrderPayment(Request $request){
    $user = Auth::user();
    if($user->role_id == 1){
      $order_payment = OrderPayment::where('id',$request['id'])->first();
      if($order_payment && $order_payment->status >= 3){
        $return_json =  array('status'=>'error','message'=>'Paid! Cannot delete','code'=>400);
        return $return_json;
      }
      else{
        $order_payment->delete();
        $return_json =  array('status'=>'success','message'=>'delete success','code'=>200);
        return $return_json;
      }
      
    }
  }

  public function saveOrderPayment(Request $request){
    $user = Auth::user();
    if($user->role_id == 1){
      $payment_data = array('name'=>$request['name'],'pay_amount'=>$request['pay_amount'],'status'=>$request['status']);
      $order_payment = OrderPayment::where('id',$request['id'])->first();
      $order_payment->update($payment_data);
      $return_json =  array('status'=>'success','message'=>'update success','code'=>200);
      return $return_json;
    }
  }

  public function saveComStatus(Request $request){
    $user = Auth::user();
    if($user->role_id == 1){
      $commission = Commission::where('id',$request['id'])->first();
      $commission->status = $request['status'];
      $commission->save();
      
      $return_json =  array('status'=>'success','message'=>'update success','code'=>200);
      return $return_json;
    }
  }

  public function products(){
    $products = Product::orderBy('id','DESC')->get();
    return view('backend/products',compact('products'));
  }

  public function productDetail($product_id){
    $product = Product::where('id',$product_id)->first();
    $artists = Artist::get();
    $attributes = ProductAttribute::where('product_id',$product_id)->get();
    $sku_items = ProductSkuItem::where('product_id',$product_id)->get();
    return view('backend/product_detail',compact('product','artists','attributes','sku_items'));
  }

  public function getAtrOption(Request $request){
   
    $options = ProductAttributeOption::where('attribute_id',$request->id)->get();
    $return_json =  array('status'=>'success','message'=>'success','code'=>200,'data'=>$options);
    return $return_json;
  }

  public function addNewAttr(Request $request){
    $user = Auth::user();
    if($user->role_id == 1){
      $new_attr_data = $request->only(["attribute_name","product_id"]);
      ProductAttribute::create($new_attr_data);
      $return_json =  array('status'=>'success','message'=>'create success','code'=>200);
      return $return_json;
    }
  }

  public function addNewOption(Request $request){
    $user = Auth::user();
    if($user->role_id == 1){
      $new_option_data = $request->only(["option_name","product_id",'attribute_id']);
      ProductAttributeOption::create($new_option_data);
      $return_json =  array('status'=>'success','message'=>'create success','code'=>200);
      return $return_json;
    }
  }

  public function saveOption(Request $request){
    $user = Auth::user();
    if($user->role_id == 1){
      $option = ProductAttributeOption::where('id',$request->id)->first();
      $new_option_data = $request->only(["option_name"]);
      $option->update($new_option_data);
      $return_json =  array('status'=>'success','message'=>'update success','code'=>200);
      return $return_json;
    }
  }

  public function deleteOption(Request $request){
    $user = Auth::user();
    if($user->role_id == 1){
      $option = ProductAttributeOption::where('id',$request->id)->first()->delete();
     
      $return_json =  array('status'=>'success','message'=>'delete success','code'=>200);
      return $return_json;
    }
  }

  public function addNewSku(Request $request){
    $user = Auth::user();
    if($user->role_id == 1){
      $new_sku_data = $request->only(["product_id","price","stock","sku_attribute"]);
      ProductSkuItem::create($new_sku_data);
      $return_json =  array('status'=>'success','message'=>'create success','code'=>200);
      return $return_json;
    }
  }

  public function deleteSku(Request $request){
    $user = Auth::user();
    if($user->role_id == 1){
      $delete_sku = ProductSkuItem::where('id',$request->id)->first()->delete();
      $return_json =  array('status'=>'success','message'=>'delete success','code'=>200);
      return $return_json;
    }
  }

  public function updateSku(Request $request){
    $user = Auth::user();
    if($user->role_id == 1){
      $attr = ProductSkuItem::where('id',$request->id)->first();

      $new_attr_data = $request->only(["price","stock","sku_attribute"]);
      $attr->update($new_attr_data);

      $return_json =  array('status'=>'success','message'=>'update success','code'=>200);
      return $return_json;
    }
  }

  public function uploadSkuFile(Request $request){
    $file =  $request->file('file');
    $sku_id = $request->sku_id;
    $sku = ProductSkuItem::where('id',$sku_id)->first();
    if($file){
      $file_folder = 'product_sku_files/'.$sku_id.'/';
      $file_name = 'sku_'.$sku_id.'_'.$file->getClientOriginalName();
      $file->move(Storage::path($file_folder),$file_name);
      $sku->file_url = $file_folder.$file_name;
      $sku->save();
      $return_json =  array('status'=>'success','message'=>'upload success','code'=>200);
      return $return_json;
    }
    // else{
    //   $new_logo = $shop->logo;//保持不变
    // }
  }
  

  // public function oderPage($com_id){



  // }

  // public function sortArtists(Request $request){
  //   $user = Auth::user();
  //   if($user){
  //     $sorted_artist_ids = $request['sorted_artist_ids'];
  //     foreach($sorted_artist_ids as $key=>$artist_id){
  //       $com_pref_artist = ComPrefArtist::where('com_id',$request['com_id'])->where('artist_id',$artist_id)->first();
  //       $com_pref_artist->sort = $key;
  //       $com_pref_artist->save();
  //     }
  //   }
  // }

  // public function removeArtist(Request $request){
  //   $user = Auth::user();
  //   if($user){
  //     $remove_artist = ComPrefArtist::where('com_id',$request['com_id'])->where('artist_id',$request['artist_id'])->first();
  //     $remove_artist->delete();
  //     $return_json =  array('status'=>'success','message'=>'Remove Success','code'=>200);
  //     return $return_json;
  //   }
  // }

  // public function paymentPage($payment_id){
  //   $order_payment = OrderPayment::where('id',$payment_id)->first();
  //   $order = Order::where('id',$order_payment->order_id)->first();
  //   $user = User::where('id',$order->user_id)->first();
  //   if($order){
  //     $order_items = OrderItem::where('order_id',$order->id)->get();
  //     $order_payments = OrderPayment::where('order_id',$order->id)->get();
  //     $order_payments = OrderPayment::statusRead($order_payments);
  //   }
  //   else{
  //     $order_items = [];
  //     $order_payments = [];
  //   }


  //   return view('dashboard/payment',compact('order','order_items','order_payment','user'));
  // }
}
