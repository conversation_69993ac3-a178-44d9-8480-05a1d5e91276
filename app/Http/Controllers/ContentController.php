<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Artist;
use App\Models\Artwork;
use App\Models\Service;
use App\Models\ServiceStage;
use App\Models\Tag;

class ContentController extends Controller
{
    public function workDetail(Request $request){
        $works = Artwork::where('id',$request->id)->first();
        return response()->json([
			'status' => 'success',
			'message' => 'get work detail success',
			'code'=>200,
            'data'=>$works,
		]);
    
    }

    public function serviceDetail($service_id){
        $service = Service::where("id",$service_id)->first();
        $artist = Artist::where("id",$service->artist_id)->first();
        $service_stages = ServiceStage::where("service_id",$service_id)->get();

        $prefer_tags = Tag::join("service_tag_pivot",'service_tag_pivot.tag_id','=','tags.id')
                            ->where("service_id",$service_id)
                            ->where("service_tag_pivot.type",2)
                            ->select("tags.*")
                            ->get();

        $cantdo_tags = Tag::join("service_tag_pivot",'service_tag_pivot.tag_id','=','tags.id')
                            ->where("service_id",$service_id)
                            ->where("service_tag_pivot.type",3)
                            ->select("tags.*")
                            ->get();

        return view("/content/service_detail",compact("service","artist","service_stages","prefer_tags","cantdo_tags"));
    }
}
