<?php

namespace App\Http\Controllers;

use App\Models\CommContent;
use App\Models\CurrencyRate;
use Illuminate\Http\File;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use OpenAI\Laravel\Facades\OpenAI;

use Illuminate\Support\Facades\Mail;
use App\Mail\EmailVerify;

use App\Events\TestEvent;

class OpenAIController extends Controller
{
    public function translateCommContent($comm_content_translate_job)
    {
        if ($comm_content_translate_job) {
            // $comm_content = CommContent::where('comm_id',$comm_content_translate_job->comm_id)->where('is_source',1)->first();
            if ($comm_content) {
                $translate_json = $comm_content->only(['title', 'detail']);
                $result = OpenAI::chat()->create([
                    'model' => 'gpt-3.5-turbo',
                    'messages' => [
                        ['role' => 'system', 'content' => 'You'],
                        ['role' => 'user', 'content' => json_encode($translate_json)],
                    ],
                ]);

                if ($result) {
                    $content_json_encoded = $result->choices[0]->message->content;
                    $content_json = json_decode($content_json_encoded, true);
                    $other_content_data = [
                        'comm_id' => $comm_content->comm_id,
                        'artist_id' => $comm_content->artist_id,
                        'is_source' => 0,
                        'lang_id' => $comm_content_translate_job->target_lang_id,
                        'lang_code' => $comm_content_translate_job->target_lang_code,
                        'lang_name' => $comm_content_translate_job->target_lang_name,
                    ];
                    $translated_content = array_merge($content_json, $other_content_data);
                    CommContent::create($translated_content);
                    $comm_content_translate_job->status = 100;
                    $comm_content_translate_job->save();

                    return 'success';
                }
            }
            // dd($result);
        }
    }

    public function testOpenAi()
    {

        // $request->save();
        $translate_json = array(
            'title'=>'I hate niggers and fucking Ching Chongs, and I want to kill myself, and I want to have sex with your mom',
            'content'=>'Looking for: Rigger that also offers outfit/hair/asset/accessory add ons Type of Model: Live 2D Full Body (2 different models) Usage: Streaming/Commercial NSFW: Yes (1 model is heavily nsfw and the other is wholesome) Budget: $500 per model ($1,000 total) - Paid in USD Deadline: TBD (Would like to aim for September due date) Important: Both OCs are still in sketch process and not yet completed by the artists. I am aiming for the OCs to be very bouncy but fluid. There are a few toggles I would like to add (please keep in mind some of the toggles will be nsfw). Some othe details will be in details',
            'details'=>'Paid in USD Deadline: TBD (Would like to aim for September due date) Important: Both OCs are still in sketch process and not yet completed by the artists. I am aiming for the OCs to be very bouncy but fluid. There are a few toggles I would like to add (please keep in mind some of the toggles will be nsfw). ',
        );
        $result = OpenAI::chat()->create([
            'model' => 'gpt-4o-mini-2024-07-18',
            'messages' => [
                ['role' => 'system', 'content' => 'You are a project manager, please translate the json value of the client requirement for art commission to Chinese.'],
                ['role' => 'user', 'content' => json_encode($translate_json)],

            ],
            "response_format" => [
                "type"=> "json_schema",
                "json_schema"=> [
                    "name"=> "tranlate_json",
                    "schema"=> [
                        "type"=> "object",
                        "properties"=> [
                            "title"=> [
                                "type"=> "string",
                                "description"=>"translation of the title"
                            ],
                            "second_translation"=> [
                                "type"=> "string",
                                "description"=>"translation of the json key 'content'"
                            ],
                            "detail_translation"=> [
                                "type"=> "string",
                                "description"=>"translation of the details"
                            ],
                    ],
                    "required"=> ["title", "second_translation","detail_translation"],
                    "additionalProperties"=> false
                    ],
                    "strict"=> true
                ]
            ]
        ]);
        $result_message = $result->choices[0]->message;
        // if($result_message->refusal){

        // }
        $new_result = array(
            'raw'=>$result,
            'decode'=>json_decode($result->choices[0]->message->content)
        );
        dd($new_result);
        // dd(json_decode($result->choices[0]->message->content));

        // if ($result) {
        //     $zh_detail = $result->choices[0]->message->content;
        //     $request_detail['zh'] = $zh_detail;
        // }

        // $total_token_usage = $result->usage->totalTokens;
        // if($result){
        // 	dd($result);
        // }
    }

    public function testCurrency()
    {

        $response = Http::get('https://v6.exchangerate-api.com/v6/************************/latest/USD', [
            // 'access_key' => '7cbc81a096c1240e8d897f80bcc25ecd',
            // // 'base' => 'USD',
            // 'symbols' => 'CNY,USD,JPY',
        ]);
        $base_code = 'USD';
        if ($response->ok()) {
            $res_data = $response->json();

            $currency_rates = $res_data['conversion_rates'];
            // return $res_data[];
            foreach ($currency_rates as $key => $rate) {
                $currency_rate_db = CurrencyRate::where('base_code', $base_code)->where('target_code', $key)->first();
                if ($currency_rate_db) {
                    $currency_rate_db->rate = $rate;
                    $currency_rate_db->save();
                } else {
                    CurrencyRate::create([
                        'base_code' => $base_code,
                        'target_code' => $key,
                        'rate' => $rate,
                    ]);
                }
            }
        }

        return $response;
    }

    public function testFile()
    {
        $image_file = Storage::get('test3.txt');

        return Storage::disk('r2-private')->download('test.txt');
        // Storage::disk('r2-private')->put('test.txt',$image_file);

    }

    public function testMail(){
        $data = [
            'code'=>'778899'
        ];
        Mail::to(['email'=>'<EMAIL>'])->send(new EmailVerify($data));
    }

    public function testEcho(){
        $event = TestEvent::dispatch(['123456']);
    }
}
