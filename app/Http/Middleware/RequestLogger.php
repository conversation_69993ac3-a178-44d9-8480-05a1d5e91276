<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class RequestLogger
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        \Log::debug('Request', [
            'method' => $request->getMethod(),
            'uri' => $request->getRequestUri(),
        ]);

        $response = $next($request);

        if (defined('LARAVEL_START') and $response instanceof Response) {
            $response->headers->add(['X-RESPONSE-TIME' => microtime(true) - LARAVEL_START]);
        }

        return $response;
    }

    public function terminate($request, $response)
    {
        // At this point the response has already been sent to the browser so any
        // modification to the response (such adding HTTP headers) will have no effect
        if (defined('LARAVEL_START') and $request instanceof Request) {
            \Log::debug('Response time', [
                'method' => $request->getMethod(),
                'uri' => $request->getRequestUri(),
                // 'body' => $request->all(),
                // 'response' => $response->getContent(),
                'seconds' => microtime(true) - LARAVEL_START,
            ]);
        }
    }
}
