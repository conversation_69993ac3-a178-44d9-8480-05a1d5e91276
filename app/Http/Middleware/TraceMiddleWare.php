<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Str;

class TraceMiddleWare
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next)
    {
        $traceId = $request->header('X-Trace-ID') ?? Str::uuid()->toString();
        $request->headers->set('X-Trace-ID', $traceId);
        \Log::withContext(['trace_id' => $traceId]);

        $response = $next($request);

        $response->headers->set('X-Trace-ID', $traceId);

        return $response;
    }
}
