<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TranslateCreateResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'key' => $this->key,
            'source_lang' => $this->source_lang,
            'target_text' => $this->target_text,
            'target_lang' => $this->target_lang,
            'batteries' => $this->batteries,
        ];
    }
}
