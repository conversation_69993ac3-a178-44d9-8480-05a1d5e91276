<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TranslateAiCreateResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'busable_table' => $this->busable_table,
            'busable_field' => $this->busable_field,
            'source_lang' => $this->source_lang,
            'target_lang' => $this->target_lang,
            'target_text' => $this->target_text,
            'batteries' => $this->batteries,
        ];
    }
}
