<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserInfoResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'avatar' => $this->avatar,
            'settings' => $this->settings,
            'currency' => $this->currency,
            'language' => $this->language,
            'artist' => $this->artist,
            'permissions' => $this->permissions->map(fn ($permission) => $permission->name),
            'roles' => $this->roles->map(fn ($role) => $role->name),
            'is_artist' => $this->is_artist,
        ];
    }
}
