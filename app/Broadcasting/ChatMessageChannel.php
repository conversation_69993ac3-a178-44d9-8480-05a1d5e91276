<?php

namespace App\Broadcasting;

use App\Models\ChatUserPivot;
use App\Models\User;

class ChatMessageChannel
{
    /**
     * Create a new channel instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Authenticate the user's access to the channel.
     */
    public function join(User $user, $chatId): array|bool
    {
        dd($user, $chatId);

        // return false;

        return ChatUserPivot::where('chat_id', $chatId)
            ->where('user_id', $user->id)
            ->exists();
    }
}
