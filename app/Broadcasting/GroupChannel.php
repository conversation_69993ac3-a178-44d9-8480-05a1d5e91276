<?php

namespace App\Broadcasting;

use App\Models\GroupUserPivot;
use App\Models\User;

class GroupChannel
{
    /**
     * Create a new channel instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Authenticate the user's access to the channel.
     */
    public function join(User $user, $groupId): array|bool
    {
        return GroupUserPivot::where('group_id', $groupId)
            ->where('user_id', $user->id)
            ->exists();
    }
}
