<?php

namespace App\Casts;

use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Illuminate\Database\Eloquent\Casts\Json;
use Illuminate\Database\Eloquent\Model;
use stdClass;

class TranslateCast implements CastsAttributes
{
    /**
     * Cast the given value.
     *
     * @param  array<string, mixed>  $attributes
     */
    public function get(Model $model, string $key, mixed $value, array $attributes): mixed
    {
        $table = $model->getTable();
        if (in_array($table, ['group_messages', 'chat_messages']) && $key === 'content') {
            if ($model->is_undo) {
                $value = null;
            }
        }

        if (! $value) {
            return $value;
        }
        $jsonValue = Json::decode($value, false);
        if ($jsonValue === null) {
            $jsonValue = new stdClass;
        }

        if (is_array($jsonValue) && count($jsonValue) === 0) {
            $jsonValue = (object) $jsonValue;
        }

        $language = 'en';
        if (property_exists($jsonValue, '_lang')) {
            $language = $jsonValue->_lang;
        } else {
            $jsonArray = (array) $jsonValue;
            $firstKey = array_key_first($jsonArray);

            if ($firstKey !== null) {
                $language = $firstKey;
            }
        }
        $jsonValue->_lang = $language;

        $jsonValue->_key = aes128Encrypt("{$model->getTable()}:{$model->getKey()}:{$key}");

        return $jsonValue;
    }

    /**
     * Prepare the given value for storage.
     *
     * @param  array<string, mixed>  $attributes
     */
    public function set(Model $model, string $key, mixed $value, array $attributes): mixed
    {
        return $value;
    }
}
