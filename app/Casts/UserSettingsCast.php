<?php

namespace App\Casts;

use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Illuminate\Database\Eloquent\Model;

class UserSettingsCast implements CastsAttributes
{
    /**
     * Cast the given value.
     *
     * @param  array<string, mixed>  $attributes
     */
    public function get(Model $model, string $key, mixed $value, array $attributes): mixed
    {
        $jsonValue = json_decode($value ?? '[]', true);
        if ($jsonValue === null) {
            $jsonValue = [];
        }

        $language = $model->language;
        $default_language_code = $language->code ?? 'en';

        $defaultSettings = [
            'chat_language_code' => $default_language_code,
            'chat_auto_translate' => true,
            'chat_minimize_on_redirect' => true,
        ];

        $jsonValue = array_merge($defaultSettings, $jsonValue);

        return $jsonValue;
    }

    /**
     * Prepare the given value for storage.
     *
     * @param  array<string, mixed>  $attributes
     */
    public function set(Model $model, string $key, mixed $value, array $attributes): mixed
    {
        return json_encode($value);
    }
}
