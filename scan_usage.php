<?php

require __DIR__.'/vendor/autoload.php';
$app = require_once __DIR__.'/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
$response = $kernel->handle(
    $request = Illuminate\Http\Request::capture()
);
function scanDirectory($directory)
{
    $results = [];
    $files = glob($directory.'/**/*');

    foreach ($files as $file) {
        if (is_file($file) && strpos($file, 'Controller.php') !== false) {
            // 获取类名
            $className = getClassNameFromFile($file);
            if (! $className) {
                continue;
            }

            try {
                // 使用反射分析类
                $reflector = new ReflectionClass($className);
                $methods = $reflector->getMethods();

                foreach ($methods as $method) {
                    // 获取方法的源代码
                    $start = $method->getStartLine();
                    $end = $method->getEndLine();
                    $source = file($file);
                    $body = implode('', array_slice($source, $start - 1, $end - $start + 1));

                    if (strpos($body, 'setTransByReq') !== false) {
                        // echo 'File: '.$file."\n";
                        // echo 'Method: '.$method->getName()."\n";
                        // echo 'Lines: '.$start.'-'.$end."\n\n";

                        // 查找对应的路由
                        $routes = findRoutesByController($className, $method->getName());
                        if (! empty($routes)) {
                            echo "Api:\n";
                            foreach ($routes as $route) {
                                echo '  '.$route['method'].' '.$route['uri']."\n";
                            }
                        }

                        // 添加正则表达式匹配 setTransByReq 的调用参数
                        if (preg_match_all('/setTransByReq\((.*?)\)/', $body, $matches)) {
                            echo "Parameters:\n";
                            foreach ($matches[1] as $params) {
                                $param = explode(',', $params)[0];
                                echo '  '.$param."\n";
                            }
                        }

                        echo "\n";

                    }

                }
            } catch (ReflectionException $e) {
                continue;
            }
        }
    }

    return $results;
}

function getClassNameFromFile($file)
{
    $content = file_get_contents($file);
    if (preg_match('/namespace\s+(.+?);/s', $content, $matches)) {
        $namespace = $matches[1];
        if (preg_match('/class\s+(\w+)/', $content, $matches)) {
            return $namespace.'\\'.$matches[1];
        }
    }

    return null;
}
function findRoutesByController($controller, $method)
{
    // dump($controller, $method);
    $routes = [];
    // 使用 Route facade 或直接从应用容器获取路由集合
    $routeCollection = \Route::getRoutes();

    foreach ($routeCollection as $route) {
        $action = $route->getAction();

        if (isset($action['controller'])) {
            $routeController = $action['controller'];
            if (strpos($routeController, 'Spatie\LaravelIgnition') !== false) {
                continue;
            }
            [$controllerClass, $controllerMethod] = explode('@', $routeController);

            if ($controllerClass === $controller && $controllerMethod === $method) {
                // dump($controllerClass, $controllerMethod);
                // dump($controller, $method);

                $routes[] = [
                    'method' => implode('|', $route->methods()),
                    'uri' => $route->uri(),
                ];
            }
        }
    }

    return $routes;
}
$controllerPath = 'app/Http/Controllers/Api';
scanDirectory($controllerPath);
