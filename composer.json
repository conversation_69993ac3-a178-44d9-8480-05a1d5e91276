{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "hisorange/browser-detect": "^5.0", "intervention/image": "^3.5", "intervention/image-laravel": "^1.2", "laravel/cashier": "^15.3", "laravel/framework": "^11.0", "laravel/reverb": "^1.4", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.9", "league/flysystem-aws-s3-v3": "^3.26", "openai-php/laravel": "^0.8.1", "rajentrivedi/tokenizer-x": "^1.0", "spatie/laravel-data": "^4.14", "spatie/laravel-permission": "^6.9", "spatie/laravel-translatable": "^6.6", "staudenmeir/laravel-adjacency-list": "^1.21", "stripe/stripe-php": "^13.18", "symfony/http-client": "^7.1", "symfony/mailgun-mailer": "^7.1", "yansongda/laravel-pay": "~3.7.0"}, "require-dev": {"barryvdh/laravel-ide-helper": "^3.0", "barryvdh/reflection-docblock": "^2.1", "fakerphp/faker": "^1.23", "kitloong/laravel-migrations-generator": "^7.0", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "phpunit/phpunit": "^11.0", "spatie/laravel-ignition": "^2.4"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}