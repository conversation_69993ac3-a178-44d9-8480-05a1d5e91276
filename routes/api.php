<?php

use App\Http\Controllers\Api\StripeController;
use App\Http\Controllers\OpenAIController;
use App\Http\Controllers\TestController;
use Illuminate\Support\Facades\Route;

Route::get('/stripe/callback_payment_link', [StripeController::class, 'paymentLinkCallback']);
Route::post('/stripe/webhook', [StripeController::class, 'webhook']);

if (APP::environment(['local', 'test'])) {
    Route::group(['prefix' => 'test'], function () {
        Route::get('/currency', [OpenAIController::class, 'testCurrency']);
        Route::Post('/openai', [OpenAIController::class, 'testOpenAi']);
        Route::get('/mail', [OpenAIController::class, 'testMail']);
        Route::get('/echo', [OpenAIController::class, 'testEcho']);

        Route::Post('/file', [TestController::class, 'file']);
        Route::Post('/upload_storage_file', [TestController::class, 'uploadStoreageFile']);
        Route::get('/phpinfo', function () {
            phpinfo();
        });
    });
}
