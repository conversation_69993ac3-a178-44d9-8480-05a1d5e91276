<?php

use App\Broadcasting\ChatMessageChannel;
use App\Broadcasting\GroupChannel;
use Illuminate\Support\Facades\Broadcast;

Broadcast::channel('App.Models.User.{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});

Broadcast::channel('test-channel-1', function () {
    return true;
});

// Broadcast::channel('private-chat_messages.{chatId}', ChatMessageChannel::class);
// Broadcast::channel('chat_messages.{chatId}', ChatMessageChannel::class);
// Broadcast::channel('chat_messages.{chatId}', function () {
//     return false;
// });
// Broadcast::channel('group.{groupId}', GroupChannel::class);
