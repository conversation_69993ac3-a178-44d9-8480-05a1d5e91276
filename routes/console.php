<?php

use App\Console\Commands\CalculateRecommendScore;
use App\Console\Commands\DeleteExpiredUploadFile;
use App\Console\Commands\ProcessWalletDeductions;
use App\Console\Commands\SyncCurrencyRate;

Schedule::command(CalculateRecommendScore::class)->hourly();
Schedule::command(SyncCurrencyRate::class)->daily();
Schedule::command(DeleteExpiredUploadFile::class)->daily();

// 每5分钟处理一次待补差记录
Schedule::command(ProcessWalletDeductions::class, ['--limit=20'])
    ->everyFiveMinutes()
    ->withoutOverlapping()
    ->runInBackground();
