<?php

use App\Http\Controllers\Web\ShareController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::group(['prefix' => 'web'], function () {
    Route::group(['prefix' => 'share'], function () {
        Route::get('/{scence}/{id}', [ShareController::class, 'twitterSharePage'])->name('share.page');
        Route::get('/{scence}/{id}/twitter_card_image.jpg', [ShareController::class, 'twitterShareCardImage'])->name('share.twitter.image');
    });
});
