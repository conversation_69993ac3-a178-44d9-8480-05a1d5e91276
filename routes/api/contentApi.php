<?php

use App\Http\Controllers\Api\Content\ArticleCategoryController;
use App\Http\Controllers\Api\Content\ArticleController;
use App\Http\Controllers\Api\Content\ArtistController;
use App\Http\Controllers\Api\Content\ArtworkController;
use App\Http\Controllers\Api\Content\CaptchaController;
use App\Http\Controllers\Api\Content\ContactUsController;
use App\Http\Controllers\Api\Content\CurrenciesController;
use App\Http\Controllers\Api\Content\ProductController;
use App\Http\Controllers\Api\Content\ProjectController;
use App\Http\Controllers\Api\Content\ReminderCategoryController;
use App\Http\Controllers\Api\Content\ReminderTemplateController;
use App\Http\Controllers\Api\Content\ServiceController;
use App\Http\Controllers\Api\Content\SiteController;
use App\Http\Controllers\Api\Content\TagController;
use Illuminate\Broadcasting\BroadcastController;
use Illuminate\Support\Facades\Route;

Route::group(['prefix' => 'content'], function () {

    Route::group(['prefix' => 'site'], function () {
        Route::get('meta', [SiteController::class, 'meta']);
        Route::get('download', [SiteController::class, 'download']);
    });

    Route::group(['prefix' => 'article'], function () {
        Route::get('list', [ArticleController::class, 'list']);
        Route::get('info', [ArticleController::class, 'info']);
    });

    Route::group(['prefix' => 'article_categories'], function () {
        Route::get('list', [ArticleCategoryController::class, 'list']);
    });

    Route::group(['prefix' => 'artist'], function () {
        Route::get('info', [ArtistController::class, 'artistInfo']);
        Route::post('list', [ArtistController::class, 'artistList']);
        Route::get('filters', [ArtistController::class, 'artistFilters']);
    });

    Route::group(['prefix' => 'artwork'], function () {
        Route::post('list', [ArtworkController::class, 'artworkList']);
        Route::get('info', [ArtworkController::class, 'artworkInfo']);
        Route::get('filters', [ArtworkController::class, 'artistFilters']);
    });

    Route::group(['prefix' => 'service'], function () {
        Route::post('list', [ServiceController::class, 'serviceList']);
        Route::get('info', [ServiceController::class, 'serviceInfo']);
        Route::get('filters', [ServiceController::class, 'serviceFilters']);
    });

    Route::group(['prefix' => 'projects'], function () {
        Route::Post('/list', [ProjectController::class, 'list']);
        Route::Get('/info', [ProjectController::class, 'info']);
    });

    Route::group(['prefix' => 'currencies'], function () {
        Route::get('rates', [CurrenciesController::class, 'rates']);
        Route::get('list', [CurrenciesController::class, 'list']);
    });

    Route::group(['prefix' => 'products'], function () {
        Route::Post('/list', [ProductController::class, 'list']);
        Route::Post('/info', [ProductController::class, 'info']);
    });

    Route::group(['prefix' => 'tags'], function () {
        Route::Post('/list', [TagController::class, 'list']);
    });

    Route::group(['prefix' => 'captchas'], function () {
        Route::Post('/create', [CaptchaController::class, 'create']);
        Route::Post('/verify', [CaptchaController::class, 'verify']);
    });

    Route::group(['prefix' => 'contact_us'], function () {
        Route::Post('/create', [ContactUsController::class, 'create']);
    });

    Route::group(['prefix' => 'reminder_categories'], function () {
        Route::Post('/list', [ReminderCategoryController::class, 'list']);
    });

    Route::group(['prefix' => 'reminder_templates'], function () {
        Route::Post('/list', [ReminderTemplateController::class, 'list']);
    });

    // Route::any('/broadcasting/auth', [BroadcastController::class, 'authenticate']);
    Route::any('/xxx/auth', [ReminderTemplateController::class, 'authenticate']);
});
