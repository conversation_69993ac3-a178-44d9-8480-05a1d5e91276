<?php

use App\Http\Controllers\Api\Artist\ArtistCenterController;
use App\Http\Controllers\Api\Artist\ArtistInviteCodeController;
use App\Http\Controllers\Api\Artist\ArtWorkController;
use App\Http\Controllers\Api\Artist\ProductController;
use App\Http\Controllers\Api\Artist\ProjectRequestController;
use App\Http\Controllers\Api\Artist\ServiceController;
use App\Http\Controllers\Api\Artist\ServiceRequestController;
use App\Http\Controllers\Api\Artist\ServiceShowcaseController;
use App\Http\Controllers\Api\Artist\UserRecReviewsController;
use App\Http\Controllers\Api\Artist\WorkTaskController;
use App\Http\Controllers\Api\Artist\WorkTaskFileChangeRequestController;
use App\Http\Controllers\Api\Artist\WorkTaskFileController;
use App\Http\Controllers\Api\Artist\WorkTaskPriceChangeController;
use App\Http\Middleware\AuthIsArtistMiddleware;
use Illuminate\Support\Facades\Route;

Route::middleware(['auth:sanctum', AuthIsArtistMiddleware::class])->group(function () {
    // logined artister
    Route::group(['prefix' => 'artist_center'], function () {

        Route::get('/profile', [ArtistCenterController::class, 'profile']);
        Route::post('/update_profile', [ArtistCenterController::class, 'updateProfile']);
        Route::post('/update_cover', [ArtistCenterController::class, 'updateCover']);
        Route::post('/update_avatar', [ArtistCenterController::class, 'updateAvatar']);

        Route::group(['prefix' => 'artworks'], function () {
            Route::get('/list', [ArtWorkController::class, 'list']);
            Route::get('/meta', [ArtWorkController::class, 'meta']);
            Route::get('/info', [ArtWorkController::class, 'info']);
            Route::post('/update', [ArtWorkController::class, 'update']);
            Route::post('/create', [ArtWorkController::class, 'create']);
            Route::post('/create_by_link', [ArtWorkController::class, 'createByLink']);
            Route::post('/delete', [ArtWorkController::class, 'delete']);
            Route::post('/update_sort', [ArtWorkController::class, 'updateSort']);
        });

        Route::group(['prefix' => 'services'], function () {
            Route::get('/list', [ServiceController::class, 'list']);
            Route::get('/meta', [ServiceController::class, 'meta']);
            Route::get('/info', [ServiceController::class, 'info']);
            Route::post('/update', [ServiceController::class, 'update']);
            Route::post('/create', [ServiceController::class, 'create']);
            Route::post('/delete', [ServiceController::class, 'delete']);
            Route::post('/update_all_is_open', [ServiceController::class, 'updateAllIsOpen']);
        });

        Route::group(['prefix' => 'service_showcases'], function () {
            Route::post('/create', [ServiceShowcaseController::class, 'create']);
            Route::post('/delete', [ServiceShowcaseController::class, 'delete']);
        });

        Route::group(['prefix' => 'service_requests'], function () {
            Route::get('/list', [ServiceRequestController::class, 'list']);
            Route::get('/info', [ServiceRequestController::class, 'info']);
            Route::post('/accept', [ServiceRequestController::class, 'accept']);
            Route::post('/reject', [ServiceRequestController::class, 'reject']);
            Route::post('/delete', [ServiceRequestController::class, 'delete']);
        });

        Route::group(['prefix' => 'work_task_files'], function () {
            Route::post('/create', [WorkTaskFileController::class, 'create']);
            Route::post('/delete', [WorkTaskFileController::class, 'delete']);
            Route::post('/download', [WorkTaskFileController::class, 'download']);
        });

        Route::group(['prefix' => 'work_task_file_change_requests'], function () {
            Route::post('/list', [WorkTaskFileChangeRequestController::class, 'list']);
        });

        Route::group(['prefix' => 'work_tasks'], function () {
            Route::post('/list', [WorkTaskController::class, 'list']);
            Route::get('/info', [WorkTaskController::class, 'info']);
            Route::post('/accept', [WorkTaskController::class, 'accept']);
            Route::post('/reject', [WorkTaskController::class, 'reject']);
            Route::post('/cancel', [WorkTaskController::class, 'cancel']);
        });

        Route::group(['prefix' => 'work_task_price_changes'], function () {
            Route::post('/list', [WorkTaskPriceChangeController::class, 'list']);
            Route::post('/info', [WorkTaskPriceChangeController::class, 'info']);
            Route::post('/create', [WorkTaskPriceChangeController::class, 'create']);
            Route::post('/approve', [WorkTaskPriceChangeController::class, 'approve']);
            Route::post('/reject', [WorkTaskPriceChangeController::class, 'reject']);
            Route::post('/cancel', [WorkTaskPriceChangeController::class, 'cancel']);
        });

        Route::group(['prefix' => 'user_rec_reviews'], function () {
            Route::Post('/create_work_task_review', [UserRecReviewsController::class, 'createWorkTaskReview']);
            Route::Post('/delete_work_task_review', [UserRecReviewsController::class, 'deleteWorkTaskReview']);
            Route::Get('/work_task_review_info', [UserRecReviewsController::class, 'workTaskReviewInfo']);
        });

        Route::group(['prefix' => 'project_request'], function () {
            Route::Post('/create', [ProjectRequestController::class, 'create']);
            Route::Post('/cancel', [ProjectRequestController::class, 'cancel']);
            Route::Post('/list', [ProjectRequestController::class, 'list']);
            Route::Post('/list_by_chosen', [ProjectRequestController::class, 'list_by_chosen']);
        });

        Route::group(['prefix' => 'products'], function () {
            Route::Post('/create', [ProductController::class, 'create']);
            Route::Get('/meta', [ProductController::class, 'meta']);
            Route::Post('/info', [ProductController::class, 'info']);
            Route::Post('/list', [ProductController::class, 'list']);
            Route::Post('/update', [ProductController::class, 'update']);
            Route::Post('/delete', [ProductController::class, 'delete']);
            Route::Post('/upload_product_file', [ProductController::class, 'upload_product_file']);
            Route::Post('/download_product_file', [ProductController::class, 'download_product_file']);
        });

        Route::group(['prefix' => 'artist_invite_codes'], function () {
            Route::Post('/list', [ArtistInviteCodeController::class, 'list']);
        });

        Route::group(['prefix' => 'profile'], function () {
        });

    });

});
