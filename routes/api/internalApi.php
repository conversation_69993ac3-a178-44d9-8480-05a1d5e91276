<?php

use App\Http\Controllers\Api\Internal\InternalController;
use App\Http\Controllers\Api\Internal\InternalWalletDeductionController;
use App\Http\Controllers\Api\Internal\UserApplicantController;
use App\Http\Middleware\AuthInternalApiRequestMiddleware;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Route;

Route::middleware([AuthInternalApiRequestMiddleware::class])->group(function () {
    Route::group(['prefix' => 'internal'], function () {
        Route::post('/become_artist', [InternalController::class, 'becomeArtist']);

        Route::post('/user_applicant/accept', [UserApplicantController::class, 'accept']);
        Route::post('/user_applicant/reject', [UserApplicantController::class, 'reject']);

        // 钱包补差记录管理
        Route::prefix('wallet-deduction')->group(function () {
            Route::get('/records', [InternalWalletDeductionController::class, 'list']);
            Route::post('/manual-compensate/{id}', [InternalWalletDeductionController::class, 'manualCompensate']);
            Route::post('/batch-process', [InternalWalletDeductionController::class, 'batchProcess']);
            Route::get('/statistics', [InternalWalletDeductionController::class, 'statistics']);
        });

        Route::post('/env', function () {
            return [
                'app' => Config::get('app'),
            ];
        });
    });

});
