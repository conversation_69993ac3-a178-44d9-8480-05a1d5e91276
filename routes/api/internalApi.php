<?php

use App\Http\Controllers\Api\Internal\InternalController;
use App\Http\Controllers\Api\Internal\UserApplicantController;
use App\Http\Middleware\AuthInternalApiRequestMiddleware;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Route;

Route::middleware([AuthInternalApiRequestMiddleware::class])->group(function () {
    Route::group(['prefix' => 'internal'], function () {
        Route::post('/become_artist', [InternalController::class, 'becomeArtist']);

        Route::post('/user_applicant/accept', [UserApplicantController::class, 'accept']);
        Route::post('/user_applicant/reject', [UserApplicantController::class, 'reject']);

        Route::post('/env', function () {
            return [
                'app' => Config::get('app'),
            ];
        });
    });

});
