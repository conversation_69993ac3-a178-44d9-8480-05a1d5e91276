<?php

use App\Http\Controllers\Api\Admin\ArtistController;
use App\Http\Controllers\Api\Admin\GroupController;
use App\Http\Controllers\Api\Admin\GroupMessageController;
use App\Http\Controllers\Api\Admin\ProfileController;
use App\Http\Controllers\Api\Admin\ProjectController;
use App\Http\Controllers\Api\Admin\ServiceController;
use App\Http\Controllers\Api\Admin\ServiceRequestController;
use App\Http\Controllers\Api\Admin\TranslateController;
use App\Http\Controllers\Api\Admin\WorkTaskController;
use App\Http\Middleware\AuthRoleMiddleware;

Route::middleware(['auth:sanctum', AuthRoleMiddleware::class.':admin,super_admin'])->group(function () {
    Route::group(['prefix' => 'admin_center'], function () {
        Route::group(['prefix' => 'service'], function () {
            Route::post('/list', [ServiceController::class, 'list']);
            Route::post('/update', [ServiceController::class, 'update']);
        });

        Route::group(['prefix' => 'service_request'], function () {
            Route::post('/list', [ServiceRequestController::class, 'list']);
            Route::post('/update', [ServiceRequestController::class, 'update']);
            Route::post('/info', [ServiceRequestController::class, 'info']);
        });

        Route::group(['prefix' => 'project'], function () {
            Route::post('/list', [ProjectController::class, 'list']);
            Route::post('/update', [ProjectController::class, 'update']);
        });

        Route::group(['prefix' => 'artist'], function () {
            Route::post('/list', [ArtistController::class, 'list']);
            Route::post('/update', [ArtistController::class, 'update']);
        });

        Route::group(['prefix' => 'work_task'], function () {
            Route::post('/list', [WorkTaskController::class, 'list']);
            Route::post('/info', [WorkTaskController::class, 'info']);
        });

        Route::group(['prefix' => 'group_message'], function () {
            Route::post('/list', [GroupMessageController::class, 'list']);
        });

        Route::group(['prefix' => 'group'], function () {
            Route::post('/join', [GroupController::class, 'join']);
        });

        Route::group(['prefix' => 'translate'], function () {
            Route::post('/human_pending_count', [TranslateController::class, 'humanPendingCount']);
        });

        Route::group(['prefix' => 'profile'], function () {
            Route::post('/job_count', [ProfileController::class, 'jobCount']);
        });
    });

});
