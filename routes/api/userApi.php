<?php

use App\Http\Controllers\Api\StripeController;
use App\Http\Controllers\Api\User\ArtistApplicationController;
use App\Http\Controllers\Api\User\ArtistRecReviewsController;
use App\Http\Controllers\Api\User\BookmarkController;
use App\Http\Controllers\Api\User\ChatController;
use App\Http\Controllers\Api\User\ChatMessageController;
use App\Http\Controllers\Api\User\GroupController;
use App\Http\Controllers\Api\User\GroupMessageController;
use App\Http\Controllers\Api\User\GroupReminderController;
use App\Http\Controllers\Api\User\NotificationController;
use App\Http\Controllers\Api\User\OrderController;
use App\Http\Controllers\Api\User\ProductOrderController;
use App\Http\Controllers\Api\User\ProductOrderItemController;
use App\Http\Controllers\Api\User\ProfileController;
use App\Http\Controllers\Api\User\ProjectController;
use App\Http\Controllers\Api\User\ProjectManagerController;
use App\Http\Controllers\Api\User\ProjectRequestController;
use App\Http\Controllers\Api\User\ServiceRequestController;
use App\Http\Controllers\Api\User\TranslateController;
use App\Http\Controllers\Api\User\UserController;
use App\Http\Controllers\Api\User\UserWithdrawAccountController;
use App\Http\Controllers\Api\User\WalletController;
use App\Http\Controllers\Api\User\WalletHistoryController;
use App\Http\Controllers\Api\User\WalletTransactionController;
use App\Http\Controllers\Api\User\WalletWithdrawController;
use App\Http\Controllers\Api\User\WorkTaskController;
use App\Http\Controllers\Api\User\WorkTaskFileChangeRequestController;
use App\Http\Controllers\Api\User\WorkTaskFileController;
use App\Http\Controllers\Api\User\WorkTaskPayController;
use App\Http\Controllers\Api\User\WorkTaskPriceChangeController;
use Illuminate\Support\Facades\Route;

// 无需登录，无需鉴权的接口
Route::group(['prefix' => 'user'], function () {
    Route::post('/send_sign_up_code', [UserController::class, 'sendSignUpCode']);
    Route::post('/verify_sign_up_code', [UserController::class, 'verifySignUpCode']);
    Route::post('/sign_up', [UserController::class, 'signUp']);

    Route::post('/send_forget_psw_code', [UserController::class, 'sendForgetPswCode']);
    Route::post('/verify_forget_psw_code', [UserController::class, 'verifyForgetPswCode']);
    Route::post('/forget_psw_change', [UserController::class, 'forgetPswChange']);
    Route::post('/sign_in', [UserController::class, 'signIn']);

    Route::get('/proxy_image', [UserController::class, 'proxyImage']);
    Route::get('/video_preview_image', [UserController::class, 'videoPreviewImage']);
});

Route::middleware('auth:sanctum')->group(function () {
    // logined user
    Route::group(['prefix' => 'user'], function () {
        Route::post('/upload_image', [UserController::class, 'uploadImage']);
        Route::post('/upload_video', [UserController::class, 'uploadVideo']);
        Route::post('/upload_file', [UserController::class, 'uploadFile']);

        Route::post('/upload_file_template_url', [UserController::class, 'uploadFileTemplateUrl']);
        Route::post('/upload_file_success', [UserController::class, 'uploadFileSuccess']);

        Route::post('/sign_out', [UserController::class, 'signOut']);
        Route::post('/change_name', [UserController::class, 'changeName']);
        Route::post('/change_psw', [UserController::class, 'changePsw']);
        Route::post('/send_change_email_code', [UserController::class, 'sendChangeEmailCode']);
        Route::post('/change_email', [UserController::class, 'changeEmail']);
        Route::post('/change_avatar', [UserController::class, 'changeAvatar']);
        Route::post('/update_setting', [UserController::class, 'updateSetting']);
        Route::get('/info', [UserController::class, 'info']);
    });

    Route::group(['prefix' => 'service_requests'], function () {
        Route::post('/create', [ServiceRequestController::class, 'create']);
        Route::post('/cancel', [ServiceRequestController::class, 'cancel']);
        Route::post('/delete', [ServiceRequestController::class, 'delete']);
        Route::post('/list', [ServiceRequestController::class, 'list']);
        Route::post('/info', [ServiceRequestController::class, 'info']);
    });

    Route::group(['prefix' => 'work_tasks'], function () {
        Route::post('/list', [WorkTaskController::class, 'list']);
        Route::get('/info', [WorkTaskController::class, 'info']);
        Route::post('/confrim_stage_work_status', [WorkTaskController::class, 'confrimStageWorkStatus']);

        Route::post('/cancel', [WorkTaskController::class, 'cancel']);
    });

    Route::group(['prefix' => 'work_task_price_changes'], function () {
        Route::post('/create', [WorkTaskPriceChangeController::class, 'create']);
        Route::post('/list', [WorkTaskPriceChangeController::class, 'list']);
        Route::post('/cancel', [WorkTaskPriceChangeController::class, 'cancel']);
        Route::post('/reject', [WorkTaskPriceChangeController::class, 'reject']);
        Route::post('/approve', [WorkTaskPriceChangeController::class, 'approve']);
    });

    Route::group(['prefix' => 'work_task_files'], function () {
        Route::post('/download', [WorkTaskController::class, 'download']);

        Route::post('/list', [WorkTaskFileController::class, 'list']);
        Route::get('/info', [WorkTaskFileController::class, 'info']);
    });

    Route::group(['prefix' => 'work_task_file_change_requests'], function () {
        Route::post('/list', [WorkTaskFileChangeRequestController::class, 'list']);
        Route::post('/create', [WorkTaskFileChangeRequestController::class, 'create']);
        Route::post('/update', [WorkTaskFileChangeRequestController::class, 'update']);
    });

    Route::group(['prefix' => 'stripe'], function () {
        // stripe connect
        Route::post('/create_connect_account_session', [StripeController::class, 'createConnectAccountSession']);
        Route::post('/create_connect_account_link', [StripeController::class, 'createConnectAccountLink']);
        Route::get('/create_connect_account_refresh', [StripeController::class, 'createConnectAccountRefresh'])
            ->name('create_connect_account_refresh')
            ->withoutMiddleware('auth:sanctum');
        Route::get('/create_connect_account_return', [StripeController::class, 'createConnectAccountReturn'])
            ->name('create_connect_account_return')
            ->withoutMiddleware('auth:sanctum');

        // stripe checkout
        Route::post('/create_payment_link', [StripeController::class, 'createPaymentLink']);
        Route::get('/checkout', [StripeController::class, 'checkout']);
        Route::get('/checkout_success', [StripeController::class, 'checkoutSuccess'])->name('checkout-success');
        Route::get('/checkout_cancel', [StripeController::class, 'checkoutCancel'])->name('checkout-cancel');

        Route::Get('/payment_intent', [StripeController::class, 'paymentIntent']);

        // work_task
        Route::Post('/create_work_task_checkout_session', [WorkTaskPayController::class, 'createWorkTaskCheckoutSession']);
        Route::Post('/pre_calc', [WorkTaskPayController::class, 'preCalc']);

        // product
        Route::Post('/create_product_checkout_session', [StripeController::class, 'createProductCheckoutSession']);

        // checkout session callback
        Route::Post('/callback_checkout_session', [StripeController::class, 'callbackCheckoutSession']);

        // embedded
        Route::Post('/create_embedded_account_sessions', [StripeController::class, 'createEmbeddedAccountSessions']);
    });

    Route::group(['prefix' => 'orders'], function () {
        Route::Post('/cancel', [OrderController::class, 'cancel']);
        Route::Post('/continue', [OrderController::class, 'continue']);
    });

    Route::group(['prefix' => 'artist_rec_reviews'], function () {
        Route::Post('/create_work_task_review', [ArtistRecReviewsController::class, 'workTaskReview']);
        Route::Post('/delete_work_task_review', [ArtistRecReviewsController::class, 'deleteWorkTaskReview']);
        Route::Get('/work_task_review_info', [ArtistRecReviewsController::class, 'workTaskReviewInfo']);
    });

    Route::group(['prefix' => 'projects'], function () {
        Route::Post('/list', [ProjectController::class, 'list']);
        Route::Post('/create', [ProjectController::class, 'create']);
        Route::Post('/update', [ProjectController::class, 'update']);
        Route::Post('/delete', [ProjectController::class, 'delete']);
        Route::Get('/info', [ProjectController::class, 'info']);
        Route::Get('/meta', [ProjectController::class, 'meta']);
    });

    Route::group(['prefix' => 'project_requests'], function () {
        Route::Post('/choose', [ProjectRequestController::class, 'chooseAndCreateWorkTask']);
        Route::Post('/requested_list', [ProjectRequestController::class, 'requestedList']);
        Route::Post('/choosed_list', [ProjectRequestController::class, 'choosedList']);
    });

    Route::group(['prefix' => 'project_manager'], function () {
        Route::Post('/list', [ProjectManagerController::class, 'list']);
    });

    Route::group(['prefix' => 'apply_artist'], function () {
        Route::Post('/check_code', [ArtistApplicationController::class, 'checkCode']);
        Route::Post('/apply_by_code', [ArtistApplicationController::class, 'applyByCode']);
        Route::Post('/applicant_artist', [ArtistApplicationController::class, 'applicantArtist']);
        Route::Post('/cancel_applicant_artist', [ArtistApplicationController::class, 'cancelApplicantArtist']);
        Route::Get('/applicant_artist_list', [ArtistApplicationController::class, 'applicantArtistList']);
    });

    Route::group(['prefix' => 'product_order'], function () {
        Route::Post('/create', [ProductOrderController::class, 'create']);
        Route::Post('/list', [ProductOrderController::class, 'list']);
        Route::Post('/delete', [ProductOrderController::class, 'delete']);
    });

    Route::group(['prefix' => 'product_order_item'], function () {
        Route::Post('/list', [ProductOrderItemController::class, 'list']);
        Route::Post('/download_file', [ProductOrderItemController::class, 'downloadFile']);
    });

    Route::group(['prefix' => 'translate'], function () {
        Route::Post('/create', [TranslateController::class, 'create']);
        Route::Post('/pre_calc_batteries', [TranslateController::class, 'preCalcBatteries']);
        Route::Post('/cancel_human_translate', [TranslateController::class, 'cancelHumanTranslate']);
        Route::Post('/create_ai', [TranslateController::class, 'createAiTranslate']);
    });

    Route::group(['prefix' => 'bookmark'], function () {
        Route::post('/save_artist', [BookmarkController::class, 'saveArtist']);
        Route::post('/save_artwork', [BookmarkController::class, 'saveArtwork']);
        Route::post('/save_service', [BookmarkController::class, 'saveService']);
        Route::post('/save_product', [BookmarkController::class, 'saveProduct']);

        Route::get('/saved_artists', [BookmarkController::class, 'savedArtists']);
        Route::get('/saved_artworks', [BookmarkController::class, 'savedArtworks']);
        Route::get('/saved_services', [BookmarkController::class, 'savedServices']);
        Route::get('/saved_products', [BookmarkController::class, 'savedProducts']);

    });

    Route::group(['prefix' => 'chats'], function () {
        Route::Post('/list', [ChatController::class, 'list']);
        Route::Post('/create', [ChatController::class, 'createOrJoin']);
        Route::Post('/new_chats', [ChatController::class, 'newChats']);
        Route::post('update_settings', [ChatController::class, 'updateSettings']);
        Route::Post('settings', [ChatController::class, 'getSettings']);
    });

    Route::group(['prefix' => 'chat_messages'], function () {
        Route::Post('/send', [ChatMessageController::class, 'send']);
        Route::Post('/translate_message', [ChatMessageController::class, 'translateMessage']);
        Route::Post('/list', [ChatMessageController::class, 'list']);
        Route::Post('/enter_chat', [ChatMessageController::class, 'enterChat']);
        Route::Post('/history_messages', [ChatMessageController::class, 'historyMessages']);
        Route::Post('/new_messages', [ChatMessageController::class, 'newMessages']);
        Route::Get('/download_attachment', [ChatMessageController::class, 'downloadAttachment'])
            ->withoutMiddleware('auth:sanctum');
        Route::Post('/undo', [ChatMessageController::class, 'undo']);
        Route::Post('/update_last_read_cursor', [ChatMessageController::class, 'updateLastReadCursor']);
    });

    Route::group(['prefix' => 'groups'], function () {
        Route::Post('/list', [GroupController::class, 'list']);
        Route::Post('/info', [GroupController::class, 'info']);
        Route::Post('/new_groups', [GroupController::class, 'newGroups']);
        Route::post('update_settings', [GroupController::class, 'updateSettings']);
        Route::Post('settings', [GroupController::class, 'getSettings']);
        Route::Post('request_admin_join', [GroupController::class, 'requestAdminJoin']);
        Route::Post('open', [GroupController::class, 'openGroup']);
    });

    Route::group(['prefix' => 'group_messages'], function () {
        Route::Post('/send', [GroupMessageController::class, 'send']);
        Route::Post('/translate_message', [GroupMessageController::class, 'translateMessage']);
        Route::Post('/list', [GroupMessageController::class, 'list']);
        Route::Post('/enter_group', [GroupMessageController::class, 'enterGroup']);
        Route::Post('/history_messages', [GroupMessageController::class, 'historyMessages']);
        Route::Post('/new_messages', [GroupMessageController::class, 'newMessages']);
        Route::Get('/download_attachment', [GroupMessageController::class, 'downloadAttachment'])
            ->withoutMiddleware('auth:sanctum');
        Route::Post('/undo', [GroupMessageController::class, 'undo']);
        Route::Post('/update_last_read_cursor', [GroupMessageController::class, 'updateLastReadCursor']);
    });

    Route::group(['prefix' => 'group_reminders'], function () {
        Route::Post('/create', [GroupReminderController::class, 'create']);
    });

    Route::group(['prefix' => 'notifications'], function () {
        Route::Post('/list', [NotificationController::class, 'list']);
        Route::Post('/has_unread', [NotificationController::class, 'hasUnreadNotification']);
        Route::Post('/has_new', [NotificationController::class, 'hasNewNotification']);
        Route::Post('/mark_as_read', [NotificationController::class, 'markAsRead']);
    });

    Route::group(['prefix' => 'wallets'], function () {
        Route::Post('/list', [WalletController::class, 'list']);
    });

    Route::group(['prefix' => 'wallet_transactions'], function () {
        Route::Post('/list', [WalletTransactionController::class, 'list']);
    });

    Route::group(['prefix' => 'wallet_withdraws'], function () {
        Route::Post('/create', [WalletWithdrawController::class, 'create']);
        Route::Post('/list', [WalletWithdrawController::class, 'list']);
        Route::Post('/cancel', [WalletWithdrawController::class, 'cancel']);
    });

    Route::group(['prefix' => 'wallets_histories'], function () {
        Route::Post('/list', [WalletHistoryController::class, 'list']);
    });

    Route::group(['prefix' => 'withdraw_accounts'], function () {
        Route::Post('/list', [UserWithdrawAccountController::class, 'list']);
        Route::Post('/create', [UserWithdrawAccountController::class, 'create']);
        Route::Post('/delete', [UserWithdrawAccountController::class, 'delete']);
        Route::Post('/send_email_verify_code', [UserWithdrawAccountController::class, 'sendEmailVerifyCode']);
    });

    Route::group(['prefix' => 'profile'], function () {
        Route::post('/job_count', [ProfileController::class, 'jobCount']);
    });
});
