{"apps": [{"name": "[pipipen-test] [queue-worker] [translate]", "script": "artisan", "args": "queue:work --sleep=3 --tries=3 --queue=translate", "exec_interpreter": "php", "exec_mode": "fork", "watch": false, "autorestart": true, "max_restarts": 10, "restart_delay": 5000, "cwd": "/home/<USER>/codes/test/pipipen-api"}, {"name": "[pipipen-test] [queue-worker] [compress-image]", "script": "artisan", "args": "queue:work --sleep=3 --tries=3 --queue=compress_image", "exec_interpreter": "php", "exec_mode": "fork", "watch": false, "autorestart": true, "max_restarts": 10, "restart_delay": 5000, "cwd": "/home/<USER>/codes/test/pipipen-api"}, {"name": "[pipipen-test] [queue-worker] [default]", "script": "artisan", "args": "queue:work --sleep=3 --tries=3 --queue=default", "exec_interpreter": "php", "exec_mode": "fork", "watch": false, "autorestart": true, "max_restarts": 10, "restart_delay": 5000, "cwd": "/home/<USER>/codes/test/pipipen-api"}, {"name": "[pipipen-test] [schedule-worker]", "script": "artisan", "args": "schedule:work", "exec_interpreter": "php", "exec_mode": "fork", "watch": false, "autorestart": true, "max_restarts": 10, "restart_delay": 5000, "cwd": "/home/<USER>/codes/test/pipipen-api"}, {"name": "[pipipen-test] [reverb-worker]", "script": "artisan", "args": "reverb:start --debug", "exec_interpreter": "php", "exec_mode": "fork", "watch": false, "autorestart": true, "max_restarts": 10, "restart_delay": 5000, "cwd": "/home/<USER>/codes/test/pipipen-api"}]}