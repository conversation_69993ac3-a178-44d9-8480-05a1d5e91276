{"apps": [{"name": "[pipipen-api] [queue-worker] [translate]", "script": "artisan", "args": "queue:work --sleep=3 --tries=3 --queue=translate", "exec_interpreter": "php", "exec_mode": "fork", "watch": false, "autorestart": true, "max_restarts": 10, "restart_delay": 5000, "cwd": "/home/<USER>/codes/pipipen-api"}, {"name": "[pipipen-api] [queue-worker] [compress-image]", "script": "artisan", "args": "queue:work --sleep=3 --tries=3 --queue=compress_image", "exec_interpreter": "php", "exec_mode": "fork", "watch": false, "autorestart": true, "max_restarts": 10, "restart_delay": 5000, "cwd": "/home/<USER>/codes/pipipen-api"}, {"name": "[pipipen-api] [queue-worker] [default]", "script": "artisan", "args": "queue:work --sleep=3 --tries=3 --queue=default", "exec_interpreter": "php", "exec_mode": "fork", "watch": false, "autorestart": true, "max_restarts": 10, "restart_delay": 5000, "cwd": "/home/<USER>/codes/pipipen-api"}, {"name": "[pipipen-api] [schedule-worker]", "script": "artisan", "args": "schedule:work", "exec_interpreter": "php", "exec_mode": "fork", "watch": false, "autorestart": true, "max_restarts": 10, "restart_delay": 5000, "cwd": "/home/<USER>/codes/pipipen-api"}, {"name": "[pipipen-api] [reverb-worker]", "script": "artisan", "args": "reverb:start --debug", "exec_interpreter": "php", "exec_mode": "fork", "watch": false, "autorestart": true, "max_restarts": 10, "restart_delay": 5000, "cwd": "/home/<USER>/codes/pipipen-api"}]}