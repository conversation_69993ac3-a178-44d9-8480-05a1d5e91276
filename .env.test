APP_NAME=PIPIPEN-API
APP_ENV=test
APP_KEY=base64:SS8qAIB2R7uevKzTzyZI+oFKzDDccKABinIe0UPpnpc=
APP_DEBUG=true
APP_URL=http://*************:8000

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=*************
DB_PORT=3306
DB_DATABASE=pipipen_api
DB_USERNAME=root
DB_PASSWORD=eldasolar

# DB_CONNECTION=mysql
# DB_HOST=127.0.0.1
# DB_PORT=3307
# DB_DATABASE=pipipen_api
# DB_USERNAME=root
# DB_PASSWORD="Eld@T3stS3rv3r#SGP"

BROADCAST_DRIVER=pusher
CACHE_STORE=file
FILESYSTEM_DISK=r2
QUEUE_CONNECTION=database
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=0.0.0.0

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=mailgun

MAIL_HOST=mail.drdaji.com
MAIL_PORT=465
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=solarstudio123
MAIL_ENCRYPTION=null

MAILGUN_DOMAIN=mg.eldasolar.com
MAILGUN_SECRET=**************************************************
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="Kono Dio Da"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

CLOUDFLARE_R2_ACCESS_KEY_ID=3aa57b424c7db75d762d501984ed6455
CLOUDFLARE_R2_SECRET_ACCESS_KEY=2fd9c07a5f8a666aef385c432ffe2b0b9533bd9be1ab55f11221397c84127654
CLOUDFLARE_R2_BUCKET=pipipen
CLOUDFLARE_R2_BUCKET_TEST=test-public
CLOUDFLARE_R2_BUCKET_PRIVATE=test-private
CLOUDFLARE_R2_ENDPOINT=https://86c796930660b66cb5f4b26ef7f444c5.r2.cloudflarestorage.com
CLOUDFLARE_R2_URL=https://cdn.pipipen.com/
CLOUDFLARE_R2_URL_TEST=https://cdn.pipipen.com/
CLOUDFLARE_R2_URL_PRIVATE=https://cdn.pipipen.com/


PUSHER_APP_ID=*******
PUSHER_APP_KEY=0a912ab23e09bfefc652
PUSHER_APP_SECRET=3a2ceb5b52074f32fa61
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=us2

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

PAYPAL_SANDBOX_CLIENT_ID=AeDLBzwPUV0CZ_I7pk0Et_vMPwRrJmqsA2Db3kf61-4eQIIuUDCuA_9Ye2Dka9sA8J_OryEmUO9-Katr
PAYPAL_SANDBOX_CLIENT_SECRET=EAwi-XwGxG4HOrpHFMV4ULa72H-8Bjvzz-qVAxwEA0LLACR5eSQLyXtisgYGSzZJ9lIWe32d4Ae5Ij-X

STRIPE_KEY=pk_test_51RVqPB4MAwRbkMt3LhgjskLj5COBxwmJbPx9Z4xtkaHbEmjov2zPjmuQB4phaee8Ib2cIDxdMARdoQWoyO7uumrZ00x0rZ5w4a
STRIPE_SECRET=sk_test_51RVqPB4MAwRbkMt3ueEufjCpf6Pjx24B3HSQcay24oVFUILlGRboNt4N1wUTem6U2vkFE9fjeeFEuaGkatpa8aik00h7OEBZ2Z
STRIPE_WEBHOOK_SECRET=whsec_48c7d69e8f1b6d319542bd050e947c8cd2536a7fac8857ddba503c366b67634a
CASHIER_LOGGER=stack

OPENAI_API_KEY=********************************************************
OPENAI_ORGANIZATION=
OPENAI_PROXY=http://***********:7897


ALIPAY_APP_ID=9021000139691397
ALIPAY_APP_SECRET="MIIEpAIBAAKCAQEAr655kmbezlxx9VCjdR4p8Ex/dChDlPuaNMgpWeVzpHW8Qa0UIUZd787BD7iIw8Cs4HNqQRgwXdB0yum5SN16R3jVd92Lh+lz/DjioNQv2ms9GbVjXmOso3+SA68DUiZQqQMgVS/vjmI+BG1DqfNIVUGMhi1ZHlKTnGizgfQmpendlMNvKSljX8C7EuJdtOQHxeotW0lia/H3a+kKxLnJ2x4U9ETg2I7jHGPEYA2y4IhbtrMcT+4GMJlb4rBuxbFxH0JtknPTDWT4okUDsQdlN+pGvuSnd/DCiCZyEUBhcBqekzVXafK9rh2IHxORgIgzvCvIeoXrevfWtDsXVPTuSwIDAQABAoIBAQCrpww+EaktdIIcMTgk3N4ZXoVB132A+f0ICXLt1RWS4T6Z5STABmMORrO4SpPGwGGpxNtS0vGfGGilQoy5Nz2cy+G3ho4os9+R13sHrYs8HH/MuMBon3c1IRZn5v6Vq74wBU/4LCgitLbn2GK+kWhhdPR6vaBP0ltL/bk3Bnkkrafe/tAmyIR0gXXHol+C9jT73PKlke/1/GnyWFQ+LlMtoVrMSSTLRxSA9Q2sUsmeJEZ9rGK7zEZ6LihCFN4jDAGQw6iH1DHbaaaqwjsdSvMqhIHANUwTGo48grVC5UxWWTO3gNiCTaoQrevSxXhMElrgl502OKEqVWw6a2tdKRShAoGBAN562lfjzcWoA6V/sBjnJSPfJ738wXsgzokMJdZdMdGZk42k3Q+T3fAjX6gUVzD7g7zoS+hrPy7FAacEgR8ILteh/bkCllLcMsaMqQMX5KGnKnSZI3CzzWkYlHeKgX9Md+ZhArrSixoJSsAfejoleM8FwG9sy2mADHaq67b/J0eTAoGBAMomlstF3e8UEurjqDGLaf3W30onq3BAyEu7wrLY4nfeT8xMiQkVwbN6cfAKsIr1RA5c+tnDsktno+Hd9uSAZ/KAwfQfTFK4zlYck/24vBi9AdEW1Jo9Xf4rBNScsRXz/aOSfQENr3CKp0AXhGFN43YA6/nv0zeRXXrzYQ5HywFpAoGATXsrn4BOoY+p9XFKuuGFJcEJ7jigiWkbhM8ePlGmkACUSrgn5MzydjdbkQXEd+KgNqIQDZmMH5WsxaXj8hK/fYmE+3HTOHhHlq5WDiW8EjBNsN3EYfO8jeVpj1hFUTgoaatPcBDzMNYbml1cC/QQ5shyBFw3Wz6lGI7eGJUz8NECgYEAjF+xlOQmUe+Jl2yRDh52cpt0IKVdaizCKbzvXYw37u8EUKTQ/+sIwB4F4p85qrTYFnVAiFlZhP0qEx00ZcY46G+6v0/Aiyzjs25S4fwXOcJ98Xi+F4EHtQigaReJ7OmqNpzxivq62haeP2ZVOdCBZB8NvlhoPo0F8evxeJ4n/QkCgYAYfChQM7AiDgcd5Ggu5CiGMo0MST8MU+gi6ZWq8uq8cl3+lkT0udVmIuZkv1oTYJOC6J3bMBju+uNPhZGdkJ5fGgXY9808g7fruLeGr/wqn7rNoDT0dbAFku4OisdzCrBxF8r3hO56hhgTxZW4ynImTk+0qIygZ4LJ6nQDxc6iGA=="
ALIPAY_APP_PUBLIC_CERT_PATH=/home/<USER>/codes/pipipen-api/appPublicCert.crt
ALIPAY_ALIPAY_PUBLIC_CERT_PATH=/home/<USER>/codes/pipipen-api/alipayPublicCert.crt
ALIPAY_ALIPAY_ROOT_CERT_PATH=/home/<USER>/codes/pipipen-api/alipayRootCert.crt