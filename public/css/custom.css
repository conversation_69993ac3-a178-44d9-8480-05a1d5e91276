.toast-top-center {
  top: 24px;
}

.d-none-ni{
  display: none;
}
.sort-handle{
  display: none;
  cursor:grab;
}

.nav.nav-pills-css .nav-link.active{
  background:white;
  -webkit-box-shadow: 5px 5px 5px -5px rgba(166,166,166,0.5);
  -moz-box-shadow: 5px 5px 5px -5px rgba(166,166,166,0.5);
  box-shadow: 5px 5px 5px -5px rgba(166,166,166,0.5);
}

.chat-unread-number{
  display: flex;
  justify-content:center;
  align-items:center;
  width: 20px;
  height: 20px;
  font-size:0.75rem;
}

/* Rating Star Widgets Style */
.rating-stars ul {
  list-style-type:none;
  padding:0;
  -moz-user-select:none;
  -webkit-user-select:none;
}
.rating-stars ul > li.star {
  display:inline-block;
  cursor: pointer;
}

/* Idle State of the stars */
.rating-stars ul > li.star > i.fa {
  color:#ccc; /* Color on idle state */
}
.rating-stars i.fa {
  color:#ccc; /* Color on idle state */
}

/* Hover state of the stars */
.rating-stars ul > li.star.hover > i.fa {
  color:#add5ff;
}

/* Selected state of the stars */
.rating-stars ul > li.star.selected > i.fa {
  color:#1A73E8;
}
.rating-stars i.fa.selected {
  color:#1A73E8;
}

.hidden{
  visibility: hidden;
}
.cursor-grab{
  cursor:grab;
}
.white-space-pre-wrap{
  white-space: pre-wrap
}
.center-flex{
  display: flex;
  align-items: center;
  justify-content: center;
}

.sort-mask{
  display: none;
  opacity:0.8;
  transition-duration: 0.3s;
}
.edit-avatar-icon{
  position: absolute;
  bottom: 0;
  right: 0;
  font-size: 1.5rem;
  transition: 0.7s ease-in;
}
.edit-avatar-icon:hover{
  text-shadow: rgba(26, 115, 232, 0.5) 1px 1px 3px;
}
.edit-cover-icon{
  position: absolute;
  top:0;
  right: 0;
  font-size: 1.5rem;
  transition: 0.7s ease-in;
}
.edit-cover-icon:hover{
  text-shadow: rgba(26, 115, 232, 0.5) 1px 1px 3px;
}
.aspect-ratio-1{
  aspect-ratio: 1/1;
  justify-content: center;
  align-items: center;
  object-position: top;
  object-fit: cover;
  overflow: hidden;
}
.aspect-ratio-16-9{
  width: 100%;
  aspect-ratio: 16/9;
  justify-content: center;
  align-items: center;
  object-position: top;
  object-fit: cover;
  overflow: hidden;
}
.chat-input{
  resize: none;
  overflow: hidden;
}
.modal{
  z-index: 9999;
}
.resize-none{
  resize: none!important;
}

.nav-pills .nav-link{
  cursor: pointer;
  /* transform:translate3d(0px, 0px, 0px); */
  transition :.5s ease;
}
.nav-pills .nav-link:hover{
  color: #1A73E8 !important;
}

.nav.nav-pills-css .nav-link.active{
  background:white;
  -webkit-box-shadow: 5px 5px 5px -5px rgba(166,166,166,0.5);
  -moz-box-shadow: 5px 5px 5px -5px rgba(166,166,166,0.5);
  box-shadow: 5px 5px 5px -5px rgba(166,166,166,0.5);
}
/* .select-work{
  transition: 0.2s;
} */
.select-work.selected{
  border: 2px solid #1A73E8 !important;
}



/* .sort-mask:hover{
  opacity:0.8;
} */
/* .sort-mask:hover ~ .sort-mask{
  opacity:0.8;
} */






/* conetents custom css */
